<?php if (!empty($breakingNews)): ?>
<div class="breaking-news-container bg-danger text-white py-2">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-auto">
                <span class="badge bg-light text-danger fw-bold px-3 py-2">
                    <i class="fas fa-bolt me-1"></i>ब्रेकिंग न्यूज़
                </span>
            </div>
            <div class="col">
                <div class="breaking-news-ticker">
                    <div class="breaking-news-content">
                        <?php foreach ($breakingNews as $index => $news): ?>
                            <span class="breaking-news-item <?= $index === 0 ? 'active' : '' ?>">
                                <i class="fas fa-circle me-2" style="font-size: 0.5rem;"></i>
                                <a href="<?= base_url('news/' . $news['slug']) ?>" class="text-white text-decoration-none">
                                    <?= esc($news['title']) ?>
                                </a>
                            </span>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.breaking-news-container {
    position: relative;
    overflow: hidden;
    border-bottom: 3px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.breaking-news-ticker {
    overflow: hidden;
    white-space: nowrap;
    position: relative;
    height: 30px;
    line-height: 30px;
}

.breaking-news-content {
    display: inline-block;
    animation: scroll-left 60s linear infinite;
    white-space: nowrap;
}

.breaking-news-item {
    display: inline-block;
    margin-right: 50px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.breaking-news-item:hover {
    text-shadow: 0 0 5px rgba(255,255,255,0.8);
}

.breaking-news-item a:hover {
    text-decoration: underline !important;
}

@keyframes scroll-left {
    0% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(-100%);
    }
}

/* Pause animation on hover */
.breaking-news-ticker:hover .breaking-news-content {
    animation-play-state: paused;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .breaking-news-content {
        animation-duration: 45s;
    }
    
    .breaking-news-item {
        margin-right: 30px;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .breaking-news-ticker {
        height: 25px;
        line-height: 25px;
    }
    
    .breaking-news-item {
        font-size: 0.8rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Optional: Add click tracking for breaking news
    const breakingNewsItems = document.querySelectorAll('.breaking-news-item a');
    breakingNewsItems.forEach(item => {
        item.addEventListener('click', function() {
            // You can add analytics tracking here
            console.log('Breaking news clicked:', this.textContent.trim());
        });
    });
    
    // Optional: Auto-refresh breaking news every 5 minutes
    setInterval(function() {
        // You can implement AJAX refresh here if needed
        console.log('Breaking news refresh check');
    }, 300000); // 5 minutes
});
</script>
<?php endif; ?>
