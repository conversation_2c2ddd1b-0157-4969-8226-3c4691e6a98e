<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-edit me-2"></i>Edit Poll</h2>
                <a href="<?= base_url('admin/polls') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Polls
                </a>
            </div>

            <!-- Error Messages -->
            <?php if (session()->getFlashdata('errors')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <h6>Please fix the following errors:</h6>
                    <ul class="mb-0">
                        <?php foreach (session()->getFlashdata('errors') as $error): ?>
                            <li><?= esc($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Poll Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-poll me-2"></i>Poll Details
                    </h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('admin/polls/update/' . $poll['id']) ?>" method="post">
                        <?= csrf_field() ?>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <!-- Title -->
                                <div class="mb-3">
                                    <label for="title" class="form-label">
                                        <i class="fas fa-heading me-2"></i>Poll Title (सवाल) *
                                    </label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="<?= old('title', $poll['title']) ?>" required maxlength="500"
                                           placeholder="Enter poll question in Hindi">
                                    <div class="form-text">Maximum 500 characters</div>
                                </div>

                                <!-- Description -->
                                <div class="mb-3">
                                    <label for="description" class="form-label">
                                        <i class="fas fa-align-left me-2"></i>Description (विवरण)
                                    </label>
                                    <textarea class="form-control" id="description" name="description" rows="3"
                                              placeholder="Optional description or context for the poll"><?= old('description', $poll['description']) ?></textarea>
                                    <div class="form-text">Optional field for additional context</div>
                                </div>

                                <!-- Poll Options -->
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-list me-2"></i>Poll Options (विकल्प) *
                                    </label>
                                    <div id="pollOptions">
                                        <?php if (!empty($poll['options'])): ?>
                                            <?php foreach ($poll['options'] as $index => $option): ?>
                                                <div class="input-group mb-2">
                                                    <span class="input-group-text"><?= $index + 1 ?></span>
                                                    <input type="text" class="form-control" name="options[]" 
                                                           value="<?= esc($option['option_text']) ?>"
                                                           placeholder="Enter option <?= $index + 1 ?>" required>
                                                    <button type="button" class="btn btn-outline-danger" onclick="removeOption(this)" <?= $index < 2 ? 'disabled' : '' ?>>
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                    <div class="input-group-text">
                                                        <small class="text-muted"><?= number_format($option['vote_count']) ?> votes</small>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <div class="input-group mb-2">
                                                <span class="input-group-text">1</span>
                                                <input type="text" class="form-control" name="options[]" 
                                                       placeholder="Enter option 1" required>
                                                <button type="button" class="btn btn-outline-danger" onclick="removeOption(this)" disabled>
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                            <div class="input-group mb-2">
                                                <span class="input-group-text">2</span>
                                                <input type="text" class="form-control" name="options[]" 
                                                       placeholder="Enter option 2" required>
                                                <button type="button" class="btn btn-outline-danger" onclick="removeOption(this)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="addOption()">
                                        <i class="fas fa-plus me-2"></i>Add Option
                                    </button>
                                    <div class="form-text">Minimum 2 options required. Note: Editing options will reset vote counts.</div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- Status -->
                                <div class="mb-3">
                                    <label for="status" class="form-label">
                                        <i class="fas fa-toggle-on me-2"></i>Status *
                                    </label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="">Select Status</option>
                                        <option value="active" <?= old('status', $poll['status']) == 'active' ? 'selected' : '' ?>>Active</option>
                                        <option value="inactive" <?= old('status', $poll['status']) == 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                        <option value="closed" <?= old('status', $poll['status']) == 'closed' ? 'selected' : '' ?>>Closed</option>
                                    </select>
                                </div>

                                <!-- Multiple Choice -->
                                <div class="mb-3">
                                    <label for="multiple_choice" class="form-label">
                                        <i class="fas fa-check-square me-2"></i>Choice Type *
                                    </label>
                                    <select class="form-select" id="multiple_choice" name="multiple_choice" required>
                                        <option value="">Select Type</option>
                                        <option value="0" <?= old('multiple_choice', $poll['multiple_choice']) == '0' ? 'selected' : '' ?>>Single Choice</option>
                                        <option value="1" <?= old('multiple_choice', $poll['multiple_choice']) == '1' ? 'selected' : '' ?>>Multiple Choice</option>
                                    </select>
                                </div>

                                <!-- Show Results -->
                                <div class="mb-3">
                                    <label for="show_results" class="form-label">
                                        <i class="fas fa-chart-bar me-2"></i>Show Results *
                                    </label>
                                    <select class="form-select" id="show_results" name="show_results" required>
                                        <option value="">Select Option</option>
                                        <option value="after_vote" <?= old('show_results', $poll['show_results']) == 'after_vote' ? 'selected' : '' ?>>After Vote</option>
                                        <option value="always" <?= old('show_results', $poll['show_results']) == 'always' ? 'selected' : '' ?>>Always</option>
                                        <option value="never" <?= old('show_results', $poll['show_results']) == 'never' ? 'selected' : '' ?>>Never</option>
                                    </select>
                                </div>

                                <!-- Start Date -->
                                <div class="mb-3">
                                    <label for="start_date" class="form-label">
                                        <i class="fas fa-calendar-alt me-2"></i>Start Date
                                    </label>
                                    <?php 
                                    $startDate = old('start_date', $poll['start_date']);
                                    if ($startDate) {
                                        $startDate = date('Y-m-d\TH:i', strtotime($startDate));
                                    }
                                    ?>
                                    <input type="datetime-local" class="form-control" id="start_date" name="start_date" 
                                           value="<?= $startDate ?>">
                                    <div class="form-text">Leave empty for immediate start</div>
                                </div>

                                <!-- End Date -->
                                <div class="mb-3">
                                    <label for="end_date" class="form-label">
                                        <i class="fas fa-calendar-alt me-2"></i>End Date
                                    </label>
                                    <?php 
                                    $endDate = old('end_date', $poll['end_date']);
                                    if ($endDate) {
                                        $endDate = date('Y-m-d\TH:i', strtotime($endDate));
                                    }
                                    ?>
                                    <input type="datetime-local" class="form-control" id="end_date" name="end_date" 
                                           value="<?= $endDate ?>">
                                    <div class="form-text">Leave empty for no expiry</div>
                                </div>

                                <!-- Poll Info -->
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-info-circle me-2"></i>Poll Info
                                    </label>
                                    <div class="card bg-light">
                                        <div class="card-body p-2">
                                            <small>
                                                <strong>ID:</strong> <?= $poll['id'] ?><br>
                                                <strong>Total Votes:</strong> <?= number_format($poll['total_votes']) ?><br>
                                                <strong>Created:</strong> <?= date('d M Y, H:i', strtotime($poll['created_at'])) ?><br>
                                                <strong>Updated:</strong> <?= date('d M Y, H:i', strtotime($poll['updated_at'])) ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <a href="<?= base_url('admin/polls') ?>" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Update Poll
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
let optionCount = <?= count($poll['options'] ?? []) ?> || 2;

function addOption() {
    optionCount++;
    const optionsContainer = document.getElementById('pollOptions');
    const newOption = document.createElement('div');
    newOption.className = 'input-group mb-2';
    newOption.innerHTML = `
        <span class="input-group-text">${optionCount}</span>
        <input type="text" class="form-control" name="options[]" 
               placeholder="Enter option ${optionCount}" required>
        <button type="button" class="btn btn-outline-danger" onclick="removeOption(this)">
            <i class="fas fa-trash"></i>
        </button>
    `;
    optionsContainer.appendChild(newOption);
    updateOptionNumbers();
}

function removeOption(button) {
    const optionsContainer = document.getElementById('pollOptions');
    if (optionsContainer.children.length > 2) {
        button.parentElement.remove();
        updateOptionNumbers();
    }
}

function updateOptionNumbers() {
    const options = document.querySelectorAll('#pollOptions .input-group');
    options.forEach((option, index) => {
        const numberSpan = option.querySelector('.input-group-text');
        const input = option.querySelector('input');
        const deleteBtn = option.querySelector('.btn-outline-danger');
        
        numberSpan.textContent = index + 1;
        input.placeholder = `Enter option ${index + 1}`;
        
        // Disable delete button for first two options
        if (deleteBtn) {
            deleteBtn.disabled = index < 2;
        }
    });
    optionCount = options.length;
}

// Set minimum datetime and handle date changes
document.addEventListener('DOMContentLoaded', function() {
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    
    // Update end date minimum when start date changes
    startDateInput.addEventListener('change', function() {
        if (this.value) {
            endDateInput.min = this.value;
        }
    });
    
    // Set initial minimum for end date if start date has value
    if (startDateInput.value) {
        endDateInput.min = startDateInput.value;
    }
});
</script>
<?= $this->endSection() ?>
