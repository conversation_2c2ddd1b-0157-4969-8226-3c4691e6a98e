<?= $this->extend('frontend/layout') ?>

<?= $this->section('content') ?>

<div class="container">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>">होम</a></li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('category/' . $news['category_slug']) ?>">
                            <?= esc($news['category_name']) ?>
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">समाचार</li>
                </ol>
            </nav>

            <!-- Content Top Ads -->
            <?= view('components/ad_display', [
                'ads' => $contentAds,
                'position' => 'content_top',
                'showLabel' => true
            ]) ?>

            <!-- News Article -->
            <article class="mb-5">
                <header class="mb-4">
                    <div class="mb-3">
                        <span class="badge bg-primary fs-6"><?= esc($news['category_name']) ?></span>
                        <?php if ($news['featured']): ?>
                            <span class="badge bg-warning text-dark ms-2">
                                <i class="fas fa-star me-1"></i>मुख्य समाचार
                            </span>
                        <?php endif; ?>
                    </div>

                    <h1 class="display-5 fw-bold mb-3"><?= esc($news['title']) ?></h1>

                    <div class="news-meta mb-4">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <p class="mb-0">
                                    <i class="fas fa-user me-2"></i>
                                    <strong><?= esc($news['author_name']) ?></strong>
                                </p>
                                <p class="mb-0">
                                    <i class="fas fa-clock me-2"></i>
                                    <?= date('d M Y, H:i', strtotime($news['created_at'])) ?>
                                    <?php if ($news['updated_at'] !== $news['created_at']): ?>
                                        <span class="text-muted ms-2">
                                            (अपडेट: <?= date('d M Y, H:i', strtotime($news['updated_at'])) ?>)
                                        </span>
                                    <?php endif; ?>
                                </p>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <p class="mb-0">
                                    <i class="fas fa-eye me-2"></i>
                                    <?= number_format($news['views']) ?> बार देखा गया
                                </p>
                                <div class="mt-2">
                                    <button class="btn btn-outline-primary btn-sm me-2" onclick="shareNews()">
                                        <i class="fas fa-share-alt me-1"></i>शेयर करें
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="printNews()">
                                        <i class="fas fa-print me-1"></i>प्रिंट करें
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </header>

                <!-- Featured Media -->
                <?php if (($news['media_type'] ?? 'image') === 'video' && $news['youtube_id']): ?>
                    <!-- YouTube Video -->
                    <div class="mb-4">
                        <div class="ratio ratio-16x9">
                            <iframe src="https://www.youtube.com/embed/<?= esc($news['youtube_id']) ?>?rel=0&modestbranding=1"
                                frameborder="0"
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                allowfullscreen
                                class="rounded shadow">
                            </iframe>
                        </div>
                        <div class="text-center mt-2">
                            <small class="text-muted">
                                <i class="fab fa-youtube me-1"></i>
                                <a href="<?= esc($news['youtube_url']) ?>" target="_blank" class="text-decoration-none">
                                    Watch on YouTube
                                </a>
                            </small>
                        </div>
                    </div>
                <?php elseif ($news['image']): ?>
                    <!-- Featured Image -->
                    <div class="mb-4">
                        <img src="<?= base_url('uploads/news/' . $news['image']) ?>"
                            alt="<?= esc($news['title']) ?>"
                            class="img-fluid rounded shadow">
                    </div>
                <?php endif; ?>

                <!-- News Description -->
                <div class="lead mb-4 p-3 bg-light rounded">
                    <?= esc($news['description']) ?>
                </div>

                <!-- In-Article Ads -->
                <?= view('components/ad_display', [
                    'ads' => $inArticleAds,
                    'position' => 'in_article'
                ]) ?>

                <!-- News Content -->
                <div class="news-content">
                    <?= nl2br(esc($news['content'])) ?>
                </div>

                <!-- Content Bottom Ads -->
                <?= view('components/ad_display', [
                    'ads' => $contentBottomAds,
                    'position' => 'content_bottom',
                    'showLabel' => true
                ]) ?>

                <!-- Social Share -->
                <div class="mt-5 pt-4 border-top">
                    <h5 class="mb-3">इस समाचार को शेयर करें:</h5>
                    <div class="d-flex gap-2">
                        <a href="#" class="btn btn-primary" onclick="shareOnFacebook()">
                            <i class="fab fa-facebook-f me-1"></i>Facebook
                        </a>
                        <a href="#" class="btn btn-info" onclick="shareOnTwitter()">
                            <i class="fab fa-twitter me-1"></i>Twitter
                        </a>
                        <a href="#" class="btn btn-success" onclick="shareOnWhatsApp()">
                            <i class="fab fa-whatsapp me-1"></i>WhatsApp
                        </a>
                        <a href="#" class="btn btn-secondary" onclick="copyLink()">
                            <i class="fas fa-link me-1"></i>Link Copy
                        </a>
                    </div>
                </div>
            </article>

            <!-- Related News -->
            <?php if (!empty($relatedNews)): ?>
                <section class="mt-5">
                    <h3 class="mb-4"><i class="fas fa-newspaper me-2"></i>संबंधित समाचार</h3>
                    <div class="row">
                        <?php foreach ($relatedNews as $related): ?>
                            <div class="col-md-6 mb-4">
                                <div class="card news-card h-100">
                                    <?php if ($related['image']): ?>
                                        <img src="<?= base_url('uploads/news/' . $related['image']) ?>"
                                            class="card-img-top" style="height: 150px; object-fit: cover;"
                                            alt="<?= esc($related['title']) ?>">
                                    <?php endif; ?>
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <a href="<?= base_url('news/' . $related['slug']) ?>"
                                                class="text-dark text-decoration-none">
                                                <?= esc($related['title']) ?>
                                            </a>
                                        </h6>
                                        <p class="card-text"><?= esc(substr($related['description'], 0, 100)) ?>...</p>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i><?= date('d M Y', strtotime($related['created_at'])) ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </section>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Poll Sidebar -->
            <?= view('components/poll_sidebar', ['polls' => $polls]) ?>

            <!-- Sidebar Ads -->
            <?= view('components/ad_display', [
                'ads' => $sidebarAds,
                'position' => 'sidebar',
                'showLabel' => true
            ]) ?>

            <!-- Categories Widget -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>अन्य श्रेणियां</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($categories)): ?>
                        <?php foreach ($categories as $category): ?>
                            <a href="<?= base_url('category/' . $category['slug']) ?>"
                                class="btn btn-outline-primary btn-sm mb-2 me-2">
                                <?= esc($category['name']) ?>
                            </a>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Back to Top -->
            <div class="text-center">
                <button class="btn btn-secondary" onclick="window.scrollTo(0,0)">
                    <i class="fas fa-arrow-up me-1"></i>वापस ऊपर जाएं
                </button>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    function shareNews() {
        if (navigator.share) {
            navigator.share({
                title: '<?= esc($news['title']) ?>',
                text: '<?= esc($news['description']) ?>',
                url: window.location.href
            });
        } else {
            copyLink();
        }
    }

    function shareOnFacebook() {
        const url = encodeURIComponent(window.location.href);
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
    }

    function shareOnTwitter() {
        const url = encodeURIComponent(window.location.href);
        const text = encodeURIComponent('<?= esc($news['title']) ?>');
        window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank');
    }

    function shareOnWhatsApp() {
        const url = encodeURIComponent(window.location.href);
        const text = encodeURIComponent('<?= esc($news['title']) ?> - ');
        window.open(`https://wa.me/?text=${text}${url}`, '_blank');
    }

    function copyLink() {
        navigator.clipboard.writeText(window.location.href).then(function() {
            alert('लिंक कॉपी हो गया!');
        });
    }

    function printNews() {
        window.print();
    }
</script>
<?= $this->endSection() ?>