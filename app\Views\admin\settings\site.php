<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-globe me-2"></i>Site Settings</h2>
                <a href="<?= base_url('admin/settings') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Settings
                </a>
            </div>

            <!-- Success/Error Messages -->
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('errors')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <h6>Please fix the following errors:</h6>
                    <ul class="mb-0">
                        <?php foreach (session()->getFlashdata('errors') as $error): ?>
                            <li><?= esc($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>Website Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('admin/settings/site/update') ?>" method="post" enctype="multipart/form-data">
                        <?= csrf_field() ?>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <!-- Site Name -->
                                <div class="mb-3">
                                    <label for="site_name" class="form-label">
                                        <i class="fas fa-globe me-2"></i>Site Name *
                                    </label>
                                    <input type="text" class="form-control" id="site_name" name="site_name" 
                                           value="<?= old('site_name', getSetting('site_name', 'BBC News Portal')) ?>" required>
                                </div>

                                <!-- Site Tagline -->
                                <div class="mb-3">
                                    <label for="site_tagline" class="form-label">
                                        <i class="fas fa-tag me-2"></i>Site Tagline
                                    </label>
                                    <input type="text" class="form-control" id="site_tagline" name="site_tagline" 
                                           value="<?= old('site_tagline', getSetting('site_tagline', 'Latest Hindi News Portal')) ?>" 
                                           placeholder="Optional tagline for your website">
                                </div>

                                <!-- Current Logo -->
                                <?php $currentLogo = getSetting('site_logo'); ?>
                                <?php if ($currentLogo): ?>
                                    <div class="mb-3">
                                        <label class="form-label">
                                            <i class="fas fa-image me-2"></i>Current Logo
                                        </label>
                                        <div class="card bg-light">
                                            <div class="card-body p-3">
                                                <img src="<?= base_url('uploads/site/' . $currentLogo) ?>" 
                                                     alt="Current Logo" class="img-thumbnail" style="max-height: 100px;">
                                                <div class="mt-2">
                                                    <small class="text-muted"><?= esc($currentLogo) ?></small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Upload New Logo -->
                                <div class="mb-3">
                                    <label for="site_logo" class="form-label">
                                        <i class="fas fa-upload me-2"></i><?= $currentLogo ? 'Replace Logo' : 'Upload Logo' ?>
                                    </label>
                                    <input type="file" class="form-control" id="site_logo" name="site_logo" accept="image/*">
                                    <div class="form-text">
                                        Recommended: PNG format, transparent background, max size 2MB
                                    </div>
                                </div>

                                <!-- Current Favicon -->
                                <?php $currentFavicon = getSetting('site_favicon'); ?>
                                <?php if ($currentFavicon): ?>
                                    <div class="mb-3">
                                        <label class="form-label">
                                            <i class="fas fa-star me-2"></i>Current Favicon
                                        </label>
                                        <div class="card bg-light">
                                            <div class="card-body p-3">
                                                <img src="<?= base_url('uploads/site/' . $currentFavicon) ?>" 
                                                     alt="Current Favicon" class="img-thumbnail" style="max-height: 32px;">
                                                <div class="mt-2">
                                                    <small class="text-muted"><?= esc($currentFavicon) ?></small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Upload New Favicon -->
                                <div class="mb-3">
                                    <label for="site_favicon" class="form-label">
                                        <i class="fas fa-upload me-2"></i><?= $currentFavicon ? 'Replace Favicon' : 'Upload Favicon' ?>
                                    </label>
                                    <input type="file" class="form-control" id="site_favicon" name="site_favicon" accept="image/*">
                                    <div class="form-text">
                                        Recommended: ICO or PNG format, 32x32 pixels, max size 1MB
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- Preview Section -->
                                <div class="card bg-light">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">
                                            <i class="fas fa-eye me-2"></i>Preview
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="text-center">
                                            <?php if ($currentLogo): ?>
                                                <img src="<?= base_url('uploads/site/' . $currentLogo) ?>" 
                                                     alt="Logo Preview" class="img-fluid mb-2" style="max-height: 80px;">
                                            <?php else: ?>
                                                <div class="bg-white rounded p-3 mb-2">
                                                    <i class="fas fa-image fa-2x text-muted"></i>
                                                    <div class="small text-muted">No logo uploaded</div>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <h6 class="mb-1" id="preview-site-name"><?= getSetting('site_name', 'BBC News Portal') ?></h6>
                                            <small class="text-muted" id="preview-tagline"><?= getSetting('site_tagline', 'Latest Hindi News Portal') ?></small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Guidelines -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">
                                            <i class="fas fa-info-circle me-2"></i>Guidelines
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-unstyled small mb-0">
                                            <li><i class="fas fa-check text-success me-2"></i>Logo: PNG with transparent background</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Logo size: 200x60 pixels recommended</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Favicon: 32x32 pixels ICO/PNG</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Keep site name concise</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Tagline should be descriptive</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-flex justify-content-between mt-4">
                            <a href="<?= base_url('admin/settings') ?>" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Site Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live preview updates
    const siteNameInput = document.getElementById('site_name');
    const siteTaglineInput = document.getElementById('site_tagline');
    const previewSiteName = document.getElementById('preview-site-name');
    const previewTagline = document.getElementById('preview-tagline');
    
    siteNameInput.addEventListener('input', function() {
        previewSiteName.textContent = this.value || 'BBC News Portal';
    });
    
    siteTaglineInput.addEventListener('input', function() {
        previewTagline.textContent = this.value || 'Latest Hindi News Portal';
    });
    
    // File validation
    const logoInput = document.getElementById('site_logo');
    const faviconInput = document.getElementById('site_favicon');
    
    logoInput.addEventListener('change', function() {
        validateImageFile(this, 2 * 1024 * 1024); // 2MB
    });
    
    faviconInput.addEventListener('change', function() {
        validateImageFile(this, 1 * 1024 * 1024); // 1MB
    });
    
    function validateImageFile(input, maxSize) {
        const file = input.files[0];
        if (file) {
            if (file.size > maxSize) {
                alert('File size too large. Maximum allowed: ' + (maxSize / (1024 * 1024)) + 'MB');
                input.value = '';
                return;
            }
            
            if (!file.type.startsWith('image/')) {
                alert('Please select a valid image file');
                input.value = '';
                return;
            }
        }
    }
});

// Helper function to get setting value
function getSetting(key, defaultValue = '') {
    // This would be replaced with actual PHP function call
    return defaultValue;
}
</script>
<?= $this->endSection() ?>
