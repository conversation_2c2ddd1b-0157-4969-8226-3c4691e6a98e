<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-user-circle me-2"></i>Profile Management</h2>
                <a href="<?= base_url('admin/settings') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Settings
                </a>
            </div>

            <!-- Success/Error Messages -->
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('errors')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <h6>Please fix the following errors:</h6>
                    <ul class="mb-0">
                        <?php foreach (session()->getFlashdata('errors') as $error): ?>
                            <li><?= esc($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- Profile Form -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user-edit me-2"></i>Edit Profile Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="<?= base_url('admin/settings/profile/update') ?>" method="post" enctype="multipart/form-data">
                                <?= csrf_field() ?>

                                <div class="row">
                                    <div class="col-md-6">
                                        <!-- Full Name -->
                                        <div class="mb-3">
                                            <label for="full_name" class="form-label">
                                                <i class="fas fa-user me-2"></i>Full Name *
                                            </label>
                                            <input type="text" class="form-control" id="full_name" name="full_name"
                                                value="<?= old('full_name', $user['full_name']) ?>" required>
                                        </div>

                                        <!-- Email -->
                                        <div class="mb-3">
                                            <label for="email" class="form-label">
                                                <i class="fas fa-envelope me-2"></i>Email Address *
                                            </label>
                                            <input type="email" class="form-control" id="email" name="email"
                                                value="<?= old('email', $user['email']) ?>" required>
                                        </div>

                                        <!-- Phone -->
                                        <div class="mb-3">
                                            <label for="phone" class="form-label">
                                                <i class="fas fa-phone me-2"></i>Phone Number
                                            </label>
                                            <input type="text" class="form-control" id="phone" name="phone"
                                                value="<?= old('phone', $user['phone'] ?? '') ?>" placeholder="Optional">
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <!-- Current Profile Image -->
                                        <div class="mb-3">
                                            <label class="form-label">
                                                <i class="fas fa-image me-2"></i>Current Profile Picture
                                            </label>
                                            <div class="text-center">
                                                <?php if (isset($user['profile_image']) && $user['profile_image']): ?>
                                                    <img src="<?= base_url('uploads/profiles/' . $user['profile_image']) ?>"
                                                        alt="Profile Picture" class="img-thumbnail mb-2" style="max-width: 150px; max-height: 150px;">
                                                <?php else: ?>
                                                    <div class="bg-light rounded d-flex align-items-center justify-content-center mb-2" style="width: 150px; height: 150px; margin: 0 auto;">
                                                        <i class="fas fa-user fa-3x text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <!-- Upload New Profile Image -->
                                        <div class="mb-3">
                                            <label for="profile_image" class="form-label">
                                                <i class="fas fa-upload me-2"></i>Upload New Profile Picture
                                            </label>
                                            <input type="file" class="form-control" id="profile_image" name="profile_image"
                                                accept="image/*">
                                            <div class="form-text">
                                                Supported formats: JPG, PNG, GIF. Max size: 2MB
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Submit Button -->
                                <div class="d-flex justify-content-between">
                                    <a href="<?= base_url('admin/settings') ?>" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Update Profile
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Profile Info & Quick Actions -->
                <div class="col-md-4">
                    <!-- Current User Info -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>Current Information
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <?php if (isset($user['profile_image']) && $user['profile_image']): ?>
                                    <img src="<?= base_url('uploads/profiles/' . $user['profile_image']) ?>"
                                        alt="Profile Picture" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">
                                <?php else: ?>
                                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 100px; height: 100px;">
                                        <i class="fas fa-user fa-2x text-muted"></i>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td><?= esc($user['full_name']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td><?= esc($user['email']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Role:</strong></td>
                                    <td>
                                        <span class="badge bg-primary"><?= ucfirst($user['role']) ?></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <span class="badge bg-<?= $user['status'] === 'active' ? 'success' : 'secondary' ?>">
                                            <?= ucfirst($user['status']) ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Joined:</strong></td>
                                    <td><?= date('d M Y', strtotime($user['created_at'])) ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>Quick Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="<?= base_url('admin/settings/change-password') ?>" class="btn btn-warning btn-sm">
                                    <i class="fas fa-key me-2"></i>Change Password
                                </a>
                                <a href="<?= base_url('admin/settings/site') ?>" class="btn btn-info btn-sm">
                                    <i class="fas fa-globe me-2"></i>Site Settings
                                </a>
                                <a href="<?= base_url('admin/dashboard') ?>" class="btn btn-success btn-sm">
                                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Profile image preview
        const profileImageInput = document.getElementById('profile_image');

        profileImageInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                // Validate file size (2MB)
                const maxSize = 2 * 1024 * 1024;
                if (file.size > maxSize) {
                    alert('File size must be less than 2MB');
                    this.value = '';
                    return;
                }

                // Validate file type
                if (!file.type.startsWith('image/')) {
                    alert('Please select a valid image file');
                    this.value = '';
                    return;
                }

                // Show preview (optional)
                const reader = new FileReader();
                reader.onload = function(e) {
                    // You can add preview functionality here
                    console.log('Image selected:', file.name);
                };
                reader.readAsDataURL(file);
            }
        });
    });
</script>
<?= $this->endSection() ?>