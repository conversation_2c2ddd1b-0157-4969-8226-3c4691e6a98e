<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">Add New Category</h4>
    <a href="<?= base_url('admin/categories') ?>" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to Categories
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <?php if (session()->getFlashdata('errors')): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <strong>Please fix the following errors:</strong>
                        <ul class="mb-0 mt-2">
                            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                <li><?= $error ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <form action="<?= base_url('admin/categories/store') ?>" method="post">
                    <?= csrf_field() ?>
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">
                            <i class="fas fa-tag me-2"></i>Category Name (श्रेणी नाम) <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="name" name="name" 
                               value="<?= old('name') ?>" required 
                               placeholder="e.g., राजनीति, खेल, मनोरंजन">
                        <div class="form-text">Enter the category name in Hindi</div>
                    </div>

                    <div class="mb-3">
                        <label for="slug" class="form-label">
                            <i class="fas fa-link me-2"></i>Slug (URL Identifier) <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="slug" name="slug" 
                               value="<?= old('slug') ?>" required 
                               placeholder="e.g., politics, sports, entertainment">
                        <div class="form-text">URL-friendly identifier (lowercase, no spaces, use hyphens)</div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">
                            <i class="fas fa-align-left me-2"></i>Description (विवरण)
                        </label>
                        <textarea class="form-control" id="description" name="description" rows="4" 
                                  placeholder="Category description in Hindi..."><?= old('description') ?></textarea>
                        <div class="form-text">Optional description for the category</div>
                    </div>

                    <div class="mb-3">
                        <label for="status" class="form-label">
                            <i class="fas fa-toggle-on me-2"></i>Status <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="">Select Status</option>
                            <option value="active" <?= old('status') === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="inactive" <?= old('status') === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                        </select>
                        <div class="form-text">Active categories will be available for news articles</div>
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <a href="<?= base_url('admin/categories') ?>" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Create Category
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Category Guidelines</h6>
            </div>
            <div class="card-body">
                <h6>Naming Tips:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Use clear Hindi names</li>
                    <li><i class="fas fa-check text-success me-2"></i>Keep names concise</li>
                    <li><i class="fas fa-check text-success me-2"></i>Make them descriptive</li>
                    <li><i class="fas fa-check text-success me-2"></i>Avoid special characters</li>
                </ul>

                <hr>
                <h6>Slug Guidelines:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-link text-info me-2"></i>Use lowercase letters</li>
                    <li><i class="fas fa-link text-info me-2"></i>Replace spaces with hyphens</li>
                    <li><i class="fas fa-link text-info me-2"></i>Use English equivalents</li>
                    <li><i class="fas fa-link text-info me-2"></i>Keep it short and memorable</li>
                </ul>

                <hr>
                <h6>Examples:</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Slug</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>राजनीति</td>
                                <td>politics</td>
                            </tr>
                            <tr>
                                <td>खेल</td>
                                <td>sports</td>
                            </tr>
                            <tr>
                                <td>मनोरंजन</td>
                                <td>entertainment</td>
                            </tr>
                            <tr>
                                <td>तकनीक</td>
                                <td>technology</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Pro Tips</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li><i class="fas fa-star text-warning me-2"></i>Plan your category structure</li>
                    <li><i class="fas fa-star text-warning me-2"></i>Keep categories broad enough</li>
                    <li><i class="fas fa-star text-warning me-2"></i>Avoid too many categories</li>
                    <li><i class="fas fa-star text-warning me-2"></i>Think about user navigation</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Auto-generate slug from name
    document.getElementById('name').addEventListener('input', function() {
        const name = this.value;
        const slug = name.toLowerCase()
                        .replace(/[^\w\s-]/g, '') // Remove special characters
                        .replace(/\s+/g, '-')     // Replace spaces with hyphens
                        .replace(/-+/g, '-')      // Replace multiple hyphens with single
                        .trim();
        
        // Simple transliteration for common Hindi words
        const transliterations = {
            'राजनीति': 'politics',
            'खेल': 'sports', 
            'मनोरंजन': 'entertainment',
            'तकनीक': 'technology',
            'व्यापार': 'business',
            'स्वास्थ्य': 'health',
            'शिक्षा': 'education',
            'विज्ञान': 'science'
        };
        
        const transliterated = transliterations[name] || slug;
        document.getElementById('slug').value = transliterated;
    });

    // Validate slug format
    document.getElementById('slug').addEventListener('input', function() {
        const slug = this.value;
        const validSlug = slug.toLowerCase()
                             .replace(/[^a-z0-9-]/g, '')
                             .replace(/-+/g, '-');
        
        if (slug !== validSlug) {
            this.value = validSlug;
        }
    });
</script>
<?= $this->endSection() ?>
