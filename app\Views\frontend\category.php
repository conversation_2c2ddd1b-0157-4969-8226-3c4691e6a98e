<?= $this->extend('frontend/layout') ?>

<?= $this->section('content') ?>

<div class="container">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>">होम</a></li>
                    <li class="breadcrumb-item active" aria-current="page"><?= esc($category['name']) ?></li>
                </ol>
            </nav>

            <!-- Category Header -->
            <div class="mb-5">
                <div class="d-flex align-items-center mb-3">
                    <h1 class="display-6 mb-0 me-3"><?= esc($category['name']) ?></h1>
                    <span class="badge bg-primary fs-6"><?= count($news) ?> समाचार</span>
                </div>
                <?php if ($category['description']): ?>
                    <p class="lead text-muted"><?= esc($category['description']) ?></p>
                <?php endif; ?>
                <hr>
            </div>

            <!-- News Articles -->
            <?php if (!empty($news)): ?>
                <div class="row">
                    <?php foreach ($news as $article): ?>
                        <div class="col-md-6 mb-4">
                            <div class="card news-card h-100">
                                <?php if ($article['image']): ?>
                                    <img src="<?= base_url('uploads/news/' . $article['image']) ?>"
                                        class="card-img-top" style="height: 200px; object-fit: cover;"
                                        alt="<?= esc($article['title']) ?>">
                                <?php endif; ?>
                                <div class="card-body d-flex flex-column">
                                    <?php if ($article['featured']): ?>
                                        <span class="badge bg-warning text-dark mb-2 align-self-start">
                                            <i class="fas fa-star me-1"></i>मुख्य समाचार
                                        </span>
                                    <?php endif; ?>

                                    <h5 class="card-title">
                                        <a href="<?= base_url('news/' . $article['slug']) ?>"
                                            class="text-dark text-decoration-none">
                                            <?= esc($article['title']) ?>
                                        </a>
                                    </h5>

                                    <p class="card-text flex-grow-1">
                                        <?= esc(substr($article['description'], 0, 120)) ?>...
                                    </p>

                                    <div class="news-meta mt-auto">
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i><?= esc($article['author_name']) ?>
                                            <br>
                                            <i class="fas fa-clock me-1"></i><?= date('d M Y, H:i', strtotime($article['created_at'])) ?>
                                            <i class="fas fa-eye ms-3 me-1"></i><?= number_format($article['views']) ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination (if needed) -->
                <div class="text-center mt-5">
                    <nav aria-label="News pagination">
                        <ul class="pagination justify-content-center">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1">पिछला</a>
                            </li>
                            <li class="page-item active">
                                <a class="page-link" href="#">1</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">2</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">3</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">अगला</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">इस श्रेणी में कोई समाचार नहीं मिला</h4>
                    <p class="text-muted">जल्द ही इस श्रेणी में नए समाचार उपलब्ध होंगे।</p>
                    <a href="<?= base_url() ?>" class="btn btn-primary">
                        <i class="fas fa-home me-2"></i>होम पेज पर वापस जाएं
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Sidebar Ads -->
            <?php if (!empty($sidebarAds)): ?>
                <?php foreach ($sidebarAds as $ad): ?>
                    <div class="sidebar-ad" data-ad-id="<?= $ad['id'] ?>">
                        <?php if ($ad['type'] === 'banner' && $ad['image']): ?>
                            <?php if ($ad['link_url']): ?>
                                <a href="<?= esc($ad['link_url']) ?>" target="_blank" onclick="trackAdClick(<?= $ad['id'] ?>)">
                                    <img src="<?= base_url('uploads/ads/' . $ad['image']) ?>"
                                        alt="<?= esc($ad['title']) ?>" class="img-fluid">
                                </a>
                            <?php else: ?>
                                <img src="<?= base_url('uploads/ads/' . $ad['image']) ?>"
                                    alt="<?= esc($ad['title']) ?>" class="img-fluid">
                            <?php endif; ?>
                        <?php elseif ($ad['type'] === 'text'): ?>
                            <div class="card">
                                <div class="card-body text-center">
                                    <h6 class="card-title"><?= esc($ad['title']) ?></h6>
                                    <?php if ($ad['description']): ?>
                                        <p class="card-text"><?= esc($ad['description']) ?></p>
                                    <?php endif; ?>
                                    <?php if ($ad['link_url']): ?>
                                        <a href="<?= esc($ad['link_url']) ?>" target="_blank"
                                            class="btn btn-primary btn-sm" onclick="trackAdClick(<?= $ad['id'] ?>)">
                                            और जानें
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>

            <!-- Other Categories -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>अन्य श्रेणियां</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($categories)): ?>
                        <?php foreach ($categories as $cat): ?>
                            <?php if ($cat['id'] !== $category['id']): ?>
                                <a href="<?= base_url('category/' . $cat['slug']) ?>"
                                    class="btn btn-outline-primary btn-sm mb-2 me-2">
                                    <?= esc($cat['name']) ?>
                                </a>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Popular in Category -->
            <?php if (!empty($news)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-fire me-2"></i><?= esc($category['name']) ?> में लोकप्रिय
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php
                        // Sort by views and get top 5
                        $popularNews = $news;
                        usort($popularNews, function ($a, $b) {
                            return $b['views'] - $a['views'];
                        });
                        $popularNews = array_slice($popularNews, 0, 5);
                        ?>

                        <?php foreach ($popularNews as $index => $popular): ?>
                            <div class="d-flex mb-3 <?= $index < 4 ? 'border-bottom pb-3' : '' ?>">
                                <div class="flex-shrink-0 me-3">
                                    <span class="badge bg-primary rounded-pill"><?= $index + 1 ?></span>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="<?= base_url('news/' . $popular['slug']) ?>"
                                            class="text-dark text-decoration-none">
                                            <?= esc(substr($popular['title'], 0, 60)) ?>...
                                        </a>
                                    </h6>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i><?= number_format($popular['views']) ?> views
                                        <i class="fas fa-clock ms-2 me-1"></i><?= date('d M', strtotime($popular['created_at'])) ?>
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Quick Navigation -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-compass me-2"></i>त्वरित नेवीगेशन</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= base_url() ?>" class="btn btn-outline-primary">
                            <i class="fas fa-home me-2"></i>होम पेज
                        </a>
                        <a href="#" class="btn btn-outline-secondary">
                            <i class="fas fa-search me-2"></i>समाचार खोजें
                        </a>
                        <a href="#" class="btn btn-outline-info">
                            <i class="fas fa-rss me-2"></i>RSS Feed
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>