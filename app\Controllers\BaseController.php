<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use <PERSON>Igniter\HTTP\CLIRequest;
use <PERSON>Igniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;

/**
 * Class BaseController
 *
 * BaseController provides a convenient place for loading components
 * and performing functions that are needed by all your controllers.
 * Extend this class in any new controllers:
 *     class Home extends BaseController
 *
 * For security be sure to declare any new methods as protected or private.
 */
abstract class BaseController extends Controller
{
    /**
     * Instance of the main Request object.
     *
     * @var CLIRequest|IncomingRequest
     */
    protected $request;

    /**
     * An array of helpers to be loaded automatically upon
     * class instantiation. These helpers will be available
     * to all other controllers that extend BaseController.
     *
     * @var list<string>
     */
    protected $helpers = ['hindi', 'common'];

    /**
     * Be sure to declare properties for any property fetch you initialized.
     * The creation of dynamic property is deprecated in PHP 8.2.
     */
    // protected $session;

    /**
     * @return void
     */
    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        // Do Not Edit This Line
        parent::initController($request, $response, $logger);

        // Preload any models, libraries, etc, here.

        // E.g.: $this->session = service('session');

        // Load global settings for frontend
        if (!str_contains(current_url(), '/admin/')) {
            $this->loadGlobalSettings();
        }
    }

    /**
     * Load global settings for frontend
     */
    protected function loadGlobalSettings()
    {
        try {
            $settingModel = new \App\Models\Setting();
            $siteSettings = $settingModel->getSiteSettings();

            // Store settings in session for global access
            session()->set('siteSettings', $siteSettings);
        } catch (\Exception $e) {
            // Handle gracefully if settings table doesn't exist yet
            log_message('error', 'Could not load site settings: ' . $e->getMessage());
        }
    }
}
