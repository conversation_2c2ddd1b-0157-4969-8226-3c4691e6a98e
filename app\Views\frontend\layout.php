<!DOCTYPE html>
<html lang="hi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php $siteSettings = getSiteSettings(); ?>
    <title><?= isset($title) ? $title . ' - ' . $siteSettings['site_name'] : $siteSettings['site_name'] . ' - ' . $siteSettings['site_tagline'] ?></title>
    <meta name="description" content="<?= esc($siteSettings['meta_description']) ?>">
    <meta name="keywords" content="<?= esc($siteSettings['meta_keywords']) ?>">

    <!-- Favicon -->
    <?php if ($siteSettings['site_favicon']): ?>
        <link rel="icon" type="image/x-icon" href="<?= base_url('uploads/site/' . $siteSettings['site_favicon']) ?>">
    <?php endif; ?>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Hindi Font -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Noto Sans Devanagari', sans-serif;
            line-height: 1.6;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.8rem;
        }

        .news-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .news-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
        }

        .featured-news {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .category-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }

        .breaking-news {
            background: #dc3545;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }

            50% {
                opacity: 0.7;
            }

            100% {
                opacity: 1;
            }
        }



        .ad-banner {
            margin: 20px 0;
            text-align: center;
        }

        .ad-banner img {
            max-width: 100%;
            height: auto;
        }

        .sidebar-ad {
            margin-bottom: 20px;
            text-align: center;
        }

        .news-meta {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .news-content {
            font-size: 1.1rem;
            line-height: 1.8;
        }

        .breaking-news-ticker-container {
            border-bottom: 2px solid #b02a37;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .breaking-news-ticker {
            display: flex;
            align-items: center;
            overflow: hidden;
            width: 100%;
        }

        .breaking-news-label {
            background: #b02a37;
            color: white;
            padding: 8px 15px;
            font-size: 0.9rem;
            font-weight: bold;
            white-space: nowrap;
            animation: pulse 2s infinite;
            border-radius: 0 15px 15px 0;
            margin-right: 15px;
        }

        .breaking-news-content {
            flex: 1;
            overflow: hidden;
            padding: 8px 0;
        }

        .breaking-news-scroll {
            display: flex;
            animation: scroll-left 45s linear infinite;
        }

        .breaking-news-item {
            white-space: nowrap;
            margin-right: 50px;
            font-size: 0.9rem;
            color: white;
        }

        .breaking-news-item a {
            color: white !important;
            text-decoration: none !important;
        }

        .breaking-news-item a:hover {
            text-decoration: underline !important;
        }

        @keyframes scroll-left {
            0% {
                transform: translateX(100%);
            }

            100% {
                transform: translateX(-100%);
            }
        }

        @media (max-width: 768px) {
            .breaking-news-ticker {
                flex-direction: row;
                align-items: center;
            }

            .breaking-news-label {
                padding: 6px 12px;
                font-size: 0.8rem;
                border-radius: 0 15px 15px 0;
                margin-right: 10px;
                margin-bottom: 0;
                flex-shrink: 0;
            }

            .breaking-news-content {
                padding: 5px 0;
                flex: 1;
                overflow: hidden;
            }

            .breaking-news-item {
                font-size: 0.8rem;
                margin-right: 30px;
            }

            .breaking-news-scroll {
                animation-duration: 35s;
            }
        }

        @media (max-width: 576px) {
            .breaking-news-ticker {
                flex-direction: row;
                align-items: center;
            }

            .breaking-news-label {
                font-size: 0.75rem;
                padding: 5px 10px;
                margin-right: 8px;
                flex-shrink: 0;
            }

            .breaking-news-content {
                flex: 1;
                overflow: hidden;
            }

            .breaking-news-item {
                font-size: 0.75rem;
                margin-right: 25px;
            }

            .breaking-news-scroll {
                animation-duration: 30s;
            }
        }

        /* Navigation Styling - White Background with Red Text */
        .navbar {
            background-color: #ffffff !important;
            border-bottom: 2px solid #dc3545;
        }

        .navbar-brand {
            color: #dc3545 !important;
            font-weight: bold;
            font-size: 1.5rem;
        }

        .navbar-brand:hover {
            color: #b02a37 !important;
        }

        .navbar-nav .nav-link {
            color: #dc3545 !important;
            font-weight: 500;
            padding: 0.75rem 1rem !important;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: #b02a37 !important;
            background-color: #f8f9fa;
            border-radius: 5px;
        }

        .navbar-nav .nav-link.active {
            color: #ffffff !important;
            background-color: #dc3545;
            border-radius: 5px;
        }

        .navbar-toggler {
            border-color: #dc3545;
        }

        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28220, 53, 69, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }

        .btn-outline-light {
            color: #dc3545 !important;
            border-color: #dc3545 !important;
        }

        .btn-outline-light:hover {
            color: #ffffff !important;
            background-color: #dc3545 !important;
            border-color: #dc3545 !important;
        }

        /* Professional Sidebar */
        .sidebar-widget {
            background: var(--white);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border-left: 5px solid var(--primary-red);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .sidebar-widget:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
        }

        .sidebar-widget h5,
        .sidebar-widget h6 {
            color: var(--primary-red);
            font-weight: 700;
            margin-bottom: 1.5rem;
            position: relative;
            padding-bottom: 0.5rem;
        }

        .sidebar-widget h5::after,
        .sidebar-widget h6::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-red), var(--dark-red));
            border-radius: 2px;
        }

        /* Professional Buttons */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-red), var(--dark-red));
            border: none;
            font-weight: 700;
            padding: 12px 30px;
            border-radius: 25px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px var(--shadow-medium);
        }

        .btn-outline-primary {
            color: var(--primary-red);
            border: 2px solid var(--primary-red);
            font-weight: 700;
            padding: 10px 28px;
            border-radius: 25px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover {
            background: var(--primary-red);
            border-color: var(--primary-red);
            color: var(--white);
            transform: translateY(-3px);
            box-shadow: 0 10px 25px var(--shadow-medium);
        }

        /* Professional Footer */
        .footer {
            background: #000000 !important;
            color: #ffffff;
            position: relative;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-red), var(--dark-red));
        }

        .footer h5,
        .footer h6 {
            color: var(--primary-red);
            font-weight: 700;
            margin-bottom: 1.5rem;
        }

        .footer a {
            color: #d1d5db;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .footer a:hover {
            color: var(--primary-red);
            transform: translateX(5px);
        }

        /* Professional Form Elements */
        .form-control {
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            padding: 12px 16px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-red);
            box-shadow: 0 0 0 3px var(--shadow-light);
        }

        /* Professional Badges */
        .badge {
            font-weight: 700;
            padding: 8px 12px;
            border-radius: 15px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .bg-primary {
            background: linear-gradient(135deg, var(--primary-red), var(--dark-red)) !important;
        }

        /* Professional Pagination */
        .page-link {
            color: var(--primary-red);
            border-color: var(--primary-red);
            font-weight: 600;
            padding: 10px 16px;
            margin: 0 2px;
            border-radius: 8px;
        }

        .page-link:hover {
            color: var(--white);
            background-color: var(--primary-red);
            border-color: var(--primary-red);
        }

        .page-item.active .page-link {
            background-color: var(--primary-red);
            border-color: var(--primary-red);
        }

        /* Professional Ad Banners */
        .ad-banner {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #e0f2fe;
            border-radius: 10px;
            border: 2px dashed #81d4fa;
        }

        .ad-banner img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
        }

        .sidebar-ad {
            background: #e0f2fe;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            border: 2px dashed #81d4fa;
            text-align: center;
        }

        /* Ensure All Widget Titles are Red */
        .widget-title,
        .sidebar-title,
        .section-title,
        .card-header h5,
        .card-header h6,
        .widget h5,
        .widget h6,
        .trending-title,
        .popular-title,
        .recent-title {
            color: #dc2626 !important;
            font-weight: 700;
        }

        /* Strict Color Palette Enforcement */
        .text-primary {
            color: #dc2626 !important;
        }

        .bg-primary {
            background-color: #dc2626 !important;
        }

        .border-primary {
            border-color: #dc2626 !important;
        }

        /* Remove any other colors except red, black, white, gray, and light blue for ads */
        .text-info,
        .text-success,
        .text-warning,
        .text-danger:not(.text-primary) {
            color: #dc2626 !important;
        }

        .bg-info,
        .bg-success,
        .bg-warning,
        .bg-danger:not(.bg-primary) {
            background-color: #dc2626 !important;
        }

        /* Professional Responsive Design */
        @media (max-width: 768px) {
            .navbar-nav .nav-link {
                padding: 0.5rem 1rem !important;
                margin: 0.25rem 0;
            }

            .sidebar-widget {
                padding: 1.5rem;
                margin-bottom: 1.5rem;
            }

            .news-card img {
                height: 180px;
            }

            .breaking-news-ticker {
                flex-direction: row;
                align-items: center;
            }

            .breaking-news-label {
                padding: 8px 15px;
                font-size: 0.8rem;
                margin-right: 15px;
                flex-shrink: 0;
            }

            .breaking-news-item {
                font-size: 0.8rem;
                margin-right: 40px;
            }

            .ad-header,
            .ad-container.ad-header,
            .header-ads-section {
                display: none !important;
            }
        }

        @media (max-width: 576px) {
            .breaking-news-label {
                font-size: 0.75rem;
                padding: 6px 12px;
                margin-right: 10px;
            }

            .breaking-news-item {
                font-size: 0.75rem;
                margin-right: 30px;
            }

            .ad-header,
            .ad-container.ad-header,
            .header-ads-section {
                display: none !important;
            }
        }

        /* Professional Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--light-gray);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-red), var(--dark-red));
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--dark-red);
        }
    </style>

    <!-- Google Tag Manager -->
    <?php if ($siteSettings['google_tag_manager_id']): ?>
        <script>
            (function(w, d, s, l, i) {
                w[l] = w[l] || [];
                w[l].push({
                    'gtm.start': new Date().getTime(),
                    event: 'gtm.js'
                });
                var f = d.getElementsByTagName(s)[0],
                    j = d.createElement(s),
                    dl = l != 'dataLayer' ? '&l=' + l : '';
                j.async = true;
                j.src =
                    'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
                f.parentNode.insertBefore(j, f);
            })(window, document, 'script', 'dataLayer', '<?= esc($siteSettings['google_tag_manager_id']) ?>');
        </script>
    <?php endif; ?>

    <!-- Google Analytics -->
    <?php if ($siteSettings['google_analytics_id']): ?>
        <script async src="https://www.googletagmanager.com/gtag/js?id=<?= esc($siteSettings['google_analytics_id']) ?>"></script>
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());
            gtag('config', '<?= esc($siteSettings['google_analytics_id']) ?>');
        </script>
    <?php endif; ?>

    <!-- Facebook Pixel -->
    <?php if ($siteSettings['facebook_pixel_id']): ?>
        <script>
            ! function(f, b, e, v, n, t, s) {
                if (f.fbq) return;
                n = f.fbq = function() {
                    n.callMethod ?
                        n.callMethod.apply(n, arguments) : n.queue.push(arguments)
                };
                if (!f._fbq) f._fbq = n;
                n.push = n;
                n.loaded = !0;
                n.version = '2.0';
                n.queue = [];
                t = b.createElement(e);
                t.async = !0;
                t.src = v;
                s = b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t, s)
            }(window, document, 'script',
                'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '<?= esc($siteSettings['facebook_pixel_id']) ?>');
            fbq('track', 'PageView');
        </script>
        <noscript><img height="1" width="1" style="display:none"
                src="https://www.facebook.com/tr?id=<?= esc($siteSettings['facebook_pixel_id']) ?>&ev=PageView&noscript=1" /></noscript>
    <?php endif; ?>
</head>

<body>
    <!-- Google Tag Manager (noscript) -->
    <?php if ($siteSettings['google_tag_manager_id']): ?>
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=<?= esc($siteSettings['google_tag_manager_id']) ?>"
                height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <?php endif; ?>

    <!-- Header Ads -->
    <?php if (!empty($headerAds)): ?>
        <div class="container-fluid bg-light py-2 header-ads-section">
            <div class="container">
                <?php foreach ($headerAds as $ad): ?>
                    <div class="ad-banner">
                        <?php if ($ad['type'] === 'banner' && $ad['image']): ?>
                            <?php if ($ad['link_url']): ?>
                                <a href="<?= esc($ad['link_url']) ?>" target="_blank" onclick="trackAdClick(<?= $ad['id'] ?>)">
                                    <img src="<?= base_url('uploads/ads/' . $ad['image']) ?>"
                                        alt="<?= esc($ad['title']) ?>" class="img-fluid">
                                </a>
                            <?php else: ?>
                                <img src="<?= base_url('uploads/ads/' . $ad['image']) ?>"
                                    alt="<?= esc($ad['title']) ?>" class="img-fluid">
                            <?php endif; ?>
                        <?php elseif ($ad['type'] === 'text'): ?>
                            <div class="alert alert-info">
                                <strong><?= esc($ad['title']) ?></strong>
                                <?php if ($ad['description']): ?>
                                    <br><?= esc($ad['description']) ?>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="<?= base_url() ?>">
                <?php if ($siteSettings['site_logo']): ?>
                    <img src="<?= base_url('uploads/site/' . $siteSettings['site_logo']) ?>"
                        alt="<?= esc($siteSettings['site_name']) ?>"
                        title="<?= esc($siteSettings['site_name']) ?>"
                        style="height: 40px; width: auto;">
                <?php else: ?>
                    <i class="fas fa-newspaper"
                        title="<?= esc($siteSettings['site_name']) ?>"></i>
                <?php endif; ?>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url() ?>">होम</a>
                    </li>

                    <?php if (!empty($categories)): ?>
                        <?php foreach ($categories as $category): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?= base_url('category/' . $category['slug']) ?>">
                                    <?= esc($category['name']) ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    <?php endif; ?>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('epaper') ?>">
                            <i class="fas fa-newspaper me-1"></i>ई-पेपर
                        </a>
                    </li>
                </ul>

                <div class="d-flex align-items-center">
                    <a href="<?= base_url('admin/login') ?>" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-user me-1"></i>Admin
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Breaking News Ticker (Outside Navigation) -->
    <?php if (!empty($breakingNews)): ?>
        <div class="breaking-news-ticker-container bg-danger">
            <div class="container">
                <div class="breaking-news-ticker py-2">
                    <span class="breaking-news-label">
                        <i class="fas fa-bolt me-1"></i>ब्रेकिंग न्यूज़
                    </span>
                    <div class="breaking-news-content">
                        <div class="breaking-news-scroll">
                            <?php foreach ($breakingNews as $index => $news): ?>
                                <span class="breaking-news-item <?= $index === 0 ? 'active' : '' ?>">
                                    <?php if ($news['link_url']): ?>
                                        <a href="<?= esc($news['link_url']) ?>" target="_blank" class="text-white text-decoration-none">
                                            <?= esc($news['title']) ?>
                                        </a>
                                    <?php else: ?>
                                        <?= esc($news['title']) ?>
                                    <?php endif; ?>
                                </span>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="py-4">
        <?= $this->renderSection('content') ?>
    </main>



    <!-- Floating Ads -->
    <?php if (!empty($floatingAds)): ?>
        <?= view('components/ad_display', [
            'ads' => $floatingAds,
            'position' => 'floating'
        ]) ?>
    <?php endif; ?>

    <!-- Sticky Bottom Ads -->
    <?php if (!empty($stickyBottomAds)): ?>
        <?= view('components/ad_display', [
            'ads' => $stickyBottomAds,
            'position' => 'sticky_bottom'
        ]) ?>
    <?php endif; ?>

    <!-- Footer Ads Section -->
    <section class="footer-ads-section">
        <?php if (!empty($footerAds)): ?>
            <div class="container-fluid py-4" style="background: #e0f2fe; border-top: 1px solid #81d4fa;">
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <div class="text-center mb-3">
                                <small class="text-muted fw-bold">ADVERTISEMENT</small>
                            </div>
                            <div class="footer-ads-container">
                                <?php foreach ($footerAds as $ad): ?>
                                    <div class="footer-ad-item mb-3">
                                        <?php if ($ad['type'] === 'banner' && $ad['image']): ?>
                                            <?php if ($ad['link_url']): ?>
                                                <a href="<?= esc($ad['link_url']) ?>" target="_blank" onclick="trackAdClick(<?= $ad['id'] ?>)" class="d-block">
                                                    <img src="<?= base_url('uploads/ads/' . $ad['image']) ?>"
                                                        alt="<?= esc($ad['title']) ?>"
                                                        class="img-fluid rounded shadow-sm mx-auto d-block"
                                                        style="max-height: 120px; max-width: 100%;">
                                                </a>
                                            <?php else: ?>
                                                <img src="<?= base_url('uploads/ads/' . $ad['image']) ?>"
                                                    alt="<?= esc($ad['title']) ?>"
                                                    class="img-fluid rounded shadow-sm mx-auto d-block"
                                                    style="max-height: 120px; max-width: 100%;">
                                            <?php endif; ?>
                                        <?php elseif ($ad['type'] === 'code' && $ad['code']): ?>
                                            <div class="footer-ad-code text-center">
                                                <?= $ad['code'] ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- Default Footer Ad Space -->
            <div class="container-fluid py-4" style="background: #e0f2fe; border-top: 1px solid #81d4fa;">
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <div class="text-center">
                                <small class="text-muted fw-bold mb-3 d-block">ADVERTISEMENT</small>
                                <div class="footer-ad-placeholder">
                                    <div style="background: #f8f9fa; border: 2px dashed #81d4fa; padding: 30px 20px; border-radius: 10px; max-width: 600px; margin: 0 auto;">
                                        <i class="fas fa-ad fa-2x text-muted mb-3"></i>
                                        <h6 class="text-muted mb-2">Footer Advertisement Space</h6>
                                        <p class="text-muted mb-1 small">Your ad could be here</p>
                                        <small class="text-muted">Contact us for advertising opportunities</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </section>

    <!-- Footer -->
    <footer class="footer py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <?php if ($siteSettings['site_logo']): ?>
                            <img src="<?= base_url('uploads/site/' . $siteSettings['site_logo']) ?>"
                                alt="<?= esc($siteSettings['site_name']) ?>"
                                title="<?= esc($siteSettings['site_name']) ?>"
                                style="height: 40px; width: auto;">
                        <?php else: ?>
                            <i class="fas fa-newspaper fa-2x"
                                title="<?= esc($siteSettings['site_name']) ?>"></i>
                        <?php endif; ?>
                    </div>
                    <h5><?= esc($siteSettings['site_name']) ?></h5>
                    <p><?= $siteSettings['footer_description'] ? esc($siteSettings['footer_description']) : 'भारत की सबसे विश्वसनीय हिंदी न्यूज़ वेबसाइट। ताजा खबरें, राजनीति, खेल, मनोरंजन, तकनीक और व्यापार की खबरें।' ?></p>
                </div>
                <div class="col-md-2">
                    <h6>श्रेणियां</h6>
                    <ul class="list-unstyled">
                        <?php if (!empty($categories)): ?>
                            <?php foreach (array_slice($categories, 0, 5) as $category): ?>
                                <li><a href="<?= base_url('category/' . $category['slug']) ?>" class="text-light text-decoration-none"><?= esc($category['name']) ?></a></li>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h6>संपर्क</h6>
                    <?php if ($siteSettings['contact_email']): ?>
                        <p><i class="fas fa-envelope me-2"></i>
                            <a href="mailto:<?= esc($siteSettings['contact_email']) ?>" class="text-light text-decoration-none">
                                <?= esc($siteSettings['contact_email']) ?>
                            </a>
                        </p>
                    <?php endif; ?>
                    <?php if ($siteSettings['contact_phone']): ?>
                        <p><i class="fas fa-phone me-2"></i>
                            <a href="tel:<?= esc($siteSettings['contact_phone']) ?>" class="text-light text-decoration-none">
                                <?= esc($siteSettings['contact_phone']) ?>
                            </a>
                        </p>
                    <?php endif; ?>
                    <?php if ($siteSettings['contact_whatsapp']): ?>
                        <p><i class="fab fa-whatsapp me-2"></i>
                            <a href="https://wa.me/<?= str_replace(['+', '-', ' '], '', $siteSettings['contact_whatsapp']) ?>"
                                target="_blank" class="text-light text-decoration-none">
                                <?= esc($siteSettings['contact_whatsapp']) ?>
                            </a>
                        </p>
                    <?php endif; ?>
                </div>
                <div class="col-md-3">
                    <h6>फॉलो करें</h6>
                    <div class="d-flex gap-3">
                        <?php if ($siteSettings['facebook_url']): ?>
                            <a href="<?= esc($siteSettings['facebook_url']) ?>" target="_blank" class="text-light">
                                <i class="fab fa-facebook fa-2x"></i>
                            </a>
                        <?php endif; ?>
                        <?php if ($siteSettings['twitter_url']): ?>
                            <a href="<?= esc($siteSettings['twitter_url']) ?>" target="_blank" class="text-light">
                                <i class="fab fa-twitter fa-2x"></i>
                            </a>
                        <?php endif; ?>
                        <?php if ($siteSettings['youtube_url']): ?>
                            <a href="<?= esc($siteSettings['youtube_url']) ?>" target="_blank" class="text-light">
                                <i class="fab fa-youtube fa-2x"></i>
                            </a>
                        <?php endif; ?>
                        <?php if ($siteSettings['instagram_url']): ?>
                            <a href="<?= esc($siteSettings['instagram_url']) ?>" target="_blank" class="text-light">
                                <i class="fab fa-instagram fa-2x"></i>
                            </a>
                        <?php endif; ?>
                        <?php if ($siteSettings['linkedin_url']): ?>
                            <a href="<?= esc($siteSettings['linkedin_url']) ?>" target="_blank" class="text-light">
                                <i class="fab fa-linkedin fa-2x"></i>
                            </a>
                        <?php endif; ?>
                        <?php if ($siteSettings['telegram_url']): ?>
                            <a href="<?= esc($siteSettings['telegram_url']) ?>" target="_blank" class="text-light">
                                <i class="fab fa-telegram fa-2x"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p><?= $siteSettings['footer_copyright'] ? esc($siteSettings['footer_copyright']) : '© 2025 BBC News Portal. All rights reserved.' ?></p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Track ad clicks
        function trackAdClick(adId) {
            fetch('<?= base_url("api/track-ad-click") ?>/' + adId, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
        }

        // Track ad impressions on page load
        document.addEventListener('DOMContentLoaded', function() {
            const ads = document.querySelectorAll('[data-ad-id]');
            ads.forEach(function(ad) {
                const adId = ad.getAttribute('data-ad-id');
                fetch('<?= base_url("api/track-ad-impression") ?>/' + adId, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
            });
        });
    </script>

    <?= $this->renderSection('scripts') ?>
</body>

</html>