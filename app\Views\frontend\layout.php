<!DOCTYPE html>
<html lang="hi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title : 'BBC News Portal - Latest Hindi News' ?></title>
    <meta name="description" content="BBC News Portal - Latest Hindi news, breaking news, politics, sports, entertainment, technology and business news in Hindi">
    <meta name="keywords" content="Hindi news, breaking news, राजनीति, खेल, मनोरंजन, तकनीक, व्यापार">

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Hindi Font -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Noto Sans Devanagari', sans-serif;
            line-height: 1.6;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.8rem;
        }

        .news-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .news-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
        }

        .featured-news {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .category-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }

        .breaking-news {
            background: #dc3545;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }

            50% {
                opacity: 0.7;
            }

            100% {
                opacity: 1;
            }
        }

        .footer {
            background: #2c3e50;
            color: white;
        }

        .ad-banner {
            margin: 20px 0;
            text-align: center;
        }

        .ad-banner img {
            max-width: 100%;
            height: auto;
        }

        .sidebar-ad {
            margin-bottom: 20px;
            text-align: center;
        }

        .news-meta {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .news-content {
            font-size: 1.1rem;
            line-height: 1.8;
        }

        .breaking-news-ticker-container {
            border-bottom: 2px solid #b02a37;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .breaking-news-ticker {
            display: flex;
            align-items: center;
            overflow: hidden;
            width: 100%;
        }

        .breaking-news-label {
            background: #b02a37;
            color: white;
            padding: 8px 15px;
            font-size: 0.9rem;
            font-weight: bold;
            white-space: nowrap;
            animation: pulse 2s infinite;
            border-radius: 0 15px 15px 0;
            margin-right: 15px;
        }

        .breaking-news-content {
            flex: 1;
            overflow: hidden;
            padding: 8px 0;
        }

        .breaking-news-scroll {
            display: flex;
            animation: scroll-left 45s linear infinite;
        }

        .breaking-news-item {
            white-space: nowrap;
            margin-right: 50px;
            font-size: 0.9rem;
            color: white;
        }

        .breaking-news-item a {
            color: white !important;
            text-decoration: none !important;
        }

        .breaking-news-item a:hover {
            text-decoration: underline !important;
        }

        @keyframes scroll-left {
            0% {
                transform: translateX(100%);
            }

            100% {
                transform: translateX(-100%);
            }
        }

        @media (max-width: 768px) {
            .breaking-news-ticker {
                flex-direction: column;
                align-items: flex-start;
            }

            .breaking-news-label {
                padding: 6px 12px;
                font-size: 0.8rem;
                border-radius: 0 0 15px 15px;
                margin-right: 0;
                margin-bottom: 5px;
                align-self: flex-start;
            }

            .breaking-news-content {
                padding: 5px 0;
                width: 100%;
            }

            .breaking-news-item {
                font-size: 0.8rem;
                margin-right: 30px;
            }

            .breaking-news-scroll {
                animation-duration: 35s;
            }
        }

        @media (max-width: 576px) {
            .breaking-news-label {
                font-size: 0.75rem;
                padding: 5px 10px;
            }

            .breaking-news-item {
                font-size: 0.75rem;
                margin-right: 25px;
            }

            .breaking-news-scroll {
                animation-duration: 30s;
            }
        }
    </style>
</head>

<body>
    <!-- Header Ads -->
    <?php if (!empty($headerAds)): ?>
        <div class="container-fluid bg-light py-2">
            <div class="container">
                <?php foreach ($headerAds as $ad): ?>
                    <div class="ad-banner">
                        <?php if ($ad['type'] === 'banner' && $ad['image']): ?>
                            <?php if ($ad['link_url']): ?>
                                <a href="<?= esc($ad['link_url']) ?>" target="_blank" onclick="trackAdClick(<?= $ad['id'] ?>)">
                                    <img src="<?= base_url('uploads/ads/' . $ad['image']) ?>"
                                        alt="<?= esc($ad['title']) ?>" class="img-fluid">
                                </a>
                            <?php else: ?>
                                <img src="<?= base_url('uploads/ads/' . $ad['image']) ?>"
                                    alt="<?= esc($ad['title']) ?>" class="img-fluid">
                            <?php endif; ?>
                        <?php elseif ($ad['type'] === 'text'): ?>
                            <div class="alert alert-info">
                                <strong><?= esc($ad['title']) ?></strong>
                                <?php if ($ad['description']): ?>
                                    <br><?= esc($ad['description']) ?>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-top">
        <div class="container">
            <a class="navbar-brand" href="<?= base_url() ?>">
                <i class="fas fa-newspaper me-2"></i>BBC News
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url() ?>">होम</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('epaper') ?>">
                            <i class="fas fa-newspaper me-1"></i>ई-पेपर
                        </a>
                    </li>
                    <?php if (!empty($categories)): ?>
                        <?php foreach ($categories as $category): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?= base_url('category/' . $category['slug']) ?>">
                                    <?= esc($category['name']) ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </ul>

                <div class="d-flex align-items-center">
                    <a href="<?= base_url('admin/login') ?>" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-user me-1"></i>Admin
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Breaking News Ticker (Outside Navigation) -->
    <?php if (!empty($breakingNews)): ?>
        <div class="breaking-news-ticker-container bg-danger">
            <div class="container">
                <div class="breaking-news-ticker py-2">
                    <span class="breaking-news-label">
                        <i class="fas fa-bolt me-1"></i>ब्रेकिंग न्यूज़
                    </span>
                    <div class="breaking-news-content">
                        <div class="breaking-news-scroll">
                            <?php foreach ($breakingNews as $index => $news): ?>
                                <span class="breaking-news-item <?= $index === 0 ? 'active' : '' ?>">
                                    <?php if ($news['link_url']): ?>
                                        <a href="<?= esc($news['link_url']) ?>" target="_blank" class="text-white text-decoration-none">
                                            <?= esc($news['title']) ?>
                                        </a>
                                    <?php else: ?>
                                        <?= esc($news['title']) ?>
                                    <?php endif; ?>
                                </span>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="py-4">
        <?= $this->renderSection('content') ?>
    </main>

    <!-- Footer Ads -->
    <?php if (!empty($footerAds)): ?>
        <div class="container-fluid bg-light py-3">
            <div class="container">
                <?php foreach ($footerAds as $ad): ?>
                    <div class="ad-banner">
                        <?php if ($ad['type'] === 'banner' && $ad['image']): ?>
                            <?php if ($ad['link_url']): ?>
                                <a href="<?= esc($ad['link_url']) ?>" target="_blank" onclick="trackAdClick(<?= $ad['id'] ?>)">
                                    <img src="<?= base_url('uploads/ads/' . $ad['image']) ?>"
                                        alt="<?= esc($ad['title']) ?>" class="img-fluid">
                                </a>
                            <?php else: ?>
                                <img src="<?= base_url('uploads/ads/' . $ad['image']) ?>"
                                    alt="<?= esc($ad['title']) ?>" class="img-fluid">
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Floating Ads -->
    <?php if (!empty($floatingAds)): ?>
        <?= view('components/ad_display', [
            'ads' => $floatingAds,
            'position' => 'floating'
        ]) ?>
    <?php endif; ?>

    <!-- Sticky Bottom Ads -->
    <?php if (!empty($stickyBottomAds)): ?>
        <?= view('components/ad_display', [
            'ads' => $stickyBottomAds,
            'position' => 'sticky_bottom'
        ]) ?>
    <?php endif; ?>

    <!-- Footer -->
    <footer class="footer py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><i class="fas fa-newspaper me-2"></i>BBC News Portal</h5>
                    <p>भारत की सबसे विश्वसनीय हिंदी न्यूज़ वेबसाइट। ताजा खबरें, राजनीति, खेल, मनोरंजन, तकनीक और व्यापार की खबरें।</p>
                </div>
                <div class="col-md-2">
                    <h6>श्रेणियां</h6>
                    <ul class="list-unstyled">
                        <?php if (!empty($categories)): ?>
                            <?php foreach (array_slice($categories, 0, 5) as $category): ?>
                                <li><a href="<?= base_url('category/' . $category['slug']) ?>" class="text-light text-decoration-none"><?= esc($category['name']) ?></a></li>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h6>संपर्क</h6>
                    <p><i class="fas fa-envelope me-2"></i><EMAIL></p>
                    <p><i class="fas fa-phone me-2"></i>+91 12345 67890</p>
                </div>
                <div class="col-md-3">
                    <h6>फॉलो करें</h6>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-light"><i class="fab fa-facebook fa-2x"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-twitter fa-2x"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-youtube fa-2x"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-instagram fa-2x"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p>&copy; 2024 BBC News Portal. All rights reserved. | Powered by CodeIgniter 4</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Track ad clicks
        function trackAdClick(adId) {
            fetch('<?= base_url("api/track-ad-click") ?>/' + adId, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
        }

        // Track ad impressions on page load
        document.addEventListener('DOMContentLoaded', function() {
            const ads = document.querySelectorAll('[data-ad-id]');
            ads.forEach(function(ad) {
                const adId = ad.getAttribute('data-ad-id');
                fetch('<?= base_url("api/track-ad-impression") ?>/' + adId, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
            });
        });
    </script>

    <?= $this->renderSection('scripts') ?>
</body>

</html>