<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-bolt me-2"></i>Breaking News Management</h2>
                <a href="<?= base_url('admin/breaking-news/create') ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Add Breaking News
                </a>
            </div>

            <!-- Success/Error Messages -->
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Breaking News Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>All Breaking News
                        <span class="badge bg-primary ms-2"><?= count($breakingNews) ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($breakingNews)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-bolt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No breaking news found</h5>
                            <p class="text-muted">Create your first breaking news to get started.</p>
                            <a href="<?= base_url('admin/breaking-news/create') ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Add Breaking News
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Title</th>
                                        <th>Priority</th>
                                        <th>Status</th>
                                        <th>Schedule</th>
                                        <th>Created By</th>
                                        <th>Created At</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($breakingNews as $item): ?>
                                        <tr>
                                            <td><?= $item['id'] ?></td>
                                            <td>
                                                <div class="fw-bold"><?= esc($item['title']) ?></div>
                                                <?php if ($item['content']): ?>
                                                    <small class="text-muted">
                                                        <?= esc(substr($item['content'], 0, 100)) ?>...
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php 
                                                $model = new \App\Models\BreakingNews();
                                                $priorityClass = $model->getPriorityBadgeClass($item['priority']);
                                                $priorityLabel = $model->getPriorityLabel($item['priority']);
                                                ?>
                                                <span class="badge <?= $priorityClass ?>"><?= $priorityLabel ?></span>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm <?= $item['status'] === 'active' ? 'btn-success' : 'btn-secondary' ?>" 
                                                        onclick="toggleStatus(<?= $item['id'] ?>)">
                                                    <i class="fas fa-<?= $item['status'] === 'active' ? 'check' : 'times' ?> me-1"></i>
                                                    <?= ucfirst($item['status']) ?>
                                                </button>
                                            </td>
                                            <td>
                                                <?php if ($item['start_time'] || $item['end_time']): ?>
                                                    <small>
                                                        <?php if ($item['start_time']): ?>
                                                            <div><strong>Start:</strong> <?= date('d M Y, H:i', strtotime($item['start_time'])) ?></div>
                                                        <?php endif; ?>
                                                        <?php if ($item['end_time']): ?>
                                                            <div><strong>End:</strong> <?= date('d M Y, H:i', strtotime($item['end_time'])) ?></div>
                                                        <?php endif; ?>
                                                    </small>
                                                <?php else: ?>
                                                    <span class="text-muted">No schedule</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= esc($item['created_by_name']) ?></td>
                                            <td>
                                                <small><?= date('d M Y, H:i', strtotime($item['created_at'])) ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('admin/breaking-news/edit/' . $item['id']) ?>" 
                                                       class="btn btn-sm btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button class="btn btn-sm btn-outline-danger" 
                                                            onclick="confirmDelete(<?= $item['id'] ?>)" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this breaking news? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="#" id="confirmDeleteBtn" class="btn btn-danger">Delete</a>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function confirmDelete(id) {
    const deleteUrl = '<?= base_url('admin/breaking-news/delete/') ?>' + id;
    document.getElementById('confirmDeleteBtn').href = deleteUrl;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function toggleStatus(id) {
    fetch('<?= base_url('admin/breaking-news/toggle-status/') ?>' + id, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating status');
    });
}
</script>
<?= $this->endSection() ?>
