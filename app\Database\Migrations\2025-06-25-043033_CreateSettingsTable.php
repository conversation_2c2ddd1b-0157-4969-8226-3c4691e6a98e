<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateSettingsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'setting_key' => [
                'type'       => 'VARCHAR',
                'constraint' => 100,
            ],
            'setting_value' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'setting_type' => [
                'type'       => 'ENUM',
                'constraint' => ['text', 'textarea', 'image', 'file', 'json', 'boolean'],
                'default'    => 'text',
            ],
            'category' => [
                'type'       => 'VARCHAR',
                'constraint' => 50,
                'default'    => 'general',
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'is_active' => [
                'type'       => 'TINYINT',
                'constraint' => 1,
                'default'    => 1,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey('setting_key');
        $this->forge->addKey('category');
        $this->forge->createTable('settings');

        // Insert default settings
        $defaultSettings = [
            // Site Information
            ['setting_key' => 'site_name', 'setting_value' => 'BBC News Portal', 'setting_type' => 'text', 'category' => 'site', 'description' => 'Website name'],
            ['setting_key' => 'site_tagline', 'setting_value' => 'Latest Hindi News Portal', 'setting_type' => 'text', 'category' => 'site', 'description' => 'Website tagline'],
            ['setting_key' => 'site_logo', 'setting_value' => '', 'setting_type' => 'image', 'category' => 'site', 'description' => 'Website logo'],
            ['setting_key' => 'site_favicon', 'setting_value' => '', 'setting_type' => 'image', 'category' => 'site', 'description' => 'Website favicon'],

            // Contact Information
            ['setting_key' => 'contact_email', 'setting_value' => '<EMAIL>', 'setting_type' => 'text', 'category' => 'contact', 'description' => 'Contact email address'],
            ['setting_key' => 'contact_phone', 'setting_value' => '+91-9876543210', 'setting_type' => 'text', 'category' => 'contact', 'description' => 'Contact phone number'],
            ['setting_key' => 'contact_address', 'setting_value' => 'New Delhi, India', 'setting_type' => 'textarea', 'category' => 'contact', 'description' => 'Contact address'],
            ['setting_key' => 'contact_whatsapp', 'setting_value' => '+91-9876543210', 'setting_type' => 'text', 'category' => 'contact', 'description' => 'WhatsApp number'],

            // Social Media
            ['setting_key' => 'facebook_url', 'setting_value' => '', 'setting_type' => 'text', 'category' => 'social', 'description' => 'Facebook page URL'],
            ['setting_key' => 'twitter_url', 'setting_value' => '', 'setting_type' => 'text', 'category' => 'social', 'description' => 'Twitter profile URL'],
            ['setting_key' => 'instagram_url', 'setting_value' => '', 'setting_type' => 'text', 'category' => 'social', 'description' => 'Instagram profile URL'],
            ['setting_key' => 'youtube_url', 'setting_value' => '', 'setting_type' => 'text', 'category' => 'social', 'description' => 'YouTube channel URL'],
            ['setting_key' => 'linkedin_url', 'setting_value' => '', 'setting_type' => 'text', 'category' => 'social', 'description' => 'LinkedIn profile URL'],
            ['setting_key' => 'telegram_url', 'setting_value' => '', 'setting_type' => 'text', 'category' => 'social', 'description' => 'Telegram channel URL'],

            // Footer
            ['setting_key' => 'footer_copyright', 'setting_value' => '© 2025 BBC News Portal. All rights reserved.', 'setting_type' => 'text', 'category' => 'footer', 'description' => 'Footer copyright text'],
            ['setting_key' => 'footer_description', 'setting_value' => 'Your trusted source for latest Hindi news and updates.', 'setting_type' => 'textarea', 'category' => 'footer', 'description' => 'Footer description'],

            // Analytics
            ['setting_key' => 'google_analytics_id', 'setting_value' => '', 'setting_type' => 'text', 'category' => 'analytics', 'description' => 'Google Analytics tracking ID'],
            ['setting_key' => 'google_tag_manager_id', 'setting_value' => '', 'setting_type' => 'text', 'category' => 'analytics', 'description' => 'Google Tag Manager ID'],
            ['setting_key' => 'facebook_pixel_id', 'setting_value' => '', 'setting_type' => 'text', 'category' => 'analytics', 'description' => 'Facebook Pixel ID'],

            // SEO
            ['setting_key' => 'meta_description', 'setting_value' => 'Latest Hindi news, breaking news, politics, sports, entertainment, technology and business news', 'setting_type' => 'textarea', 'category' => 'seo', 'description' => 'Default meta description'],
            ['setting_key' => 'meta_keywords', 'setting_value' => 'Hindi news, breaking news, राजनीति, खेल, मनोरंजन, तकनीक', 'setting_type' => 'textarea', 'category' => 'seo', 'description' => 'Default meta keywords'],
        ];

        foreach ($defaultSettings as $setting) {
            $setting['created_at'] = date('Y-m-d H:i:s');
            $setting['updated_at'] = date('Y-m-d H:i:s');
            $this->db->table('settings')->insert($setting);
        }
    }

    public function down()
    {
        $this->forge->dropTable('settings');
    }
}
