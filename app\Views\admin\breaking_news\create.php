<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-plus me-2"></i>Add Breaking News</h2>
                <a href="<?= base_url('admin/breaking-news') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to List
                </a>
            </div>

            <!-- Error Messages -->
            <?php if (session()->getFlashdata('errors')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <h6>Please fix the following errors:</h6>
                    <ul class="mb-0">
                        <?php foreach (session()->getFlashdata('errors') as $error): ?>
                            <li><?= esc($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Breaking News Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>Breaking News Details
                    </h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('admin/breaking-news/store') ?>" method="post">
                        <?= csrf_field() ?>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <!-- Title -->
                                <div class="mb-3">
                                    <label for="title" class="form-label">
                                        <i class="fas fa-heading me-2"></i>Breaking News Title (शीर्षक) *
                                    </label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="<?= old('title') ?>" required maxlength="500"
                                           placeholder="Enter breaking news title in Hindi">
                                    <div class="form-text">Maximum 500 characters</div>
                                </div>

                                <!-- Content -->
                                <div class="mb-3">
                                    <label for="content" class="form-label">
                                        <i class="fas fa-align-left me-2"></i>Additional Details (विवरण)
                                    </label>
                                    <textarea class="form-control" id="content" name="content" rows="4"
                                              placeholder="Optional additional details about the breaking news"><?= old('content') ?></textarea>
                                    <div class="form-text">Optional field for additional context</div>
                                </div>

                                <!-- Link URL -->
                                <div class="mb-3">
                                    <label for="link_url" class="form-label">
                                        <i class="fas fa-link me-2"></i>Link URL (लिंक)
                                    </label>
                                    <input type="url" class="form-control" id="link_url" name="link_url" 
                                           value="<?= old('link_url') ?>"
                                           placeholder="https://example.com/news-article">
                                    <div class="form-text">Optional link to full news article</div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- Priority -->
                                <div class="mb-3">
                                    <label for="priority" class="form-label">
                                        <i class="fas fa-exclamation-triangle me-2"></i>Priority Level *
                                    </label>
                                    <select class="form-select" id="priority" name="priority" required>
                                        <option value="">Select Priority</option>
                                        <option value="1" <?= old('priority') == '1' ? 'selected' : '' ?>>Low</option>
                                        <option value="2" <?= old('priority') == '2' ? 'selected' : '' ?>>Medium</option>
                                        <option value="3" <?= old('priority') == '3' ? 'selected' : '' ?>>High</option>
                                        <option value="4" <?= old('priority') == '4' ? 'selected' : '' ?>>Critical</option>
                                    </select>
                                    <div class="form-text">Higher priority appears first</div>
                                </div>

                                <!-- Status -->
                                <div class="mb-3">
                                    <label for="status" class="form-label">
                                        <i class="fas fa-toggle-on me-2"></i>Status *
                                    </label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="">Select Status</option>
                                        <option value="active" <?= old('status') == 'active' ? 'selected' : '' ?>>Active</option>
                                        <option value="inactive" <?= old('status') == 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                    </select>
                                </div>

                                <!-- Start Time -->
                                <div class="mb-3">
                                    <label for="start_time" class="form-label">
                                        <i class="fas fa-clock me-2"></i>Start Time
                                    </label>
                                    <input type="datetime-local" class="form-control" id="start_time" name="start_time" 
                                           value="<?= old('start_time') ?>">
                                    <div class="form-text">Leave empty for immediate start</div>
                                </div>

                                <!-- End Time -->
                                <div class="mb-3">
                                    <label for="end_time" class="form-label">
                                        <i class="fas fa-clock me-2"></i>End Time
                                    </label>
                                    <input type="datetime-local" class="form-control" id="end_time" name="end_time" 
                                           value="<?= old('end_time') ?>">
                                    <div class="form-text">Leave empty for no expiry</div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <a href="<?= base_url('admin/breaking-news') ?>" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Create Breaking News
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Set minimum datetime to current time
document.addEventListener('DOMContentLoaded', function() {
    const now = new Date();
    const currentDateTime = now.toISOString().slice(0, 16);
    
    const startTimeInput = document.getElementById('start_time');
    const endTimeInput = document.getElementById('end_time');
    
    startTimeInput.min = currentDateTime;
    endTimeInput.min = currentDateTime;
    
    // Update end time minimum when start time changes
    startTimeInput.addEventListener('change', function() {
        if (this.value) {
            endTimeInput.min = this.value;
        }
    });
});
</script>
<?= $this->endSection() ?>
