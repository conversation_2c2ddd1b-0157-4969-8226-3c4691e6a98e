<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-line me-2"></i>Analytics & Tracking Settings</h2>
                <a href="<?= base_url('admin/settings') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Settings
                </a>
            </div>

            <!-- Success/Error Messages -->
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-analytics me-2"></i>Tracking Codes Configuration
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="<?= base_url('admin/settings/analytics/update') ?>" method="post">
                                <?= csrf_field() ?>
                                
                                <!-- Google Analytics -->
                                <div class="mb-4">
                                    <label for="google_analytics_id" class="form-label">
                                        <i class="fab fa-google me-2"></i>Google Analytics Tracking ID
                                    </label>
                                    <input type="text" class="form-control" id="google_analytics_id" name="google_analytics_id" 
                                           value="<?= old('google_analytics_id', getSetting('google_analytics_id')) ?>" 
                                           placeholder="G-XXXXXXXXXX or UA-XXXXXXXXX-X">
                                    <div class="form-text">
                                        <strong>Format:</strong> G-XXXXXXXXXX (GA4) or UA-XXXXXXXXX-X (Universal Analytics)<br>
                                        <a href="https://analytics.google.com/" target="_blank" class="text-decoration-none">
                                            <i class="fas fa-external-link-alt me-1"></i>Get your Google Analytics ID
                                        </a>
                                    </div>
                                </div>

                                <!-- Google Tag Manager -->
                                <div class="mb-4">
                                    <label for="google_tag_manager_id" class="form-label">
                                        <i class="fab fa-google me-2"></i>Google Tag Manager Container ID
                                    </label>
                                    <input type="text" class="form-control" id="google_tag_manager_id" name="google_tag_manager_id" 
                                           value="<?= old('google_tag_manager_id', getSetting('google_tag_manager_id')) ?>" 
                                           placeholder="GTM-XXXXXXX">
                                    <div class="form-text">
                                        <strong>Format:</strong> GTM-XXXXXXX<br>
                                        <a href="https://tagmanager.google.com/" target="_blank" class="text-decoration-none">
                                            <i class="fas fa-external-link-alt me-1"></i>Get your GTM Container ID
                                        </a>
                                    </div>
                                </div>

                                <!-- Facebook Pixel -->
                                <div class="mb-4">
                                    <label for="facebook_pixel_id" class="form-label">
                                        <i class="fab fa-facebook me-2"></i>Facebook Pixel ID
                                    </label>
                                    <input type="text" class="form-control" id="facebook_pixel_id" name="facebook_pixel_id" 
                                           value="<?= old('facebook_pixel_id', getSetting('facebook_pixel_id')) ?>" 
                                           placeholder="123456789012345">
                                    <div class="form-text">
                                        <strong>Format:</strong> 15-digit number<br>
                                        <a href="https://business.facebook.com/events_manager" target="_blank" class="text-decoration-none">
                                            <i class="fas fa-external-link-alt me-1"></i>Get your Facebook Pixel ID
                                        </a>
                                    </div>
                                </div>

                                <!-- Submit Button -->
                                <div class="d-flex justify-content-between">
                                    <a href="<?= base_url('admin/settings') ?>" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Update Analytics Settings
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Current Status -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>Current Status
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="status-item mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span><i class="fab fa-google me-2"></i>Google Analytics</span>
                                    <?php if (getSetting('google_analytics_id')): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Not Set</span>
                                    <?php endif; ?>
                                </div>
                                <?php if (getSetting('google_analytics_id')): ?>
                                    <small class="text-muted">ID: <?= esc(getSetting('google_analytics_id')) ?></small>
                                <?php endif; ?>
                            </div>

                            <div class="status-item mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span><i class="fab fa-google me-2"></i>Tag Manager</span>
                                    <?php if (getSetting('google_tag_manager_id')): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Not Set</span>
                                    <?php endif; ?>
                                </div>
                                <?php if (getSetting('google_tag_manager_id')): ?>
                                    <small class="text-muted">ID: <?= esc(getSetting('google_tag_manager_id')) ?></small>
                                <?php endif; ?>
                            </div>

                            <div class="status-item mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span><i class="fab fa-facebook me-2"></i>Facebook Pixel</span>
                                    <?php if (getSetting('facebook_pixel_id')): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Not Set</span>
                                    <?php endif; ?>
                                </div>
                                <?php if (getSetting('facebook_pixel_id')): ?>
                                    <small class="text-muted">ID: <?= esc(getSetting('facebook_pixel_id')) ?></small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Setup Guidelines -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-book me-2"></i>Setup Guidelines
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled small mb-0">
                                <li class="mb-2">
                                    <strong>Google Analytics:</strong><br>
                                    <small class="text-muted">Track website traffic, user behavior, and conversions</small>
                                </li>
                                <li class="mb-2">
                                    <strong>Google Tag Manager:</strong><br>
                                    <small class="text-muted">Manage all tracking codes from one place</small>
                                </li>
                                <li class="mb-2">
                                    <strong>Facebook Pixel:</strong><br>
                                    <small class="text-muted">Track conversions from Facebook ads</small>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- Privacy Notice -->
                    <div class="card mt-3 border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>Privacy Notice
                            </h6>
                        </div>
                        <div class="card-body">
                            <small>
                                Ensure compliance with privacy laws (GDPR, CCPA) when using tracking codes. 
                                Consider implementing cookie consent banners and privacy policies.
                            </small>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-external-link-alt me-2"></i>Quick Links
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="https://analytics.google.com/" target="_blank" class="btn btn-outline-primary btn-sm">
                                    <i class="fab fa-google me-2"></i>Google Analytics
                                </a>
                                <a href="https://tagmanager.google.com/" target="_blank" class="btn btn-outline-info btn-sm">
                                    <i class="fab fa-google me-2"></i>Tag Manager
                                </a>
                                <a href="https://business.facebook.com/events_manager" target="_blank" class="btn btn-outline-primary btn-sm">
                                    <i class="fab fa-facebook me-2"></i>Facebook Events
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Format validation for tracking IDs
    const gaInput = document.getElementById('google_analytics_id');
    const gtmInput = document.getElementById('google_tag_manager_id');
    const fbInput = document.getElementById('facebook_pixel_id');
    
    // Google Analytics ID validation
    gaInput.addEventListener('blur', function() {
        const value = this.value.trim();
        if (value && !isValidGAId(value)) {
            this.setCustomValidity('Please enter a valid Google Analytics ID (G-XXXXXXXXXX or UA-XXXXXXXXX-X)');
        } else {
            this.setCustomValidity('');
        }
    });
    
    // Google Tag Manager ID validation
    gtmInput.addEventListener('blur', function() {
        const value = this.value.trim();
        if (value && !isValidGTMId(value)) {
            this.setCustomValidity('Please enter a valid GTM Container ID (GTM-XXXXXXX)');
        } else {
            this.setCustomValidity('');
        }
    });
    
    // Facebook Pixel ID validation
    fbInput.addEventListener('blur', function() {
        const value = this.value.trim();
        if (value && !isValidFBPixelId(value)) {
            this.setCustomValidity('Please enter a valid Facebook Pixel ID (15-digit number)');
        } else {
            this.setCustomValidity('');
        }
    });
    
    function isValidGAId(id) {
        // GA4: G-XXXXXXXXXX or Universal Analytics: UA-XXXXXXXXX-X
        return /^(G-[A-Z0-9]{10}|UA-\d{8,9}-\d{1,4})$/.test(id);
    }
    
    function isValidGTMId(id) {
        // GTM-XXXXXXX
        return /^GTM-[A-Z0-9]{7}$/.test(id);
    }
    
    function isValidFBPixelId(id) {
        // 15-digit number
        return /^\d{15}$/.test(id);
    }
});
</script>
<?= $this->endSection() ?>
