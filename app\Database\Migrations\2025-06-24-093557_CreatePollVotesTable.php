<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreatePollVotesTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'poll_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
            ],
            'option_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
            ],
            'voter_ip' => [
                'type'       => 'VARCHAR',
                'constraint' => 45,
            ],
            'voter_session' => [
                'type'       => 'VARCHAR',
                'constraint' => 128,
                'null'       => true,
            ],
            'user_agent' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'voted_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('poll_id');
        $this->forge->addKey('option_id');
        $this->forge->addKey('voter_ip');
        $this->forge->addKey('voter_session');
        $this->forge->addForeignKey('poll_id', 'polls', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('option_id', 'poll_options', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('poll_votes');
    }

    public function down()
    {
        $this->forge->dropTable('poll_votes');
    }
}
