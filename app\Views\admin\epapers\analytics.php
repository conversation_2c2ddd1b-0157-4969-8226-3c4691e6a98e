<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-line me-2"></i>Epaper Analytics</h2>
                <a href="<?= base_url('admin/epapers') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Epapers
                </a>
            </div>

            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= number_format($statistics['total_epapers']) ?></h4>
                                    <p class="mb-0">Total Epapers</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-newspaper fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= number_format($statistics['active_epapers']) ?></h4>
                                    <p class="mb-0">Active Epapers</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= number_format($statistics['total_views']) ?></h4>
                                    <p class="mb-0">Total Views</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-eye fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= number_format($statistics['total_downloads']) ?></h4>
                                    <p class="mb-0">Total Downloads</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-download fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Archive Overview -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-archive me-2"></i>Archive Overview
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($archiveYears)): ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Year</th>
                                                <th>Epapers</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($archiveYears as $yearData): ?>
                                                <tr>
                                                    <td><strong><?= $yearData['year'] ?></strong></td>
                                                    <td>
                                                        <span class="badge bg-primary">
                                                            <?= $yearData['count'] ?? 'N/A' ?> epapers
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <?php if ($yearData['year'] == date('Y')): ?>
                                                            <span class="badge bg-success">Current</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-secondary">Archived</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-3">
                                    <i class="fas fa-archive fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">No archive data available</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-pie me-2"></i>Quick Stats
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6 mb-3">
                                    <div class="border-end">
                                        <h5 class="text-primary mb-1">
                                            <?= $statistics['total_epapers'] > 0 ? number_format($statistics['total_views'] / $statistics['total_epapers'], 1) : '0' ?>
                                        </h5>
                                        <small class="text-muted">Avg Views/Epaper</small>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <h5 class="text-success mb-1">
                                        <?= $statistics['total_epapers'] > 0 ? number_format($statistics['total_downloads'] / $statistics['total_epapers'], 1) : '0' ?>
                                    </h5>
                                    <small class="text-muted">Avg Downloads/Epaper</small>
                                </div>
                                <div class="col-6">
                                    <div class="border-end">
                                        <h5 class="text-info mb-1">
                                            <?= $statistics['total_views'] > 0 ? number_format(($statistics['total_downloads'] / $statistics['total_views']) * 100, 1) : '0' ?>%
                                        </h5>
                                        <small class="text-muted">Download Rate</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h5 class="text-warning mb-1">
                                        <?= count($archiveYears) ?>
                                    </h5>
                                    <small class="text-muted">Archive Years</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Epapers Performance -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-trophy me-2"></i>Recent Epapers Performance
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($recentEpapers)): ?>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Title</th>
                                                <th>Publication Date</th>
                                                <th>Edition</th>
                                                <th>File Size</th>
                                                <th>Views</th>
                                                <th>Downloads</th>
                                                <th>Download Rate</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recentEpapers as $epaper): ?>
                                                <?php 
                                                $downloadRate = $epaper['view_count'] > 0 ? 
                                                    ($epaper['download_count'] / $epaper['view_count']) * 100 : 0;
                                                ?>
                                                <tr>
                                                    <td>
                                                        <div class="fw-bold"><?= esc($epaper['title']) ?></div>
                                                        <?php if ($epaper['featured']): ?>
                                                            <span class="badge bg-warning text-dark">Featured</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <strong><?= date('d M Y', strtotime($epaper['publication_date'])) ?></strong>
                                                        <br><small class="text-muted"><?= date('l', strtotime($epaper['publication_date'])) ?></small>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info"><?= esc($epaper['edition']) ?></span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-secondary"><?= formatBytes($epaper['file_size']) ?></span>
                                                        <?php if ($epaper['page_count']): ?>
                                                            <br><small class="text-muted"><?= $epaper['page_count'] ?> pages</small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-primary"><?= number_format($epaper['view_count']) ?></span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-success"><?= number_format($epaper['download_count']) ?></span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-<?= $downloadRate > 20 ? 'success' : ($downloadRate > 10 ? 'warning' : 'danger') ?>">
                                                            <?= number_format($downloadRate, 1) ?>%
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        $statusClass = match($epaper['status']) {
                                                            'active' => 'bg-success',
                                                            'inactive' => 'bg-secondary',
                                                            'archived' => 'bg-warning text-dark',
                                                            default => 'bg-secondary'
                                                        };
                                                        ?>
                                                        <span class="badge <?= $statusClass ?>"><?= ucfirst($epaper['status']) ?></span>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No epapers found</h5>
                                    <p class="text-muted">Upload your first epaper to see analytics.</p>
                                    <a href="<?= base_url('admin/epapers/create') ?>" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>Upload New Epaper
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Add any analytics-specific JavaScript here
document.addEventListener('DOMContentLoaded', function() {
    // You can add charts or interactive elements here
    console.log('Epaper Analytics loaded');
});
</script>
<?= $this->endSection() ?>
