<?php
// Try to get site settings safely
$siteSettings = [
    'site_name' => 'BBC News Portal',
    'site_logo' => '',
    'site_favicon' => ''
];

try {
    // Try to load settings from database if possible
    if (class_exists('\App\Models\Setting')) {
        $settingModel = new \App\Models\Setting();
        $dbSettings = $settingModel->getSiteSettings();
        if ($dbSettings) {
            $siteSettings = $dbSettings;
        }
    }
} catch (Exception $e) {
    // Use default settings if database not available
}

// Escape function for safety
if (!function_exists('esc')) {
    function esc($data)
    {
        return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    }
}

// Base URL function for safety
if (!function_exists('base_url')) {
    function base_url($path = '')
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $scriptName = dirname($_SERVER['SCRIPT_NAME']);
        $baseUrl = $protocol . '://' . $host . $scriptName;
        return rtrim($baseUrl, '/') . '/' . ltrim($path, '/');
    }
}
?>
<!DOCTYPE html>
<html lang="hi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Page Not Found | <?= esc($siteSettings['site_name']) ?></title>
    <meta name="description" content="The page you are looking for could not be found.">

    <!-- Favicon -->
    <?php if (!empty($siteSettings['site_favicon'])): ?>
        <link rel="icon" type="image/x-icon" href="<?= base_url('uploads/site/' . $siteSettings['site_favicon']) ?>">
    <?php endif; ?>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-red: #dc2626;
            --dark-red: #b91c1c;
            --primary-black: #000000;
            --white: #ffffff;
            --light-gray: #f9fafb;
            --medium-gray: #6b7280;
        }

        body {
            font-family: 'Noto Sans Devanagari', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--light-gray) 0%, #ffffff 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }

        .error-container {
            max-width: 800px;
            width: 100%;
            text-align: center;
            background: var(--white);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            padding: 60px 40px;
            position: relative;
            overflow: hidden;
        }

        .error-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, var(--primary-red), var(--dark-red));
        }

        .error-number {
            font-size: 8rem;
            font-weight: 900;
            color: var(--primary-red);
            line-height: 1;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
            animation: bounce 2s infinite;
        }

        @keyframes bounce {

            0%,
            20%,
            50%,
            80%,
            100% {
                transform: translateY(0);
            }

            40% {
                transform: translateY(-10px);
            }

            60% {
                transform: translateY(-5px);
            }
        }

        .error-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-black);
            margin-bottom: 20px;
            line-height: 1.2;
        }

        .error-subtitle {
            font-size: 1.2rem;
            color: var(--medium-gray);
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .error-icon {
            font-size: 4rem;
            color: var(--primary-red);
            margin-bottom: 30px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.1);
            }

            100% {
                transform: scale(1);
            }
        }

        .btn-home {
            background: linear-gradient(135deg, var(--primary-red), var(--dark-red));
            border: none;
            color: var(--white);
            font-weight: 700;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 1.1rem;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin: 10px;
            position: relative;
            overflow: hidden;
        }

        .btn-home::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-home:hover::before {
            left: 100%;
        }

        .btn-home:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(220, 38, 38, 0.4);
            color: var(--white);
            text-decoration: none;
        }

        .btn-secondary {
            background: transparent;
            border: 2px solid var(--primary-red);
            color: var(--primary-red);
            font-weight: 700;
            padding: 13px 38px;
            border-radius: 50px;
            font-size: 1.1rem;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin: 10px;
        }

        .btn-secondary:hover {
            background: var(--primary-red);
            color: var(--white);
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(220, 38, 38, 0.4);
            text-decoration: none;
        }

        .suggestions {
            margin-top: 50px;
            padding-top: 40px;
            border-top: 2px solid #e5e7eb;
        }

        .suggestions h4 {
            color: var(--primary-black);
            font-weight: 700;
            margin-bottom: 25px;
        }

        .suggestion-links {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
        }

        .suggestion-link {
            background: var(--light-gray);
            color: var(--primary-black);
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .suggestion-link:hover {
            background: var(--primary-red);
            color: var(--white);
            text-decoration: none;
            transform: translateY(-2px);
        }

        .logo {
            margin-bottom: 30px;
        }

        .logo img {
            height: 60px;
            width: auto;
        }

        @media (max-width: 768px) {
            .error-container {
                padding: 40px 20px;
                margin: 10px;
            }

            .error-number {
                font-size: 6rem;
            }

            .error-title {
                font-size: 2rem;
            }

            .error-subtitle {
                font-size: 1rem;
            }

            .btn-home,
            .btn-secondary {
                padding: 12px 30px;
                font-size: 1rem;
                margin: 5px;
            }

            .suggestion-links {
                flex-direction: column;
                align-items: center;
            }

            .suggestion-link {
                width: 100%;
                max-width: 250px;
                text-align: center;
            }
        }

        @media (max-width: 480px) {
            .error-number {
                font-size: 4.5rem;
            }

            .error-title {
                font-size: 1.5rem;
            }

            .logo img {
                height: 40px;
            }
        }
    </style>
</head>

<body>
    <div class="error-container">
        <!-- Logo -->
        <?php if (!empty($siteSettings['site_logo'])): ?>
            <div class="logo">
                <img src="<?= base_url('uploads/site/' . $siteSettings['site_logo']) ?>"
                    alt="<?= esc($siteSettings['site_name']) ?>"
                    title="<?= esc($siteSettings['site_name']) ?>">
            </div>
        <?php else: ?>
            <div class="logo">
                <i class="fas fa-newspaper" style="font-size: 3rem; color: var(--primary-red); margin-bottom: 20px;"
                    title="<?= esc($siteSettings['site_name']) ?>"></i>
            </div>
        <?php endif; ?>

        <!-- Error Icon -->
        <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>

        <!-- Error Number -->
        <div class="error-number">404</div>

        <!-- Error Title -->
        <h1 class="error-title">पेज नहीं मिला!</h1>
        <h2 class="error-title" style="font-size: 1.8rem; margin-top: -10px;">Page Not Found!</h2>

        <!-- Error Description -->
        <p class="error-subtitle">
            क्षमा करें, आपके द्वारा खोजा गया पेज उपलब्ध नहीं है। यह हटा दिया गया हो सकता है, नाम बदला गया हो सकता है, या अस्थायी रूप से अनुपलब्ध हो सकता है।
        </p>
        <p class="error-subtitle">
            Sorry, the page you are looking for could not be found. It may have been removed, renamed, or is temporarily unavailable.
        </p>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="<?= base_url() ?>" class="btn-home">
                <i class="fas fa-home me-2"></i>होम पेज पर जाएं
            </a>
            <a href="javascript:history.back()" class="btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>वापस जाएं
            </a>
        </div>

        <!-- Suggestions -->
        <div class="suggestions">
            <h4><i class="fas fa-lightbulb me-2"></i>आप यहाँ जा सकते हैं:</h4>
            <div class="suggestion-links">
                <a href="<?= base_url() ?>" class="suggestion-link">
                    <i class="fas fa-home me-2"></i>होम
                </a>
                <a href="<?= base_url('epaper') ?>" class="suggestion-link">
                    <i class="fas fa-newspaper me-2"></i>ई-पेपर
                </a>
                <a href="<?= base_url() ?>#breaking-news" class="suggestion-link">
                    <i class="fas fa-bolt me-2"></i>ब्रेकिंग न्यूज़
                </a>
                <a href="<?= base_url() ?>#latest-news" class="suggestion-link">
                    <i class="fas fa-clock me-2"></i>ताजा खबरें
                </a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>