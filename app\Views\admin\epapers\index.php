<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-newspaper me-2"></i>Epaper Management</h2>
                <div>
                    <a href="<?= base_url('admin/epapers/analytics') ?>" class="btn btn-info me-2">
                        <i class="fas fa-chart-line me-2"></i>Analytics
                    </a>
                    <a href="<?= base_url('admin/epapers/create') ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Upload New Epaper
                    </a>
                </div>
            </div>

            <!-- Success/Error Messages -->
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Epapers Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>All Epapers
                        <span class="badge bg-primary ms-2"><?= count($epapers) ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($epapers)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No epapers found</h5>
                            <p class="text-muted">Upload your first epaper to get started.</p>
                            <a href="<?= base_url('admin/epapers/create') ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Upload New Epaper
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Title</th>
                                        <th>Publication Date</th>
                                        <th>Edition</th>
                                        <th>Language</th>
                                        <th>File Info</th>
                                        <th>Statistics</th>
                                        <th>Status</th>
                                        <th>Uploaded By</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($epapers as $epaper): ?>
                                        <tr>
                                            <td><?= $epaper['id'] ?></td>
                                            <td>
                                                <div class="fw-bold"><?= esc($epaper['title']) ?></div>
                                                <?php if ($epaper['description']): ?>
                                                    <small class="text-muted">
                                                        <?= esc(substr($epaper['description'], 0, 80)) ?>...
                                                    </small>
                                                <?php endif; ?>
                                                <?php if ($epaper['featured']): ?>
                                                    <span class="badge bg-warning text-dark ms-2">Featured</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong><?= date('d M Y', strtotime($epaper['publication_date'])) ?></strong>
                                                <br><small class="text-muted"><?= date('l', strtotime($epaper['publication_date'])) ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?= esc($epaper['edition']) ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary"><?= esc($epaper['language']) ?></span>
                                            </td>
                                            <td>
                                                <div class="small">
                                                    <div><strong>Size:</strong> <?= formatBytes($epaper['file_size']) ?></div>
                                                    <?php if ($epaper['page_count']): ?>
                                                        <div><strong>Pages:</strong> <?= $epaper['page_count'] ?></div>
                                                    <?php endif; ?>
                                                    <?php if ($epaper['thumbnail']): ?>
                                                        <div><i class="fas fa-image text-success"></i> Thumbnail</div>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="small">
                                                    <div><i class="fas fa-eye text-primary"></i> <?= number_format($epaper['view_count']) ?> views</div>
                                                    <div><i class="fas fa-download text-success"></i> <?= number_format($epaper['download_count']) ?> downloads</div>
                                                </div>
                                            </td>
                                            <td>
                                                <?php
                                                $statusClass = match($epaper['status']) {
                                                    'active' => 'bg-success',
                                                    'inactive' => 'bg-secondary',
                                                    'archived' => 'bg-warning text-dark',
                                                    default => 'bg-secondary'
                                                };
                                                ?>
                                                <span class="badge <?= $statusClass ?>"><?= ucfirst($epaper['status']) ?></span>
                                            </td>
                                            <td>
                                                <?= esc($epaper['uploaded_by_name']) ?>
                                                <br><small class="text-muted"><?= date('d M Y', strtotime($epaper['created_at'])) ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('uploads/epapers/' . $epaper['pdf_file']) ?>" 
                                                       class="btn btn-sm btn-outline-success" title="View PDF" target="_blank">
                                                        <i class="fas fa-file-pdf"></i>
                                                    </a>
                                                    <a href="<?= base_url('admin/epapers/edit/' . $epaper['id']) ?>" 
                                                       class="btn btn-sm btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button class="btn btn-sm btn-outline-danger" 
                                                            onclick="confirmDelete(<?= $epaper['id'] ?>)" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this epaper? This action cannot be undone and will also delete the PDF file and thumbnail.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="#" id="confirmDeleteBtn" class="btn btn-danger">Delete</a>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function confirmDelete(id) {
    const deleteUrl = '<?= base_url('admin/epapers/delete/') ?>' + id;
    document.getElementById('confirmDeleteBtn').href = deleteUrl;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// Helper function to format bytes
<?php if (!function_exists('formatBytes')): ?>
    <?php 
    function formatBytes($size, $precision = 2) {
        $base = log($size, 1024);
        $suffixes = array('B', 'KB', 'MB', 'GB', 'TB');
        return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
    }
    ?>
<?php endif; ?>
</script>
<?= $this->endSection() ?>
