<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddVideoFieldsToNewsTable extends Migration
{
    public function up()
    {
        $this->forge->addColumn('news', [
            'media_type' => [
                'type'       => 'ENUM',
                'constraint' => ['image', 'video'],
                'default'    => 'image',
                'null'       => false,
                'after'      => 'content'
            ],
            'youtube_url' => [
                'type'       => 'VARCHAR',
                'constraint' => 500,
                'null'       => true,
                'after'      => 'media_type'
            ],
            'youtube_id' => [
                'type'       => 'VARCHAR',
                'constraint' => 50,
                'null'       => true,
                'after'      => 'youtube_url'
            ]
        ]);
    }

    public function down()
    {
        $this->forge->dropColumn('news', ['media_type', 'youtube_url', 'youtube_id']);
    }
}
