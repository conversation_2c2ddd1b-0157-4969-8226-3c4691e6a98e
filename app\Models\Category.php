<?php

namespace App\Models;

use CodeIgniter\Model;

class Category extends Model
{
    protected $table            = 'categories';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['name', 'slug', 'description', 'status'];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'name'   => 'required|min_length[2]|max_length[255]',
        'slug'   => 'required|min_length[2]|max_length[255]',
        'status' => 'required|in_list[active,inactive]',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['generateSlug'];
    protected $afterInsert    = [];
    protected $beforeUpdate   = ['generateSlug'];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    protected function generateSlug(array $data)
    {
        // Only generate slug if it's not provided or is empty
        if (isset($data['data']['name']) && (!isset($data['data']['slug']) || empty($data['data']['slug']))) {
            // Use transliteration for Hindi names
            $slug = transliterate_to_english($data['data']['name']);

            // If transliteration didn't work well, use a simple approach
            if (empty($slug) || strlen($slug) < 2) {
                // Generate a simple slug based on timestamp
                $slug = 'category-' . time() . '-' . rand(100, 999);
            }

            // Ensure slug is unique
            $originalSlug = $slug;
            $counter = 1;

            // Get the ID if this is an update operation
            $excludeId = isset($data['id']) ? $data['id'] : null;

            while ($this->slugExists($slug, $excludeId)) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            $data['data']['slug'] = $slug;
        }
        return $data;
    }

    /**
     * Check if slug exists in database
     */
    private function slugExists($slug, $excludeId = null)
    {
        $builder = $this->where('slug', $slug);

        if ($excludeId) {
            $builder->where('id !=', $excludeId);
        }

        return $builder->countAllResults() > 0;
    }
}
