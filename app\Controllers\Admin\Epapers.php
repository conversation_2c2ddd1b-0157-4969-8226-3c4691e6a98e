<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\Epaper;

class Epapers extends BaseController
{
    protected $epaperModel;

    public function __construct()
    {
        $this->epaperModel = new Epaper();
    }

    public function index()
    {
        $data = [
            'title' => 'Epaper Management',
            'epapers' => $this->epaperModel->getEpapersWithDetails()
        ];

        return view('admin/epapers/index', $data);
    }

    public function create()
    {
        $data = [
            'title' => 'Upload New Epaper'
        ];

        return view('admin/epapers/create', $data);
    }

    public function store()
    {
        $rules = [
            'title'            => 'required|min_length[5]|max_length[500]',
            'publication_date' => 'required|valid_date',
            'edition'          => 'required|max_length[100]',
            'language'         => 'required|max_length[50]',
            'status'           => 'required|in_list[active,inactive,archived]',
            'pdf_file'         => 'uploaded[pdf_file]|ext_in[pdf_file,pdf]|max_size[pdf_file,51200]', // 50MB max
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Handle PDF file upload
        $pdfFile = $this->request->getFile('pdf_file');
        if (!$pdfFile->isValid()) {
            return redirect()->back()->withInput()->with('error', 'Invalid PDF file uploaded');
        }

        // Create upload directory if it doesn't exist
        $uploadPath = FCPATH . 'uploads/epapers/';
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        // Generate unique filename
        $fileName = date('Y-m-d') . '_' . time() . '_' . $pdfFile->getRandomName();

        // Move uploaded file
        if (!$pdfFile->move($uploadPath, $fileName)) {
            return redirect()->back()->withInput()->with('error', 'Failed to upload PDF file');
        }

        // Handle thumbnail upload if provided
        $thumbnailName = null;
        $thumbnailFile = $this->request->getFile('thumbnail');
        if ($thumbnailFile && $thumbnailFile->isValid()) {
            $thumbnailPath = FCPATH . 'uploads/epapers/thumbnails/';
            if (!is_dir($thumbnailPath)) {
                mkdir($thumbnailPath, 0755, true);
            }

            $thumbnailName = 'thumb_' . time() . '_' . $thumbnailFile->getRandomName();
            $thumbnailFile->move($thumbnailPath, $thumbnailName);
        }

        // Get file size
        $fileSize = filesize($uploadPath . $fileName);

        // Prepare data for database
        $data = [
            'title'            => $this->request->getPost('title'),
            'description'      => $this->request->getPost('description'),
            'publication_date' => $this->request->getPost('publication_date'),
            'edition'          => $this->request->getPost('edition'),
            'language'         => $this->request->getPost('language'),
            'pdf_file'         => $fileName,
            'thumbnail'        => $thumbnailName,
            'file_size'        => $fileSize,
            'page_count'       => $this->request->getPost('page_count') ?: 0,
            'status'           => $this->request->getPost('status'),
            'featured'         => $this->request->getPost('featured') ? 1 : 0,
            'uploaded_by'      => session()->get('user_id'),
        ];

        try {
            if ($this->epaperModel->save($data)) {
                return redirect()->to('/admin/epapers')->with('success', 'Epaper uploaded successfully');
            } else {
                // Delete uploaded files if database save fails
                if (file_exists($uploadPath . $fileName)) {
                    unlink($uploadPath . $fileName);
                }
                if ($thumbnailName && file_exists($thumbnailPath . $thumbnailName)) {
                    unlink($thumbnailPath . $thumbnailName);
                }

                $errors = $this->epaperModel->errors();
                if (!empty($errors)) {
                    return redirect()->back()->withInput()->with('errors', $errors);
                } else {
                    return redirect()->back()->withInput()->with('error', 'Failed to save epaper. Please try again.');
                }
            }
        } catch (\Exception $e) {
            log_message('error', 'Epaper upload error: ' . $e->getMessage());

            // Delete uploaded files on error
            if (file_exists($uploadPath . $fileName)) {
                unlink($uploadPath . $fileName);
            }
            if ($thumbnailName && file_exists($thumbnailPath . $thumbnailName)) {
                unlink($thumbnailPath . $thumbnailName);
            }

            return redirect()->back()->withInput()->with('error', 'An error occurred while uploading the epaper: ' . $e->getMessage());
        }
    }

    public function edit($id)
    {
        $epaper = $this->epaperModel->find($id);

        if (!$epaper) {
            return redirect()->to('/admin/epapers')->with('error', 'Epaper not found');
        }

        $data = [
            'title' => 'Edit Epaper',
            'epaper' => $epaper
        ];

        return view('admin/epapers/edit', $data);
    }

    public function update($id)
    {
        $epaper = $this->epaperModel->find($id);

        if (!$epaper) {
            return redirect()->to('/admin/epapers')->with('error', 'Epaper not found');
        }

        $rules = [
            'title'            => 'required|min_length[5]|max_length[500]',
            'publication_date' => 'required|valid_date',
            'edition'          => 'required|max_length[100]',
            'language'         => 'required|max_length[50]',
            'status'           => 'required|in_list[active,inactive,archived]',
        ];

        // Only validate PDF if a new one is uploaded
        $pdfFile = $this->request->getFile('pdf_file');
        if ($pdfFile && $pdfFile->isValid()) {
            $rules['pdf_file'] = 'ext_in[pdf_file,pdf]|max_size[pdf_file,51200]';
        }

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare update data
        $data = [
            'title'            => $this->request->getPost('title'),
            'description'      => $this->request->getPost('description'),
            'publication_date' => $this->request->getPost('publication_date'),
            'edition'          => $this->request->getPost('edition'),
            'language'         => $this->request->getPost('language'),
            'page_count'       => $this->request->getPost('page_count') ?: 0,
            'status'           => $this->request->getPost('status'),
            'featured'         => $this->request->getPost('featured') ? 1 : 0,
        ];

        $uploadPath = FCPATH . 'uploads/epapers/';
        $thumbnailPath = FCPATH . 'uploads/epapers/thumbnails/';

        // Handle new PDF file upload
        if ($pdfFile && $pdfFile->isValid()) {
            // Delete old PDF file
            if ($epaper['pdf_file'] && file_exists($uploadPath . $epaper['pdf_file'])) {
                unlink($uploadPath . $epaper['pdf_file']);
            }

            // Upload new PDF file
            $fileName = date('Y-m-d') . '_' . time() . '_' . $pdfFile->getRandomName();
            if (!$pdfFile->move($uploadPath, $fileName)) {
                return redirect()->back()->withInput()->with('error', 'Failed to upload new PDF file');
            }

            $data['pdf_file'] = $fileName;
            $data['file_size'] = filesize($uploadPath . $fileName);
        }

        // Handle new thumbnail upload
        $thumbnailFile = $this->request->getFile('thumbnail');
        if ($thumbnailFile && $thumbnailFile->isValid()) {
            // Delete old thumbnail
            if ($epaper['thumbnail'] && file_exists($thumbnailPath . $epaper['thumbnail'])) {
                unlink($thumbnailPath . $epaper['thumbnail']);
            }

            // Upload new thumbnail
            if (!is_dir($thumbnailPath)) {
                mkdir($thumbnailPath, 0755, true);
            }

            $thumbnailName = 'thumb_' . time() . '_' . $thumbnailFile->getRandomName();
            if ($thumbnailFile->move($thumbnailPath, $thumbnailName)) {
                $data['thumbnail'] = $thumbnailName;
            }
        }

        try {
            if ($this->epaperModel->update($id, $data)) {
                return redirect()->to('/admin/epapers')->with('success', 'Epaper updated successfully');
            } else {
                $errors = $this->epaperModel->errors();
                if (!empty($errors)) {
                    return redirect()->back()->withInput()->with('errors', $errors);
                } else {
                    return redirect()->back()->withInput()->with('error', 'Failed to update epaper. Please try again.');
                }
            }
        } catch (\Exception $e) {
            log_message('error', 'Epaper update error: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while updating the epaper: ' . $e->getMessage());
        }
    }

    public function delete($id)
    {
        $epaper = $this->epaperModel->find($id);

        if (!$epaper) {
            return redirect()->to('/admin/epapers')->with('error', 'Epaper not found');
        }

        try {
            if ($this->epaperModel->delete($id)) {
                // Delete associated files
                $uploadPath = FCPATH . 'uploads/epapers/';
                $thumbnailPath = FCPATH . 'uploads/epapers/thumbnails/';

                if ($epaper['pdf_file'] && file_exists($uploadPath . $epaper['pdf_file'])) {
                    unlink($uploadPath . $epaper['pdf_file']);
                }

                if ($epaper['thumbnail'] && file_exists($thumbnailPath . $epaper['thumbnail'])) {
                    unlink($thumbnailPath . $epaper['thumbnail']);
                }

                return redirect()->to('/admin/epapers')->with('success', 'Epaper deleted successfully');
            } else {
                return redirect()->to('/admin/epapers')->with('error', 'Failed to delete epaper');
            }
        } catch (\Exception $e) {
            log_message('error', 'Epaper delete error: ' . $e->getMessage());
            return redirect()->to('/admin/epapers')->with('error', 'An error occurred while deleting the epaper');
        }
    }

    public function analytics()
    {
        $statistics = $this->epaperModel->getStatistics();
        $recentEpapers = $this->epaperModel->getEpapersWithDetails(10);
        $archiveYears = $this->epaperModel->getArchiveYears();

        $data = [
            'title' => 'Epaper Analytics',
            'statistics' => $statistics,
            'recentEpapers' => $recentEpapers,
            'archiveYears' => $archiveYears
        ];

        return view('admin/epapers/analytics', $data);
    }
}
