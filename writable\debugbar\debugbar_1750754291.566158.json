{"url": "http://localhost/bbc_news/public/index.php/", "method": "GET", "isAJAX": false, "startTime": **********.444179, "totalTime": 81.60000000000001, "totalMemory": "6.284", "segmentDuration": 15, "segmentCount": 6, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.44731, "duration": 0.018360137939453125}, {"name": "Required Before Filters", "component": "Timer", "start": **********.465672, "duration": 0.0036199092864990234}, {"name": "Routing", "component": "Timer", "start": **********.469298, "duration": 0.0006701946258544922}, {"name": "Before Filters", "component": "Timer", "start": **********.470157, "duration": 2.2172927856445312e-05}, {"name": "Controller", "component": "Timer", "start": **********.47018, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.470181, "duration": 0.006339073181152344}, {"name": "After Filters", "component": "Timer", "start": **********.525378, "duration": 5.9604644775390625e-06}, {"name": "Required After Filters", "component": "Timer", "start": **********.525409, "duration": 0.0003650188446044922}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(6 total Queries, 6 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "30.48 ms", "sql": "<strong>SELECT</strong> `news`.*, `categories`.`name` as `category_name`, `users`.`full_name` as `author_name`\n<strong>FROM</strong> `news`\n<strong>JOIN</strong> `categories` <strong>ON</strong> `categories`.`id` = `news`.`category_id`\n<strong>JOIN</strong> `users` <strong>ON</strong> `users`.`id` = `news`.`author_id`\n<strong>WHERE</strong> `news`.`featured` = 1\n<strong>AND</strong> `news`.`status` = &#039;published&#039;\n<strong>ORDER</strong> <strong>BY</strong> `news`.`created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 5", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\News.php:109", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Home.php:26", "function": "        App\\Models\\News->getFeaturedNews()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\News.php:109", "qid": "f6e3f01d4a7d3f0486dfaa02ddc92a6e"}, {"hover": "", "class": "", "duration": "7.46 ms", "sql": "<strong>SELECT</strong> `news`.*, `categories`.`name` as `category_name`, `users`.`full_name` as `author_name`\n<strong>FROM</strong> `news`\n<strong>JOIN</strong> `categories` <strong>ON</strong> `categories`.`id` = `news`.`category_id`\n<strong>JOIN</strong> `users` <strong>ON</strong> `users`.`id` = `news`.`author_id`\n<strong>WHERE</strong> `news`.`status` = &#039;published&#039;\n<strong>ORDER</strong> <strong>BY</strong> `news`.`created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 10", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\News.php:81", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Home.php:27", "function": "        App\\Models\\News->getNewsWithDetails()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\News.php:81", "qid": "5670c9e666fced0b12071ce7584dc59d"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `categories`\n<strong>WHERE</strong> `status` = &#039;active&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Home.php:28", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\Home.php:28", "qid": "23843d2eaf6d3d9c06ec205d543e0b59"}, {"hover": "", "class": "", "duration": "0.16 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `ads`\n<strong>WHERE</strong> `position` = &#039;header&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n<strong>AND</strong> (start_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> start_date &lt;= NOW())\n<strong>AND</strong> (end_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> end_date &gt;= NOW())", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\Ad.php:58", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Home.php:29", "function": "        App\\Models\\Ad->getActiveAdsByPosition()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\Ad.php:58", "qid": "748ce18c59811806262207c7d9a6b876"}, {"hover": "", "class": "", "duration": "0.12 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `ads`\n<strong>WHERE</strong> `position` = &#039;sidebar&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n<strong>AND</strong> (start_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> start_date &lt;= NOW())\n<strong>AND</strong> (end_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> end_date &gt;= NOW())", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\Ad.php:58", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Home.php:30", "function": "        App\\Models\\Ad->getActiveAdsByPosition()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\Ad.php:58", "qid": "c6f7b05b66a3c2f6f2e6c40a374dd450"}, {"hover": "", "class": "", "duration": "0.11 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `ads`\n<strong>WHERE</strong> `position` = &#039;footer&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n<strong>AND</strong> (start_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> start_date &lt;= NOW())\n<strong>AND</strong> (end_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> end_date &gt;= NOW())", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\Ad.php:58", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Home.php:31", "function": "        App\\Models\\Ad->getActiveAdsByPosition()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\Ad.php:58", "qid": "fe841f795eeb6e3aa27aea68b3598367"}]}, "badgeValue": 6, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.479779, "duration": "0.000775"}, {"name": "Query", "component": "Database", "start": **********.481101, "duration": "0.030479", "query": "<strong>SELECT</strong> `news`.*, `categories`.`name` as `category_name`, `users`.`full_name` as `author_name`\n<strong>FROM</strong> `news`\n<strong>JOIN</strong> `categories` <strong>ON</strong> `categories`.`id` = `news`.`category_id`\n<strong>JOIN</strong> `users` <strong>ON</strong> `users`.`id` = `news`.`author_id`\n<strong>WHERE</strong> `news`.`featured` = 1\n<strong>AND</strong> `news`.`status` = &#039;published&#039;\n<strong>ORDER</strong> <strong>BY</strong> `news`.`created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 5"}, {"name": "Query", "component": "Database", "start": **********.512803, "duration": "0.007460", "query": "<strong>SELECT</strong> `news`.*, `categories`.`name` as `category_name`, `users`.`full_name` as `author_name`\n<strong>FROM</strong> `news`\n<strong>JOIN</strong> `categories` <strong>ON</strong> `categories`.`id` = `news`.`category_id`\n<strong>JOIN</strong> `users` <strong>ON</strong> `users`.`id` = `news`.`author_id`\n<strong>WHERE</strong> `news`.`status` = &#039;published&#039;\n<strong>ORDER</strong> <strong>BY</strong> `news`.`created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 10"}, {"name": "Query", "component": "Database", "start": **********.520492, "duration": "0.000249", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `categories`\n<strong>WHERE</strong> `status` = &#039;active&#039;"}, {"name": "Query", "component": "Database", "start": **********.520859, "duration": "0.000156", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `ads`\n<strong>WHERE</strong> `position` = &#039;header&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n<strong>AND</strong> (start_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> start_date &lt;= NOW())\n<strong>AND</strong> (end_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> end_date &gt;= NOW())"}, {"name": "Query", "component": "Database", "start": **********.521086, "duration": "0.000120", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `ads`\n<strong>WHERE</strong> `position` = &#039;sidebar&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n<strong>AND</strong> (start_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> start_date &lt;= NOW())\n<strong>AND</strong> (end_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> end_date &gt;= NOW())"}, {"name": "Query", "component": "Database", "start": **********.521305, "duration": "0.000106", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `ads`\n<strong>WHERE</strong> `position` = &#039;footer&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n<strong>AND</strong> (start_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> start_date &lt;= NOW())\n<strong>AND</strong> (end_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> end_date &gt;= NOW())"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": []}, "badgeValue": null, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 2, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: frontend/layout.php", "component": "Views", "start": **********.524498, "duration": 0.0007140636444091797}, {"name": "View: frontend/home.php", "component": "Views", "start": **********.523223, "duration": 0.0020999908447265625}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 154 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH\\Database\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH\\Database\\Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\I18n\\Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH\\Controllers\\Home.php", "name": "Home.php"}, {"path": "APPPATH\\Helpers\\hindi_helper.php", "name": "hindi_helper.php"}, {"path": "APPPATH\\Models\\Ad.php", "name": "Ad.php"}, {"path": "APPPATH\\Models\\Category.php", "name": "Category.php"}, {"path": "APPPATH\\Models\\News.php", "name": "News.php"}, {"path": "APPPATH\\Views\\frontend\\home.php", "name": "home.php"}, {"path": "APPPATH\\Views\\frontend\\layout.php", "name": "layout.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}, {"path": "VENDORPATH\\autoload.php", "name": "autoload.php"}, {"path": "VENDORPATH\\composer\\ClassLoader.php", "name": "ClassLoader.php"}, {"path": "VENDORPATH\\composer\\InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "VENDORPATH\\composer\\autoload_real.php", "name": "autoload_real.php"}, {"path": "VENDORPATH\\composer\\autoload_static.php", "name": "autoload_static.php"}, {"path": "VENDORPATH\\composer\\installed.php", "name": "installed.php"}, {"path": "VENDORPATH\\composer\\platform_check.php", "name": "platform_check.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\Escaper.php", "name": "Escaper.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "VENDORPATH\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php", "name": "deep_copy.php"}, {"path": "VENDORPATH\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php", "name": "Functions.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LogLevel.php", "name": "LogLevel.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "VENDORPATH\\symfony\\deprecation-contracts\\function.php", "name": "function.php"}]}, "badgeValue": 154, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Home", "method": "index", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "news/([^/]+)", "handler": "\\App\\Controllers\\Home::news/$1"}, {"method": "GET", "route": "category/([^/]+)", "handler": "\\App\\Controllers\\Home::category/$1"}, {"method": "GET", "route": "admin/login", "handler": "\\App\\Controllers\\Auth::login"}, {"method": "GET", "route": "admin/logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "admin/dashboard", "handler": "\\App\\Controllers\\Admin\\Dashboard::index"}, {"method": "GET", "route": "admin/users", "handler": "\\App\\Controllers\\Admin\\Users::index"}, {"method": "GET", "route": "admin/users/create", "handler": "\\App\\Controllers\\Admin\\Users::create"}, {"method": "GET", "route": "admin/users/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Users::edit/$1"}, {"method": "GET", "route": "admin/users/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Users::delete/$1"}, {"method": "GET", "route": "admin/news", "handler": "\\App\\Controllers\\Admin\\News::index"}, {"method": "GET", "route": "admin/news/create", "handler": "\\App\\Controllers\\Admin\\News::create"}, {"method": "GET", "route": "admin/news/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\News::edit/$1"}, {"method": "GET", "route": "admin/news/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\News::delete/$1"}, {"method": "GET", "route": "admin/categories", "handler": "\\App\\Controllers\\Admin\\Categories::index"}, {"method": "GET", "route": "admin/categories/create", "handler": "\\App\\Controllers\\Admin\\Categories::create"}, {"method": "GET", "route": "admin/categories/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Categories::edit/$1"}, {"method": "GET", "route": "admin/categories/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Categories::delete/$1"}, {"method": "GET", "route": "admin/tags", "handler": "\\App\\Controllers\\Admin\\Tags::index"}, {"method": "GET", "route": "admin/tags/create", "handler": "\\App\\Controllers\\Admin\\Tags::create"}, {"method": "GET", "route": "admin/tags/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Tags::edit/$1"}, {"method": "GET", "route": "admin/tags/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Tags::delete/$1"}, {"method": "GET", "route": "admin/ads", "handler": "\\App\\Controllers\\Admin\\Ads::index"}, {"method": "GET", "route": "admin/ads/create", "handler": "\\App\\Controllers\\Admin\\Ads::create"}, {"method": "GET", "route": "admin/ads/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Ads::edit/$1"}, {"method": "GET", "route": "admin/ads/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Ads::delete/$1"}, {"method": "POST", "route": "api/track-ad-click/([0-9]+)", "handler": "\\App\\Controllers\\Api\\AdTracking::trackClick/$1"}, {"method": "POST", "route": "api/track-ad-impression/([0-9]+)", "handler": "\\App\\Controllers\\Api\\AdTracking::trackImpression/$1"}, {"method": "POST", "route": "admin/authenticate", "handler": "\\App\\Controllers\\Auth::authenticate"}, {"method": "POST", "route": "admin/users/store", "handler": "\\App\\Controllers\\Admin\\Users::store"}, {"method": "POST", "route": "admin/users/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Users::update/$1"}, {"method": "POST", "route": "admin/news/store", "handler": "\\App\\Controllers\\Admin\\News::store"}, {"method": "POST", "route": "admin/news/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\News::update/$1"}, {"method": "POST", "route": "admin/categories/store", "handler": "\\App\\Controllers\\Admin\\Categories::store"}, {"method": "POST", "route": "admin/categories/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Categories::update/$1"}, {"method": "POST", "route": "admin/tags/store", "handler": "\\App\\Controllers\\Admin\\Tags::store"}, {"method": "POST", "route": "admin/tags/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Tags::update/$1"}, {"method": "POST", "route": "admin/ads/store", "handler": "\\App\\Controllers\\Admin\\Ads::store"}, {"method": "POST", "route": "admin/ads/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Ads::update/$1"}]}, "badgeValue": 27, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "6.28", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "0.12", "count": 6}}}, "badgeValue": 7, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.459382, "duration": 0.0062830448150634766}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.511589, "duration": 3.3855438232421875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.520269, "duration": 3.910064697265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.520744, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.521017, "duration": 8.821487426757812e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.521209, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.521413, "duration": 1.6927719116210938e-05}]}], "vars": {"varData": {"View Data": {"title": "BBC News Portal - Latest Hindi News", "featuredNews": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (3)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (3)</li><li>Contents (3)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>title</th><th>slug</th><th>description</th><th>content</th><th>image</th><th>category_id</th><th>author_id</th><th>status</th><th>featured</th><th>views</th><th>published_at</th><th>created_at</th><th>updated_at</th><th>category_name</th><th>author_name</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">1</td><td title=\"UTF-8 string (90)\">&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367; &#2325;&#2366; &#2344;&#2351;&#2366; &#2342;&#2380;&#2352;</td><td title=\"string (40)\">bharat-mein-takneeki-kranti-ka-naya-daur</td><td title=\"UTF-8 string (216)\">&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2361;&#2379; &#2352;&#2361;&#2375; &#2344;&#2357;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2368; &#2360;&#2347;&#2354;&#2340;&#2366; &#2325;&#2368;UTF-8</td><td title=\"UTF-8 string (930)\">&#2349;&#2366;&#2352;&#2340; &#2310;&#2332; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367; &#2325;&#2375; &#2319;&#2325; &#2344;&#2319; &#2342;&#2380;&#2352; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2357;&#2375;&#2358; &#2325;&#2352; &#2352;&#2361;&#2366; &#2361;&#2376;&#2404; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;UTF-8</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">4</td><td title=\"string (1)\">1</td><td title=\"string (9)\">published</td><td title=\"string (1)\">1</td><td title=\"string (4)\">1250</td><td title=\"string (19)\">2025-06-24 06:19:54</td><td title=\"string (19)\">2025-06-24 06:19:54</td><td title=\"string (19)\">2025-06-24 06:19:54</td><td title=\"UTF-8 string (15)\">&#2340;&#2325;&#2344;&#2368;&#2325;</td><td title=\"string (13)\">Administrator</td></tr><tr><th>1</th><td title=\"string (1)\">2</td><td title=\"UTF-8 string (103)\">&#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2332;&#2368;&#2340;</td><td title=\"string (46)\">cricket-vishwa-cup-mein-bharat-ki-shandar-jeet</td><td title=\"UTF-8 string (225)\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;&#2366;&#2348; &#2309;&#2346;&#2344;&#2375;UTF-8</td><td title=\"UTF-8 string (853)\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2310;&#2332; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2376;&#2330; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;UTF-8</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">2</td><td title=\"string (1)\">2</td><td title=\"string (9)\">published</td><td title=\"string (1)\">1</td><td title=\"string (4)\">2100</td><td title=\"string (19)\">2025-06-24 04:19:54</td><td title=\"string (19)\">2025-06-24 04:19:54</td><td title=\"string (19)\">2025-06-24 04:19:54</td><td title=\"UTF-8 string (9)\">&#2326;&#2375;&#2354;</td><td title=\"string (12)\">News Manager</td></tr><tr><th>2</th><td title=\"string (1)\">5</td><td title=\"UTF-8 string (101)\">&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340;</td><td title=\"string (45)\">rajya-sabha-mein-mahattvapurna-vidheyak-parit</td><td title=\"UTF-8 string (182)\">&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2310;&#2332; &#2319;&#2325; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2332;&#2379; &#2332;&#2344;&#2340;&#2366; &#2325;&#2375; &#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2361;&#2376;&#2404;</td><td title=\"UTF-8 string (693)\">&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2310;&#2332; &#2319;&#2325; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2361;&#2376; &#2332;&#2379; &#2310;&#2350; &#2332;&#2344;&#2340;&#2366; &#2325;&#2375; &#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2361;&#2376;UTF-8</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"string (9)\">published</td><td title=\"string (1)\">1</td><td title=\"string (4)\">1800</td><td title=\"string (19)\">2025-06-23 22:19:54</td><td title=\"string (19)\">2025-06-23 22:19:54</td><td title=\"string (19)\">2025-06-23 22:19:54</td><td title=\"UTF-8 string (21)\">&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;</td><td title=\"string (13)\">Administrator</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (90) \"&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367; &#2325;&#2366; &#2344;&#2351;&#2366; &#2342;&#2380;&#2352;\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (40) \"bharat-mein-takneeki-kranti-ka-naya-daur\"<div class=\"access-path\">$value[0]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (216) \"&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2361;&#2379; &#2352;&#2361;&#2375; &#2344;&#2357;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2368; &#2360;&#2347;&#2354;&#2340;&#2366; &#2325;&#2368; ...<div class=\"access-path\">$value[0]['description']</div></dt><dd><pre>&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2361;&#2379; &#2352;&#2361;&#2375; &#2344;&#2357;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2368; &#2360;&#2347;&#2354;&#2340;&#2366; &#2325;&#2368; &#2325;&#2361;&#2366;&#2344;&#2368;&#2404;\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>content</dfn> =&gt; <var>UTF-8 string</var> (930) \"&#2349;&#2366;&#2352;&#2340; &#2310;&#2332; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367; &#2325;&#2375; &#2319;&#2325; &#2344;&#2319; &#2342;&#2380;&#2352; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2357;&#2375;&#2358; &#2325;&#2352; &#2352;&#2361;&#2366; &#2361;&#2376;&#2404; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344;...<div class=\"access-path\">$value[0]['content']</div></dt><dd><pre>&#2349;&#2366;&#2352;&#2340; &#2310;&#2332; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367; &#2325;&#2375; &#2319;&#2325; &#2344;&#2319; &#2342;&#2380;&#2352; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2357;&#2375;&#2358; &#2325;&#2352; &#2352;&#2361;&#2366; &#2361;&#2376;&#2404; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2375; &#2340;&#2361;&#2340; &#2342;&#2375;&#2358; &#2349;&#2352; &#2350;&#2375;&#2306; &#2311;&#2306;&#2335;&#2352;&#2344;&#2375;&#2335; &#2325;&#2344;&#2375;&#2325;&#2381;&#2335;&#2367;&#2357;&#2367;&#2335;&#2368;, &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2360;&#2366;&#2325;&#2381;&#2359;&#2352;&#2340;&#2366; &#2324;&#2352; &#2312;-&#2327;&#2357;&#2352;&#2381;&#2344;&#2375;&#2306;&#2360; &#2325;&#2375; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2313;&#2354;&#2381;&#2354;&#2375;&#2326;&#2344;&#2368;&#2351; &#2346;&#2381;&#2352;&#2327;&#2340;&#2367; &#2361;&#2369;&#2312; &#2361;&#2376;&#2404; &#2360;&#2381;&#2335;&#2366;&#2352;&#2381;&#2335;&#2309;&#2346; &#2311;&#2325;&#2379;&#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2342;&#2369;&#2344;&#2367;&#2351;&#2366; &#2325;&#2366; &#2340;&#2368;&#2360;&#2352;&#2366; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2366; &#2342;&#2375;&#2358; &#2348;&#2344; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404; &#2347;&#2367;&#2344;&#2335;&#2375;&#2325;, &#2319;&#2337;&#2335;&#2375;&#2325;, &#2361;&#2375;&#2354;&#2381;&#2341;&#2335;&#2375;&#2325; &#2332;&#2376;&#2360;&#2375; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2379;&#2306; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2306;&#2346;&#2344;&#2367;&#2351;&#2366;&#2306; &#2357;&#2376;&#2358;&#2381;&#2357;&#2367;&#2325; &#2360;&#2381;&#2340;&#2352; &#2346;&#2352; &#2309;&#2346;&#2344;&#2368; &#2346;&#2361;&#2330;&#2366;&#2344; &#2348;&#2344;&#2366; &#2352;&#2361;&#2368; &#2361;&#2376;&#2306;&#2404;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[0]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['author_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (9) \"published\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>views</dfn> =&gt; <var>string</var> (4) \"1250\"<div class=\"access-path\">$value[0]['views']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>published_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 06:19:54\"<div class=\"access-path\">$value[0]['published_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 06:19:54\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 06:19:54\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>UTF-8 string</var> (15) \"&#2340;&#2325;&#2344;&#2368;&#2325;\"<div class=\"access-path\">$value[0]['category_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_name</dfn> =&gt; <var>string</var> (13) \"Administrator\"<div class=\"access-path\">$value[0]['author_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (103) \"&#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2332;&#2368;&#2340;\"<div class=\"access-path\">$value[1]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (46) \"cricket-vishwa-cup-mein-bharat-ki-shandar-jeet\"<div class=\"access-path\">$value[1]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (225) \"&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;&#2366;&#2348; &#2309;&#2346;&#2344;&#2375; ...<div class=\"access-path\">$value[1]['description']</div></dt><dd><pre>&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;&#2366;&#2348; &#2309;&#2346;&#2344;&#2375; &#2344;&#2366;&#2350; &#2325;&#2367;&#2351;&#2366;&#2404;\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>content</dfn> =&gt; <var>UTF-8 string</var> (853) \"&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2310;&#2332; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2376;&#2330; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;&#2366;...<div class=\"access-path\">$value[1]['content']</div></dt><dd><pre>&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2310;&#2332; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2376;&#2330; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;&#2366;&#2348; &#2332;&#2368;&#2340;&#2366; &#2361;&#2376;&#2404; &#2325;&#2346;&#2381;&#2340;&#2366;&#2344; &#2325;&#2368; &#2348;&#2375;&#2361;&#2340;&#2352;&#2368;&#2344; &#2325;&#2346;&#2381;&#2340;&#2366;&#2344;&#2368; &#2324;&#2352; &#2335;&#2368;&#2350; &#2325;&#2375; &#2360;&#2349;&#2368; &#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2367;&#2351;&#2379;&#2306; &#2325;&#2375; &#2313;&#2340;&#2381;&#2325;&#2371;&#2359;&#2381;&#2335; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2360;&#2375; &#2351;&#2361; &#2332;&#2368;&#2340; &#2360;&#2306;&#2349;&#2357; &#2361;&#2369;&#2312;&#2404; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2376;&#2330; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2344;&#2375; 350 &#2352;&#2344; &#2325;&#2366; &#2357;&#2367;&#2358;&#2366;&#2354; &#2360;&#2381;&#2325;&#2379;&#2352; &#2348;&#2344;&#2366;&#2351;&#2366; &#2324;&#2352; &#2357;&#2367;&#2346;&#2325;&#2381;&#2359;&#2368; &#2335;&#2368;&#2350; &#2325;&#2379; 280 &#2352;&#2344; &#2346;&#2352; &#2321;&#2354; &#2310;&#2313;&#2335; &#2325;&#2352; &#2342;&#2367;&#2351;&#2366;&#2404; &#2351;&#2361; &#2332;&#2368;&#2340; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2325;&#2375; &#2311;&#2340;&#2367;&#2361;&#2366;&#2360; &#2350;&#2375;&#2306; &#2319;&#2325; &#2344;&#2351;&#2366; &#2309;&#2343;&#2381;&#2351;&#2366;&#2351; &#2332;&#2379;&#2337;&#2364;&#2340;&#2368; &#2361;&#2376;&#2404;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['author_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (9) \"published\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>views</dfn> =&gt; <var>string</var> (4) \"2100\"<div class=\"access-path\">$value[1]['views']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>published_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 04:19:54\"<div class=\"access-path\">$value[1]['published_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 04:19:54\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 04:19:54\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>UTF-8 string</var> (9) \"&#2326;&#2375;&#2354;\"<div class=\"access-path\">$value[1]['category_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_name</dfn> =&gt; <var>string</var> (12) \"News Manager\"<div class=\"access-path\">$value[1]['author_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (101) \"&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340;\"<div class=\"access-path\">$value[2]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (45) \"rajya-sabha-mein-mahattvapurna-vidheyak-parit\"<div class=\"access-path\">$value[2]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (182) \"&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2310;&#2332; &#2319;&#2325; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2332;&#2379; &#2332;&#2344;&#2340;&#2366; &#2325;&#2375; &#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2361;&#2376;&#2404;\"<div class=\"access-path\">$value[2]['description']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>content</dfn> =&gt; <var>UTF-8 string</var> (693) \"&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2310;&#2332; &#2319;&#2325; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2361;&#2376; &#2332;&#2379; &#2310;&#2350; &#2332;&#2344;&#2340;&#2366; &#2325;&#2375; &#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2361;&#2376;&#2404;...<div class=\"access-path\">$value[2]['content']</div></dt><dd><pre>&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2310;&#2332; &#2319;&#2325; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2361;&#2376; &#2332;&#2379; &#2310;&#2350; &#2332;&#2344;&#2340;&#2366; &#2325;&#2375; &#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2361;&#2376;&#2404; &#2311;&#2360; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2360;&#2375; &#2358;&#2367;&#2325;&#2381;&#2359;&#2366;, &#2360;&#2381;&#2357;&#2366;&#2360;&#2381;&#2341;&#2381;&#2351; &#2324;&#2352; &#2352;&#2379;&#2332;&#2327;&#2366;&#2352; &#2325;&#2375; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2360;&#2369;&#2343;&#2366;&#2352; &#2361;&#2379;&#2327;&#2366;&#2404; &#2360;&#2349;&#2368; &#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;&#2325; &#2342;&#2354;&#2379;&#2306; &#2344;&#2375; &#2311;&#2360; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2325;&#2366; &#2360;&#2350;&#2352;&#2381;&#2341;&#2344; &#2325;&#2367;&#2351;&#2366; &#2361;&#2376;&#2404; &#2357;&#2367;&#2346;&#2325;&#2381;&#2359; &#2344;&#2375; &#2349;&#2368; &#2311;&#2360;&#2375; &#2332;&#2344;&#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2348;&#2340;&#2366;&#2351;&#2366; &#2361;&#2376;&#2404; &#2309;&#2348; &#2351;&#2361; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2354;&#2379;&#2325;&#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2349;&#2375;&#2332;&#2366; &#2332;&#2366;&#2319;&#2327;&#2366;&#2404;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['author_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (9) \"published\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>views</dfn> =&gt; <var>string</var> (4) \"1800\"<div class=\"access-path\">$value[2]['views']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>published_at</dfn> =&gt; <var>string</var> (19) \"2025-06-23 22:19:54\"<div class=\"access-path\">$value[2]['published_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-23 22:19:54\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-23 22:19:54\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>UTF-8 string</var> (21) \"&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;\"<div class=\"access-path\">$value[2]['category_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_name</dfn> =&gt; <var>string</var> (13) \"Administrator\"<div class=\"access-path\">$value[2]['author_name']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "latestNews": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (6)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (6)</li><li>Contents (6)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>title</th><th>slug</th><th>description</th><th>content</th><th>image</th><th>category_id</th><th>author_id</th><th>status</th><th>featured</th><th>views</th><th>published_at</th><th>created_at</th><th>updated_at</th><th>category_name</th><th>author_name</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">6</td><td title=\"UTF-8 string (221)\">&#2346;&#2368;&#2319;&#2350; &#2350;&#2379;&#2342;&#2368; 12 &#2332;&#2370;&#2344; &#2325;&#2379; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2330;&#2306;&#2342;&#2381;&#2352;&#2348;&#2366;&#2348;&#2370; &#2344;&#2366;&#2351;&#2337;&#2370; &#2325;&#2375; &#2358;&#2346;&#2341; &#2327;&#2381;&#2352;&#2361;&#2339; &#2360;&#2350;&#2366;&#2352;&#2379;&#2361; &#2350;&#2375;&#2306; &#2358;UTF-8</td><td title=\"UTF-8 string (221)\">&#2346;&#2368;&#2319;&#2350;-&#2350;&#2379;&#2342;&#2368;-12-&#2332;&#2370;&#2344;-&#2325;&#2379;-&#2335;&#2368;&#2337;&#2368;&#2346;&#2368;-&#2346;&#2381;&#2352;&#2350;&#2369;&#2326;-&#2330;&#2306;&#2342;&#2381;&#2352;&#2348;&#2366;&#2348;&#2370;-&#2344;&#2366;&#2351;&#2337;&#2370;-&#2325;&#2375;-&#2358;&#2346;&#2341;-&#2327;&#2381;&#2352;&#2361;&#2339;-&#2360;&#2350;&#2366;&#2352;&#2379;&#2361;-&#2350;&#2375;&#2306;-&#2358;UTF-8</td><td title=\"UTF-8 string (818)\">&#2350;&#2344;&#2379;&#2344;&#2368;&#2340; &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2344;&#2352;&#2375;&#2306;&#2342;&#2381;&#2352; &#2350;&#2379;&#2342;&#2368; &#2309;&#2346;&#2344;&#2375; &#2327;&#2336;&#2348;&#2306;&#2343;&#2344; &#2325;&#2375; &#2360;&#2366;&#2341;&#2368; &#2324;&#2352; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2325;&#2375; &#2358;&#2346;UTF-8</td><td title=\"UTF-8 string (4198)\">&#2350;&#2344;&#2379;&#2344;&#2368;&#2340; &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2344;&#2352;&#2375;&#2306;&#2342;&#2381;&#2352; &#2350;&#2379;&#2342;&#2368; &#2309;&#2346;&#2344;&#2375; &#2327;&#2336;&#2348;&#2306;&#2343;&#2344; &#2325;&#2375; &#2360;&#2366;&#2341;&#2368; &#2324;&#2352; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2325;&#2375; &#2358;&#2346;UTF-8</td><td title=\"string (36)\">1750753523_61bb0e8fbeab52801378.jpeg</td><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"string (9)\">published</td><td title=\"string (1)\">0</td><td title=\"string (1)\">0</td><td title=\"string (19)\">2025-06-24 08:25:23</td><td title=\"string (19)\">2025-06-24 08:25:23</td><td title=\"string (19)\">2025-06-24 08:25:23</td><td title=\"UTF-8 string (21)\">&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;</td><td title=\"string (13)\">Administrator</td></tr><tr><th>1</th><td title=\"string (1)\">1</td><td title=\"UTF-8 string (90)\">&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367; &#2325;&#2366; &#2344;&#2351;&#2366; &#2342;&#2380;&#2352;</td><td title=\"string (40)\">bharat-mein-takneeki-kranti-ka-naya-daur</td><td title=\"UTF-8 string (216)\">&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2361;&#2379; &#2352;&#2361;&#2375; &#2344;&#2357;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2368; &#2360;&#2347;&#2354;&#2340;&#2366; &#2325;&#2368;UTF-8</td><td title=\"UTF-8 string (930)\">&#2349;&#2366;&#2352;&#2340; &#2310;&#2332; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367; &#2325;&#2375; &#2319;&#2325; &#2344;&#2319; &#2342;&#2380;&#2352; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2357;&#2375;&#2358; &#2325;&#2352; &#2352;&#2361;&#2366; &#2361;&#2376;&#2404; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;UTF-8</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">4</td><td title=\"string (1)\">1</td><td title=\"string (9)\">published</td><td title=\"string (1)\">1</td><td title=\"string (4)\">1250</td><td title=\"string (19)\">2025-06-24 06:19:54</td><td title=\"string (19)\">2025-06-24 06:19:54</td><td title=\"string (19)\">2025-06-24 06:19:54</td><td title=\"UTF-8 string (15)\">&#2340;&#2325;&#2344;&#2368;&#2325;</td><td title=\"string (13)\">Administrator</td></tr><tr><th>2</th><td title=\"string (1)\">2</td><td title=\"UTF-8 string (103)\">&#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2332;&#2368;&#2340;</td><td title=\"string (46)\">cricket-vishwa-cup-mein-bharat-ki-shandar-jeet</td><td title=\"UTF-8 string (225)\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;&#2366;&#2348; &#2309;&#2346;&#2344;&#2375;UTF-8</td><td title=\"UTF-8 string (853)\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2310;&#2332; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2376;&#2330; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;UTF-8</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">2</td><td title=\"string (1)\">2</td><td title=\"string (9)\">published</td><td title=\"string (1)\">1</td><td title=\"string (4)\">2100</td><td title=\"string (19)\">2025-06-24 04:19:54</td><td title=\"string (19)\">2025-06-24 04:19:54</td><td title=\"string (19)\">2025-06-24 04:19:54</td><td title=\"UTF-8 string (9)\">&#2326;&#2375;&#2354;</td><td title=\"string (12)\">News Manager</td></tr><tr><th>3</th><td title=\"string (1)\">3</td><td title=\"UTF-8 string (121)\">&#2348;&#2377;&#2354;&#2368;&#2357;&#2369;&#2337; &#2350;&#2375;&#2306; &#2344;&#2312; &#2347;&#2367;&#2354;&#2381;&#2350; &#2325;&#2366; &#2343;&#2350;&#2366;&#2325;&#2375;&#2342;&#2366;&#2352; &#2335;&#2381;&#2352;&#2375;&#2354;&#2352; &#2352;&#2367;&#2354;&#2368;&#2332;</td><td title=\"string (54)\">bollywood-mein-nayi-film-ka-dhamakedar-trailer-release</td><td title=\"UTF-8 string (198)\">&#2348;&#2377;&#2354;&#2368;&#2357;&#2369;&#2337; &#2325;&#2368; &#2310;&#2327;&#2366;&#2350;&#2368; &#2348;&#2381;&#2354;&#2377;&#2325;&#2348;&#2360;&#2381;&#2335;&#2352; &#2347;&#2367;&#2354;&#2381;&#2350; &#2325;&#2366; &#2335;&#2381;&#2352;&#2375;&#2354;&#2352; &#2352;&#2367;&#2354;&#2368;&#2332; &#2361;&#2369;&#2310;, &#2342;&#2352;&#2381;&#2358;&#2325;&#2379;&#2306; &#2350;&#2375;&#2306; &#2313;&#2340;&#2381;&#2360;&#2366;&#2361;&#2404;</td><td title=\"UTF-8 string (831)\">&#2348;&#2377;&#2354;&#2368;&#2357;&#2369;&#2337; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2346;&#2381;&#2352;&#2340;&#2368;&#2325;&#2381;&#2359;&#2367;&#2340; &#2347;&#2367;&#2354;&#2381;&#2350; &#2325;&#2366; &#2335;&#2381;&#2352;&#2375;&#2354;&#2352; &#2310;&#2332; &#2352;&#2367;&#2354;&#2368;&#2332; &#2361;&#2369;&#2310; &#2361;&#2376;&#2404; &#2311;&#2360; &#2347;&#2367;&#2354;&#2381;&#2350; &#2350;&#2375;&#2306; &#2348;&#2377;UTF-8</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">3</td><td title=\"string (1)\">1</td><td title=\"string (9)\">published</td><td title=\"string (1)\">0</td><td title=\"string (3)\">890</td><td title=\"string (19)\">2025-06-24 02:19:54</td><td title=\"string (19)\">2025-06-24 02:19:54</td><td title=\"string (19)\">2025-06-24 02:19:54</td><td title=\"UTF-8 string (21)\">&#2350;&#2344;&#2379;&#2352;&#2306;&#2332;&#2344;</td><td title=\"string (13)\">Administrator</td></tr><tr><th>4</th><td title=\"string (1)\">4</td><td title=\"UTF-8 string (113)\">&#2360;&#2352;&#2325;&#2366;&#2352; &#2325;&#2368; &#2344;&#2312; &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2344;&#2368;&#2340;&#2367; &#2360;&#2375; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352; &#2350;&#2375;&#2306; &#2340;&#2375;&#2332;&#2368;</td><td title=\"string (47)\">sarkar-ki-nayi-arthik-neeti-se-vyapar-mein-teji</td><td title=\"UTF-8 string (235)\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352; &#2360;&#2352;&#2325;&#2366;&#2352; &#2325;&#2368; &#2344;&#2312; &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2344;&#2368;&#2340;&#2367; &#2360;&#2375; &#2331;&#2379;&#2335;&#2375; &#2324;&#2352; &#2350;&#2343;&#2381;&#2351;&#2350; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306; &#2325;&#2379; &#2348;&#2337;&#2364;&#2366; &#2347;&#2366;&#2351;&#2342;&#2366; &#2361;UTF-8</td><td title=\"UTF-8 string (797)\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352; &#2360;&#2352;&#2325;&#2366;&#2352; &#2344;&#2375; &#2310;&#2332; &#2319;&#2325; &#2344;&#2312; &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2344;&#2368;&#2340;&#2367; &#2325;&#2368; &#2328;&#2379;&#2359;&#2339;&#2366; &#2325;&#2368; &#2361;&#2376; &#2332;&#2379; &#2331;&#2379;&#2335;&#2375; &#2324;&#2352; &#2350;&#2343;&#2381;&#2351;&#2350; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;UTF-8</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">5</td><td title=\"string (1)\">2</td><td title=\"string (9)\">published</td><td title=\"string (1)\">0</td><td title=\"string (3)\">650</td><td title=\"string (19)\">2025-06-24 00:19:54</td><td title=\"string (19)\">2025-06-24 00:19:54</td><td title=\"string (19)\">2025-06-24 00:19:54</td><td title=\"UTF-8 string (21)\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;</td><td title=\"string (12)\">News Manager</td></tr><tr><th>5</th><td title=\"string (1)\">5</td><td title=\"UTF-8 string (101)\">&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340;</td><td title=\"string (45)\">rajya-sabha-mein-mahattvapurna-vidheyak-parit</td><td title=\"UTF-8 string (182)\">&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2310;&#2332; &#2319;&#2325; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2332;&#2379; &#2332;&#2344;&#2340;&#2366; &#2325;&#2375; &#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2361;&#2376;&#2404;</td><td title=\"UTF-8 string (693)\">&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2310;&#2332; &#2319;&#2325; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2361;&#2376; &#2332;&#2379; &#2310;&#2350; &#2332;&#2344;&#2340;&#2366; &#2325;&#2375; &#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2361;&#2376;UTF-8</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"string (9)\">published</td><td title=\"string (1)\">1</td><td title=\"string (4)\">1800</td><td title=\"string (19)\">2025-06-23 22:19:54</td><td title=\"string (19)\">2025-06-23 22:19:54</td><td title=\"string (19)\">2025-06-23 22:19:54</td><td title=\"UTF-8 string (21)\">&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;</td><td title=\"string (13)\">Administrator</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (221) \"&#2346;&#2368;&#2319;&#2350; &#2350;&#2379;&#2342;&#2368; 12 &#2332;&#2370;&#2344; &#2325;&#2379; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2330;&#2306;&#2342;&#2381;&#2352;&#2348;&#2366;&#2348;&#2370; &#2344;&#2366;&#2351;&#2337;&#2370; &#2325;&#2375; &#2358;&#2346;&#2341; &#2327;&#2381;&#2352;&#2361;&#2339; &#2360;&#2350;&#2366;&#2352;&#2379;&#2361; &#2350;&#2375;&#2306; &#2358;&#2366;...<div class=\"access-path\">$value[0]['title']</div></dt><dd><pre>&#2346;&#2368;&#2319;&#2350; &#2350;&#2379;&#2342;&#2368; 12 &#2332;&#2370;&#2344; &#2325;&#2379; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2330;&#2306;&#2342;&#2381;&#2352;&#2348;&#2366;&#2348;&#2370; &#2344;&#2366;&#2351;&#2337;&#2370; &#2325;&#2375; &#2358;&#2346;&#2341; &#2327;&#2381;&#2352;&#2361;&#2339; &#2360;&#2350;&#2366;&#2352;&#2379;&#2361; &#2350;&#2375;&#2306; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2379;&#2306;&#2327;&#2375;\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>slug</dfn> =&gt; <var>UTF-8 string</var> (221) \"&#2346;&#2368;&#2319;&#2350;-&#2350;&#2379;&#2342;&#2368;-12-&#2332;&#2370;&#2344;-&#2325;&#2379;-&#2335;&#2368;&#2337;&#2368;&#2346;&#2368;-&#2346;&#2381;&#2352;&#2350;&#2369;&#2326;-&#2330;&#2306;&#2342;&#2381;&#2352;&#2348;&#2366;&#2348;&#2370;-&#2344;&#2366;&#2351;&#2337;&#2370;-&#2325;&#2375;-&#2358;&#2346;&#2341;-&#2327;&#2381;&#2352;&#2361;&#2339;-&#2360;&#2350;&#2366;&#2352;&#2379;&#2361;-&#2350;&#2375;&#2306;-&#2358;&#2366;...<div class=\"access-path\">$value[0]['slug']</div></dt><dd><pre>&#2346;&#2368;&#2319;&#2350;-&#2350;&#2379;&#2342;&#2368;-12-&#2332;&#2370;&#2344;-&#2325;&#2379;-&#2335;&#2368;&#2337;&#2368;&#2346;&#2368;-&#2346;&#2381;&#2352;&#2350;&#2369;&#2326;-&#2330;&#2306;&#2342;&#2381;&#2352;&#2348;&#2366;&#2348;&#2370;-&#2344;&#2366;&#2351;&#2337;&#2370;-&#2325;&#2375;-&#2358;&#2346;&#2341;-&#2327;&#2381;&#2352;&#2361;&#2339;-&#2360;&#2350;&#2366;&#2352;&#2379;&#2361;-&#2350;&#2375;&#2306;-&#2358;&#2366;&#2350;&#2367;&#2354;-&#2361;&#2379;&#2306;&#2327;&#2375;\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (818) \"&#2350;&#2344;&#2379;&#2344;&#2368;&#2340; &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2344;&#2352;&#2375;&#2306;&#2342;&#2381;&#2352; &#2350;&#2379;&#2342;&#2368; &#2309;&#2346;&#2344;&#2375; &#2327;&#2336;&#2348;&#2306;&#2343;&#2344; &#2325;&#2375; &#2360;&#2366;&#2341;&#2368; &#2324;&#2352; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2325;&#2375; &#2358;&#2346;&#2341;...<div class=\"access-path\">$value[0]['description']</div></dt><dd><pre>&#2350;&#2344;&#2379;&#2344;&#2368;&#2340; &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2344;&#2352;&#2375;&#2306;&#2342;&#2381;&#2352; &#2350;&#2379;&#2342;&#2368; &#2309;&#2346;&#2344;&#2375; &#2327;&#2336;&#2348;&#2306;&#2343;&#2344; &#2325;&#2375; &#2360;&#2366;&#2341;&#2368; &#2324;&#2352; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2325;&#2375; &#2358;&#2346;&#2341; &#2327;&#2381;&#2352;&#2361;&#2339; &#2360;&#2350;&#2366;&#2352;&#2379;&#2361; &#2350;&#2375;&#2306; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2379; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2330;&#2306;&#2342;&#2381;&#2352;&#2348;&#2366;&#2348;&#2370; &#2344;&#2366;&#2351;&#2337;&#2370; &#2310;&#2306;&#2343;&#2381;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358; &#2325;&#2375; &#2350;&#2369;&#2326;&#2381;&#2351;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; 12 &#2332;&#2370;&#2344; &#2325;&#2379; &#2358;&#2346;&#2341; &#2354;&#2375;&#2306;&#2327;&#2375;&#2404; &#2348;&#2340;&#2366; &#2342;&#2375;&#2306;, &#2350;&#2344;&#2379;&#2344;&#2368;&#2340; &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2350;&#2379;&#2342;&#2368; &#2310;&#2332; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367; &#2349;&#2357;&#2344; &#2350;&#2375;&#2306; &#2340;&#2368;&#2360;&#2352;&#2368; &#2348;&#2366;&#2352; &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2358;&#2346;&#2341; &#2327;&#2381;&#2352;&#2361;&#2339; &#2325;&#2352;&#2375;&#2306;&#2327;&#2375;&#2404;\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>content</dfn> =&gt; <var>UTF-8 string</var> (4198) \"&#2350;&#2344;&#2379;&#2344;&#2368;&#2340; &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2344;&#2352;&#2375;&#2306;&#2342;&#2381;&#2352; &#2350;&#2379;&#2342;&#2368; &#2309;&#2346;&#2344;&#2375; &#2327;&#2336;&#2348;&#2306;&#2343;&#2344; &#2325;&#2375; &#2360;&#2366;&#2341;&#2368; &#2324;&#2352; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2325;&#2375; &#2358;&#2346;&#2341;...<div class=\"access-path\">$value[0]['content']</div></dt><dd><pre>&#2350;&#2344;&#2379;&#2344;&#2368;&#2340; &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2344;&#2352;&#2375;&#2306;&#2342;&#2381;&#2352; &#2350;&#2379;&#2342;&#2368; &#2309;&#2346;&#2344;&#2375; &#2327;&#2336;&#2348;&#2306;&#2343;&#2344; &#2325;&#2375; &#2360;&#2366;&#2341;&#2368; &#2324;&#2352; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2325;&#2375; &#2358;&#2346;&#2341; &#2327;&#2381;&#2352;&#2361;&#2339; &#2360;&#2350;&#2366;&#2352;&#2379;&#2361; &#2350;&#2375;&#2306; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2379; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2330;&#2306;&#2342;&#2381;&#2352;&#2348;&#2366;&#2348;&#2370; &#2344;&#2366;&#2351;&#2337;&#2370; &#2310;&#2306;&#2343;&#2381;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358; &#2325;&#2375; &#2350;&#2369;&#2326;&#2381;&#2351;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; 12 &#2332;&#2370;&#2344; &#2325;&#2379; &#2358;&#2346;&#2341; &#2354;&#2375;&#2306;&#2327;&#2375;&#2404; &#2348;&#2340;&#2366; &#2342;&#2375;&#2306;, &#2350;&#2344;&#2379;&#2344;&#2368;&#2340; &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2350;&#2379;&#2342;&#2368; &#2310;&#2332; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367; &#2349;&#2357;&#2344; &#2350;&#2375;&#2306; &#2340;&#2368;&#2360;&#2352;&#2368; &#2348;&#2366;&#2352; &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2358;&#2346;&#2341; &#2327;&#2381;&#2352;&#2361;&#2339; &#2325;&#2352;&#2375;&#2306;&#2327;&#2375;&#2404;\r\n\r\n \r\n\r\n\r\n&#2350;&#2369;&#2326;&#2381;&#2351; &#2360;&#2330;&#2367;&#2357; &#2344;&#2368;&#2352;&#2349; &#2325;&#2369;&#2350;&#2366;&#2352; &#2346;&#2381;&#2352;&#2360;&#2366;&#2342; &#2344;&#2375; &#2358;&#2344;&#2367;&#2357;&#2366;&#2352; &#2325;&#2379; &#2348;&#2340;&#2366;&#2351;&#2366; &#2325;&#2367; &#2358;&#2346;&#2341; &#2327;&#2381;&#2352;&#2361;&#2339; &#2360;&#2350;&#2366;&#2352;&#2379;&#2361; &#2350;&#2375;&#2306; &#2346;&#2368;&#2319;&#2350; &#2350;&#2379;&#2342;&#2368;, &#2352;&#2366;&#2332;&#2381;&#2351;&#2346;&#2366;&#2354; &#2319;&#2360; &#2309;&#2348;&#2381;&#2342;&#2369;&#2354; &#2344;&#2332;&#2368;&#2352; &#2324;&#2352; &#2309;&#2344;&#2381;&#2351; &#2327;&#2339;&#2350;&#2366;&#2344;&#2381;&#2351; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2379; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2344;&#2366;&#2351;&#2337;&#2370; &#2348;&#2369;&#2343;&#2357;&#2366;&#2352; &#2325;&#2379; &#2360;&#2369;&#2348;&#2361; 11:27 &#2348;&#2332;&#2375; &#2327;&#2344;&#2381;&#2344;&#2357;&#2352;&#2350; &#2361;&#2357;&#2366;&#2312; &#2309;&#2337;&#2381;&#2337;&#2375; &#2325;&#2375; &#2346;&#2366;&#2360; &#2325;&#2375;&#2360;&#2352;&#2346;&#2354;&#2381;&#2354;&#2368; &#2310;&#2312;&#2335;&#2368; &#2346;&#2366;&#2352;&#2381;&#2325; &#2350;&#2375;&#2306; &#2358;&#2346;&#2341; &#2354;&#2375;&#2306;&#2327;&#2375;&#2404; &#2350;&#2369;&#2326;&#2381;&#2351; &#2360;&#2330;&#2367;&#2357; &#2344;&#2375; &#2360;&#2350;&#2366;&#2352;&#2379;&#2361; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2368; &#2332;&#2366; &#2352;&#2361;&#2368; &#2340;&#2376;&#2351;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306; &#2325;&#2368; &#2360;&#2350;&#2368;&#2325;&#2381;&#2359;&#2366; &#2325;&#2368; &#2324;&#2352; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306; &#2325;&#2379; &#2346;&#2369;&#2326;&#2381;&#2340;&#2366; &#2340;&#2376;&#2351;&#2366;&#2352;&#2367;&#2351;&#2366;&#2306; &#2325;&#2352;&#2344;&#2375; &#2325;&#2366; &#2344;&#2367;&#2352;&#2381;&#2342;&#2375;&#2358; &#2342;&#2367;&#2351;&#2366;&#2404; &#2327;&#2344;&#2381;&#2344;&#2357;&#2352;&#2350; &#2361;&#2357;&#2366;&#2312; &#2309;&#2337;&#2381;&#2337;&#2375; &#2346;&#2352; &#2325;&#2312; &#2357;&#2368;&#2357;&#2368;&#2310;&#2312;&#2346;&#2368; &#2325;&#2375; &#2346;&#2361;&#2369;&#2306;&#2330;&#2344;&#2375; &#2325;&#2368; &#2313;&#2350;&#2381;&#2350;&#2368;&#2342; &#2361;&#2376;&#2404; &#2358;&#2346;&#2341; &#2327;&#2381;&#2352;&#2361;&#2339; &#2360;&#2350;&#2366;&#2352;&#2379;&#2361; &#2325;&#2375; &#2354;&#2367;&#2319; &#2357;&#2352;&#2367;&#2359;&#2381;&#2336; &#2310;&#2312;&#2319;&#2319;&#2360; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352;&#2368; &#2346;&#2368;&#2319;&#2360; &#2346;&#2342;&#2381;&#2351;&#2369;&#2350;&#2381;&#2344; &#2325;&#2379; &#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2350;&#2344;&#2381;&#2357;&#2351;&#2325; &#2344;&#2367;&#2351;&#2369;&#2325;&#2381;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404;\r\n\r\n&#2325;&#2375;&#2306;&#2342;&#2381;&#2352; &#2350;&#2375;&#2306; &#2349;&#2368; &#2350;&#2332;&#2348;&#2370;&#2340; &#2360;&#2381;&#2340;&#2306;&#2349; &#2348;&#2344;&#2325;&#2352; &#2313;&#2349;&#2352;&#2375;\r\n\r\n \r\n\r\n&#2327;&#2380;&#2352;&#2340;&#2354;&#2348; &#2361;&#2376; &#2325;&#2367; &#2344;&#2366;&#2351;&#2337;&#2370; &#2346;&#2352;&#2367;&#2357;&#2366;&#2352; &#2324;&#2352; &#2330;&#2306;&#2342;&#2381;&#2352;&#2348;&#2366;&#2348;&#2370; &#2344;&#2366;&#2351;&#2337;&#2370; &#2325;&#2368; &#2346;&#2340;&#2381;&#2344;&#2368; &#2325;&#2368; &#2360;&#2306;&#2346;&#2340;&#2381;&#2340;&#2367; &#2350;&#2375;&#2306; &#2319;&#2325; &#2340;&#2352;&#2347; &#2351;&#2375; &#2311;&#2332;&#2366;&#2347;&#2366; &#2361;&#2379; &#2352;&#2361;&#2366; &#2361;&#2376;&#2404; &#2357;&#2361;&#2368;&#2306; &#2342;&#2370;&#2360;&#2352;&#2368; &#2323;&#2352; 12 &#2332;&#2370;&#2344; &#2325;&#2379; &#2330;&#2306;&#2342;&#2381;&#2352;&#2348;&#2366;&#2348;&#2370; &#2344;&#2366;&#2351;&#2337;&#2370; &#2310;&#2306;&#2343;&#2381;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358; &#2325;&#2375; &#2344;&#2319; &#2360;&#2368;&#2319;&#2350; &#2348;&#2344;&#2375;&#2306;&#2327;&#2375;&#2404; &#2310;&#2306;&#2343;&#2381;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358; &#2357;&#2367;&#2343;&#2366;&#2344;&#2360;&#2349;&#2366; &#2330;&#2369;&#2344;&#2366;&#2357; &#2346;&#2352;&#2367;&#2339;&#2366;&#2350; &#2350;&#2375;&#2306; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2344;&#2375; &#2360;&#2348;&#2360;&#2375; &#2309;&#2343;&#2367;&#2325; &#2360;&#2368;&#2335;&#2375;&#2306; &#2332;&#2368;&#2340;&#2368; &#2361;&#2376;&#2306;&#2404; 135 &#2360;&#2368;&#2335;&#2375;&#2306; &#2332;&#2368;&#2340; &#2325;&#2352; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2344;&#2375; &#2348;&#2361;&#2369;&#2350;&#2340; &#2325;&#2375; &#2310;&#2306;&#2325;&#2337;&#2364;&#2375; &#2325;&#2379; &#2346;&#2366;&#2352; &#2325;&#2352; &#2354;&#2367;&#2351;&#2366;&#2404;\r\n\r\n&#2325;&#2367;&#2306;&#2327; &#2350;&#2375;&#2325;&#2352; &#2325;&#2368; &#2349;&#2370;&#2350;&#2367;&#2325;&#2366; &#2350;&#2375;&#2306; &#2361;&#2376;&#2306; &#2330;&#2306;&#2342;&#2381;&#2352;&#2348;&#2366;&#2348;&#2370; &#2344;&#2366;&#2351;&#2337;&#2370;\r\n\r\n&#2330;&#2306;&#2342;&#2381;&#2352;&#2348;&#2366;&#2348;&#2370; &#2344;&#2366;&#2351;&#2337;&#2370; &#2319;&#2344;&#2337;&#2368;&#2319; &#2327;&#2336;&#2348;&#2306;&#2343;&#2344; &#2325;&#2375; &#2354;&#2367;&#2319; &#2319;&#2325; &#2325;&#2367;&#2306;&#2327;&#2350;&#2375;&#2325;&#2352;&#2381;&#2360; &#2350;&#2375;&#2306; &#2360;&#2375; &#2319;&#2325; &#2348;&#2344;&#2325;&#2352; &#2360;&#2366;&#2350;&#2344;&#2375; &#2310;&#2319; &#2361;&#2376;&#2306;&#2404; &#2319;&#2344;&#2337;&#2368;&#2319; &#2344;&#2375; &#2354;&#2327;&#2366;&#2340;&#2366;&#2352; &#2340;&#2368;&#2360;&#2352;&#2368; &#2348;&#2366;&#2352; &#2348;&#2361;&#2369;&#2350;&#2340; &#2346;&#2366;&#2344;&#2375; &#2325;&#2366; &#2352;&#2367;&#2325;&#2377;&#2352;&#2381;&#2337; &#2348;&#2344;&#2351;&#2366; &#2361;&#2376;&#2404; &#2330;&#2306;&#2342;&#2381;&#2352;&#2348;&#2366;&#2348;&#2370; &#2344;&#2366;&#2351;&#2337;&#2370; &#2324;&#2352; &#2344;&#2368;&#2340;&#2368;&#2358; &#2325;&#2369;&#2350;&#2366;&#2352; &#2325;&#2368; &#2346;&#2366;&#2352;&#2381;&#2335;&#2368; &#2325;&#2366; &#2327;&#2336;&#2348;&#2306;&#2343;&#2344; &#2325;&#2368; &#2360;&#2352;&#2325;&#2366;&#2352; &#2348;&#2344;&#2366;&#2344;&#2375; &#2350;&#2375;&#2306; &#2309;&#2361;&#2350; &#2351;&#2379;&#2327;&#2342;&#2366;&#2344; &#2352;&#2361;&#2344;&#2375; &#2357;&#2366;&#2354;&#2366; &#2361;&#2376;&#2404; &#2349;&#2366;&#2332;&#2346;&#2366; &#2311;&#2360; &#2348;&#2366;&#2352; &#2354;&#2379;&#2325;&#2360;&#2349;&#2366; &#2330;&#2369;&#2344;&#2366;&#2357; &#2350;&#2375;&#2306; &#2309;&#2325;&#2375;&#2354;&#2375; &#2342;&#2350; &#2346;&#2352; &#2348;&#2361;&#2369;&#2350;&#2340; &#2354;&#2366;&#2344;&#2375; &#2350;&#2375;&#2306; &#2360;&#2347;&#2354; &#2344;&#2361;&#2368;&#2306; &#2361;&#2379; &#2360;&#2325;&#2368; &#2361;&#2376;&#2404; &#2357;&#2361;&#2368;&#2306; &#2325;&#2366;&#2306;&#2327;&#2381;&#2352;&#2375;&#2360; &#2325;&#2375; &#2344;&#2375;&#2340;&#2371;&#2340;&#2381;&#2357; &#2357;&#2366;&#2354;&#2375; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2327;&#2336;&#2348;&#2306;&#2343;&#2344; &#2344;&#2375; &#2311;&#2360; &#2348;&#2366;&#2352; &#2330;&#2369;&#2344;&#2366;&#2357; &#2350;&#2375;&#2306; &#2348;&#2375;&#2361;&#2340;&#2352;&#2368;&#2344; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2367;&#2351;&#2366; &#2361;&#2376;&#2404;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (36) \"1750753523_61bb0e8fbeab52801378.jpeg\"<div class=\"access-path\">$value[0]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['author_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (9) \"published\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>featured</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[0]['featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>views</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[0]['views']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>published_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 08:25:23\"<div class=\"access-path\">$value[0]['published_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 08:25:23\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 08:25:23\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>UTF-8 string</var> (21) \"&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;\"<div class=\"access-path\">$value[0]['category_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_name</dfn> =&gt; <var>string</var> (13) \"Administrator\"<div class=\"access-path\">$value[0]['author_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (90) \"&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367; &#2325;&#2366; &#2344;&#2351;&#2366; &#2342;&#2380;&#2352;\"<div class=\"access-path\">$value[1]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (40) \"bharat-mein-takneeki-kranti-ka-naya-daur\"<div class=\"access-path\">$value[1]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (216) \"&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2361;&#2379; &#2352;&#2361;&#2375; &#2344;&#2357;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2368; &#2360;&#2347;&#2354;&#2340;&#2366; &#2325;&#2368; ...<div class=\"access-path\">$value[1]['description']</div></dt><dd><pre>&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2361;&#2379; &#2352;&#2361;&#2375; &#2344;&#2357;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2368; &#2360;&#2347;&#2354;&#2340;&#2366; &#2325;&#2368; &#2325;&#2361;&#2366;&#2344;&#2368;&#2404;\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>content</dfn> =&gt; <var>UTF-8 string</var> (930) \"&#2349;&#2366;&#2352;&#2340; &#2310;&#2332; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367; &#2325;&#2375; &#2319;&#2325; &#2344;&#2319; &#2342;&#2380;&#2352; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2357;&#2375;&#2358; &#2325;&#2352; &#2352;&#2361;&#2366; &#2361;&#2376;&#2404; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344;...<div class=\"access-path\">$value[1]['content']</div></dt><dd><pre>&#2349;&#2366;&#2352;&#2340; &#2310;&#2332; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367; &#2325;&#2375; &#2319;&#2325; &#2344;&#2319; &#2342;&#2380;&#2352; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2357;&#2375;&#2358; &#2325;&#2352; &#2352;&#2361;&#2366; &#2361;&#2376;&#2404; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2375; &#2340;&#2361;&#2340; &#2342;&#2375;&#2358; &#2349;&#2352; &#2350;&#2375;&#2306; &#2311;&#2306;&#2335;&#2352;&#2344;&#2375;&#2335; &#2325;&#2344;&#2375;&#2325;&#2381;&#2335;&#2367;&#2357;&#2367;&#2335;&#2368;, &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2360;&#2366;&#2325;&#2381;&#2359;&#2352;&#2340;&#2366; &#2324;&#2352; &#2312;-&#2327;&#2357;&#2352;&#2381;&#2344;&#2375;&#2306;&#2360; &#2325;&#2375; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2313;&#2354;&#2381;&#2354;&#2375;&#2326;&#2344;&#2368;&#2351; &#2346;&#2381;&#2352;&#2327;&#2340;&#2367; &#2361;&#2369;&#2312; &#2361;&#2376;&#2404; &#2360;&#2381;&#2335;&#2366;&#2352;&#2381;&#2335;&#2309;&#2346; &#2311;&#2325;&#2379;&#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2342;&#2369;&#2344;&#2367;&#2351;&#2366; &#2325;&#2366; &#2340;&#2368;&#2360;&#2352;&#2366; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2366; &#2342;&#2375;&#2358; &#2348;&#2344; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404; &#2347;&#2367;&#2344;&#2335;&#2375;&#2325;, &#2319;&#2337;&#2335;&#2375;&#2325;, &#2361;&#2375;&#2354;&#2381;&#2341;&#2335;&#2375;&#2325; &#2332;&#2376;&#2360;&#2375; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2379;&#2306; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2306;&#2346;&#2344;&#2367;&#2351;&#2366;&#2306; &#2357;&#2376;&#2358;&#2381;&#2357;&#2367;&#2325; &#2360;&#2381;&#2340;&#2352; &#2346;&#2352; &#2309;&#2346;&#2344;&#2368; &#2346;&#2361;&#2330;&#2366;&#2344; &#2348;&#2344;&#2366; &#2352;&#2361;&#2368; &#2361;&#2376;&#2306;&#2404;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[1]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['author_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (9) \"published\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>views</dfn> =&gt; <var>string</var> (4) \"1250\"<div class=\"access-path\">$value[1]['views']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>published_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 06:19:54\"<div class=\"access-path\">$value[1]['published_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 06:19:54\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 06:19:54\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>UTF-8 string</var> (15) \"&#2340;&#2325;&#2344;&#2368;&#2325;\"<div class=\"access-path\">$value[1]['category_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_name</dfn> =&gt; <var>string</var> (13) \"Administrator\"<div class=\"access-path\">$value[1]['author_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (103) \"&#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2332;&#2368;&#2340;\"<div class=\"access-path\">$value[2]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (46) \"cricket-vishwa-cup-mein-bharat-ki-shandar-jeet\"<div class=\"access-path\">$value[2]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (225) \"&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;&#2366;&#2348; &#2309;&#2346;&#2344;&#2375; ...<div class=\"access-path\">$value[2]['description']</div></dt><dd><pre>&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;&#2366;&#2348; &#2309;&#2346;&#2344;&#2375; &#2344;&#2366;&#2350; &#2325;&#2367;&#2351;&#2366;&#2404;\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>content</dfn> =&gt; <var>UTF-8 string</var> (853) \"&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2310;&#2332; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2376;&#2330; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;&#2366;...<div class=\"access-path\">$value[2]['content']</div></dt><dd><pre>&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2310;&#2332; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2376;&#2330; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;&#2366;&#2348; &#2332;&#2368;&#2340;&#2366; &#2361;&#2376;&#2404; &#2325;&#2346;&#2381;&#2340;&#2366;&#2344; &#2325;&#2368; &#2348;&#2375;&#2361;&#2340;&#2352;&#2368;&#2344; &#2325;&#2346;&#2381;&#2340;&#2366;&#2344;&#2368; &#2324;&#2352; &#2335;&#2368;&#2350; &#2325;&#2375; &#2360;&#2349;&#2368; &#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2367;&#2351;&#2379;&#2306; &#2325;&#2375; &#2313;&#2340;&#2381;&#2325;&#2371;&#2359;&#2381;&#2335; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2360;&#2375; &#2351;&#2361; &#2332;&#2368;&#2340; &#2360;&#2306;&#2349;&#2357; &#2361;&#2369;&#2312;&#2404; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2376;&#2330; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2344;&#2375; 350 &#2352;&#2344; &#2325;&#2366; &#2357;&#2367;&#2358;&#2366;&#2354; &#2360;&#2381;&#2325;&#2379;&#2352; &#2348;&#2344;&#2366;&#2351;&#2366; &#2324;&#2352; &#2357;&#2367;&#2346;&#2325;&#2381;&#2359;&#2368; &#2335;&#2368;&#2350; &#2325;&#2379; 280 &#2352;&#2344; &#2346;&#2352; &#2321;&#2354; &#2310;&#2313;&#2335; &#2325;&#2352; &#2342;&#2367;&#2351;&#2366;&#2404; &#2351;&#2361; &#2332;&#2368;&#2340; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2325;&#2375; &#2311;&#2340;&#2367;&#2361;&#2366;&#2360; &#2350;&#2375;&#2306; &#2319;&#2325; &#2344;&#2351;&#2366; &#2309;&#2343;&#2381;&#2351;&#2366;&#2351; &#2332;&#2379;&#2337;&#2364;&#2340;&#2368; &#2361;&#2376;&#2404;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[2]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[2]['author_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (9) \"published\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>views</dfn> =&gt; <var>string</var> (4) \"2100\"<div class=\"access-path\">$value[2]['views']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>published_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 04:19:54\"<div class=\"access-path\">$value[2]['published_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 04:19:54\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 04:19:54\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>UTF-8 string</var> (9) \"&#2326;&#2375;&#2354;\"<div class=\"access-path\">$value[2]['category_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_name</dfn> =&gt; <var>string</var> (12) \"News Manager\"<div class=\"access-path\">$value[2]['author_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (121) \"&#2348;&#2377;&#2354;&#2368;&#2357;&#2369;&#2337; &#2350;&#2375;&#2306; &#2344;&#2312; &#2347;&#2367;&#2354;&#2381;&#2350; &#2325;&#2366; &#2343;&#2350;&#2366;&#2325;&#2375;&#2342;&#2366;&#2352; &#2335;&#2381;&#2352;&#2375;&#2354;&#2352; &#2352;&#2367;&#2354;&#2368;&#2332;\"<div class=\"access-path\">$value[3]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (54) \"bollywood-mein-nayi-film-ka-dhamakedar-trailer-release\"<div class=\"access-path\">$value[3]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (198) \"&#2348;&#2377;&#2354;&#2368;&#2357;&#2369;&#2337; &#2325;&#2368; &#2310;&#2327;&#2366;&#2350;&#2368; &#2348;&#2381;&#2354;&#2377;&#2325;&#2348;&#2360;&#2381;&#2335;&#2352; &#2347;&#2367;&#2354;&#2381;&#2350; &#2325;&#2366; &#2335;&#2381;&#2352;&#2375;&#2354;&#2352; &#2352;&#2367;&#2354;&#2368;&#2332; &#2361;&#2369;&#2310;, &#2342;&#2352;&#2381;&#2358;&#2325;&#2379;&#2306; &#2350;&#2375;&#2306; &#2313;&#2340;&#2381;&#2360;&#2366;&#2361;&#2404;\"<div class=\"access-path\">$value[3]['description']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>content</dfn> =&gt; <var>UTF-8 string</var> (831) \"&#2348;&#2377;&#2354;&#2368;&#2357;&#2369;&#2337; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2346;&#2381;&#2352;&#2340;&#2368;&#2325;&#2381;&#2359;&#2367;&#2340; &#2347;&#2367;&#2354;&#2381;&#2350; &#2325;&#2366; &#2335;&#2381;&#2352;&#2375;&#2354;&#2352; &#2310;&#2332; &#2352;&#2367;&#2354;&#2368;&#2332; &#2361;&#2369;&#2310; &#2361;&#2376;&#2404; &#2311;&#2360; &#2347;&#2367;&#2354;&#2381;&#2350; &#2350;&#2375;&#2306; &#2348;&#2377;&#2354;...<div class=\"access-path\">$value[3]['content']</div></dt><dd><pre>&#2348;&#2377;&#2354;&#2368;&#2357;&#2369;&#2337; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2346;&#2381;&#2352;&#2340;&#2368;&#2325;&#2381;&#2359;&#2367;&#2340; &#2347;&#2367;&#2354;&#2381;&#2350; &#2325;&#2366; &#2335;&#2381;&#2352;&#2375;&#2354;&#2352; &#2310;&#2332; &#2352;&#2367;&#2354;&#2368;&#2332; &#2361;&#2369;&#2310; &#2361;&#2376;&#2404; &#2311;&#2360; &#2347;&#2367;&#2354;&#2381;&#2350; &#2350;&#2375;&#2306; &#2348;&#2377;&#2354;&#2368;&#2357;&#2369;&#2337; &#2325;&#2375; &#2335;&#2377;&#2346; &#2360;&#2381;&#2335;&#2366;&#2352;&#2381;&#2360; &#2344;&#2375; &#2325;&#2366;&#2350; &#2325;&#2367;&#2351;&#2366; &#2361;&#2376;&#2404; &#2335;&#2381;&#2352;&#2375;&#2354;&#2352; &#2350;&#2375;&#2306; &#2342;&#2367;&#2326;&#2366;&#2319; &#2327;&#2319; &#2319;&#2325;&#2381;&#2358;&#2344; &#2360;&#2368;&#2325;&#2381;&#2357;&#2375;&#2306;&#2360; &#2324;&#2352; &#2311;&#2350;&#2379;&#2358;&#2344;&#2354; &#2350;&#2379;&#2350;&#2375;&#2306;&#2335;&#2381;&#2360; &#2344;&#2375; &#2342;&#2352;&#2381;&#2358;&#2325;&#2379;&#2306; &#2325;&#2366; &#2342;&#2367;&#2354; &#2332;&#2368;&#2340; &#2354;&#2367;&#2351;&#2366; &#2361;&#2376;&#2404; &#2347;&#2367;&#2354;&#2381;&#2350; &#2309;&#2327;&#2354;&#2375; &#2350;&#2361;&#2368;&#2344;&#2375; &#2360;&#2367;&#2344;&#2375;&#2350;&#2366;&#2328;&#2352;&#2379;&#2306; &#2350;&#2375;&#2306; &#2352;&#2367;&#2354;&#2368;&#2332; &#2361;&#2379;&#2327;&#2368;&#2404; &#2344;&#2367;&#2352;&#2381;&#2342;&#2375;&#2358;&#2325; &#2344;&#2375; &#2325;&#2361;&#2366; &#2325;&#2367; &#2351;&#2361; &#2347;&#2367;&#2354;&#2381;&#2350; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2367;&#2344;&#2375;&#2350;&#2366; &#2325;&#2375; &#2354;&#2367;&#2319; &#2319;&#2325; &#2350;&#2368;&#2354; &#2325;&#2366; &#2346;&#2340;&#2381;&#2341;&#2352; &#2360;&#2366;&#2348;&#2367;&#2340; &#2361;&#2379;&#2327;&#2368;&#2404;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[3]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]['author_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (9) \"published\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>featured</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[3]['featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>views</dfn> =&gt; <var>string</var> (3) \"890\"<div class=\"access-path\">$value[3]['views']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>published_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 02:19:54\"<div class=\"access-path\">$value[3]['published_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 02:19:54\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 02:19:54\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>UTF-8 string</var> (21) \"&#2350;&#2344;&#2379;&#2352;&#2306;&#2332;&#2344;\"<div class=\"access-path\">$value[3]['category_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_name</dfn> =&gt; <var>string</var> (13) \"Administrator\"<div class=\"access-path\">$value[3]['author_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[4]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (113) \"&#2360;&#2352;&#2325;&#2366;&#2352; &#2325;&#2368; &#2344;&#2312; &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2344;&#2368;&#2340;&#2367; &#2360;&#2375; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352; &#2350;&#2375;&#2306; &#2340;&#2375;&#2332;&#2368;\"<div class=\"access-path\">$value[4]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (47) \"sarkar-ki-nayi-arthik-neeti-se-vyapar-mein-teji\"<div class=\"access-path\">$value[4]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (235) \"&#2325;&#2375;&#2306;&#2342;&#2381;&#2352; &#2360;&#2352;&#2325;&#2366;&#2352; &#2325;&#2368; &#2344;&#2312; &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2344;&#2368;&#2340;&#2367; &#2360;&#2375; &#2331;&#2379;&#2335;&#2375; &#2324;&#2352; &#2350;&#2343;&#2381;&#2351;&#2350; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306; &#2325;&#2379; &#2348;&#2337;&#2364;&#2366; &#2347;&#2366;&#2351;&#2342;&#2366; &#2361;&#2379;...<div class=\"access-path\">$value[4]['description']</div></dt><dd><pre>&#2325;&#2375;&#2306;&#2342;&#2381;&#2352; &#2360;&#2352;&#2325;&#2366;&#2352; &#2325;&#2368; &#2344;&#2312; &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2344;&#2368;&#2340;&#2367; &#2360;&#2375; &#2331;&#2379;&#2335;&#2375; &#2324;&#2352; &#2350;&#2343;&#2381;&#2351;&#2350; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306; &#2325;&#2379; &#2348;&#2337;&#2364;&#2366; &#2347;&#2366;&#2351;&#2342;&#2366; &#2361;&#2379;&#2344;&#2375; &#2325;&#2368; &#2313;&#2350;&#2381;&#2350;&#2368;&#2342;&#2404;\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>content</dfn> =&gt; <var>UTF-8 string</var> (797) \"&#2325;&#2375;&#2306;&#2342;&#2381;&#2352; &#2360;&#2352;&#2325;&#2366;&#2352; &#2344;&#2375; &#2310;&#2332; &#2319;&#2325; &#2344;&#2312; &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2344;&#2368;&#2340;&#2367; &#2325;&#2368; &#2328;&#2379;&#2359;&#2339;&#2366; &#2325;&#2368; &#2361;&#2376; &#2332;&#2379; &#2331;&#2379;&#2335;&#2375; &#2324;&#2352; &#2350;&#2343;&#2381;&#2351;&#2350; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;...<div class=\"access-path\">$value[4]['content']</div></dt><dd><pre>&#2325;&#2375;&#2306;&#2342;&#2381;&#2352; &#2360;&#2352;&#2325;&#2366;&#2352; &#2344;&#2375; &#2310;&#2332; &#2319;&#2325; &#2344;&#2312; &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2344;&#2368;&#2340;&#2367; &#2325;&#2368; &#2328;&#2379;&#2359;&#2339;&#2366; &#2325;&#2368; &#2361;&#2376; &#2332;&#2379; &#2331;&#2379;&#2335;&#2375; &#2324;&#2352; &#2350;&#2343;&#2381;&#2351;&#2350; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2348;&#2375;&#2361;&#2342; &#2347;&#2366;&#2351;&#2342;&#2375;&#2350;&#2306;&#2342; &#2360;&#2366;&#2348;&#2367;&#2340; &#2361;&#2379;&#2327;&#2368;&#2404; &#2311;&#2360; &#2344;&#2368;&#2340;&#2367; &#2325;&#2375; &#2340;&#2361;&#2340; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306; &#2325;&#2379; &#2325;&#2350; &#2348;&#2381;&#2351;&#2366;&#2332; &#2342;&#2352; &#2346;&#2352; &#2354;&#2379;&#2344; &#2350;&#2367;&#2354;&#2375;&#2327;&#2366; &#2324;&#2352; &#2335;&#2376;&#2325;&#2381;&#2360; &#2350;&#2375;&#2306; &#2349;&#2368; &#2331;&#2370;&#2335; &#2342;&#2368; &#2332;&#2366;&#2319;&#2327;&#2368;&#2404; &#2309;&#2352;&#2381;&#2341;&#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352;&#2367;&#2351;&#2379;&#2306; &#2325;&#2366; &#2350;&#2366;&#2344;&#2344;&#2366; &#2361;&#2376; &#2325;&#2367; &#2351;&#2361; &#2344;&#2368;&#2340;&#2367; &#2342;&#2375;&#2358; &#2325;&#2368; &#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366; &#2325;&#2379; &#2344;&#2312; &#2342;&#2367;&#2358;&#2366; &#2342;&#2375;&#2327;&#2368; &#2324;&#2352; &#2352;&#2379;&#2332;&#2327;&#2366;&#2352; &#2325;&#2375; &#2344;&#2319; &#2309;&#2357;&#2360;&#2352; &#2346;&#2376;&#2342;&#2366; &#2325;&#2352;&#2375;&#2327;&#2368;&#2404;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[4]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[4]['author_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (9) \"published\"<div class=\"access-path\">$value[4]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>featured</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[4]['featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>views</dfn> =&gt; <var>string</var> (3) \"650\"<div class=\"access-path\">$value[4]['views']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>published_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 00:19:54\"<div class=\"access-path\">$value[4]['published_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 00:19:54\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 00:19:54\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>UTF-8 string</var> (21) \"&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;\"<div class=\"access-path\">$value[4]['category_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_name</dfn> =&gt; <var>string</var> (12) \"News Manager\"<div class=\"access-path\">$value[4]['author_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>5</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[5]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[5]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (101) \"&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340;\"<div class=\"access-path\">$value[5]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (45) \"rajya-sabha-mein-mahattvapurna-vidheyak-parit\"<div class=\"access-path\">$value[5]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (182) \"&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2310;&#2332; &#2319;&#2325; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2332;&#2379; &#2332;&#2344;&#2340;&#2366; &#2325;&#2375; &#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2361;&#2376;&#2404;\"<div class=\"access-path\">$value[5]['description']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>content</dfn> =&gt; <var>UTF-8 string</var> (693) \"&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2310;&#2332; &#2319;&#2325; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2361;&#2376; &#2332;&#2379; &#2310;&#2350; &#2332;&#2344;&#2340;&#2366; &#2325;&#2375; &#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2361;&#2376;&#2404;...<div class=\"access-path\">$value[5]['content']</div></dt><dd><pre>&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2310;&#2332; &#2319;&#2325; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2361;&#2376; &#2332;&#2379; &#2310;&#2350; &#2332;&#2344;&#2340;&#2366; &#2325;&#2375; &#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2361;&#2376;&#2404; &#2311;&#2360; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2360;&#2375; &#2358;&#2367;&#2325;&#2381;&#2359;&#2366;, &#2360;&#2381;&#2357;&#2366;&#2360;&#2381;&#2341;&#2381;&#2351; &#2324;&#2352; &#2352;&#2379;&#2332;&#2327;&#2366;&#2352; &#2325;&#2375; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2360;&#2369;&#2343;&#2366;&#2352; &#2361;&#2379;&#2327;&#2366;&#2404; &#2360;&#2349;&#2368; &#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;&#2325; &#2342;&#2354;&#2379;&#2306; &#2344;&#2375; &#2311;&#2360; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2325;&#2366; &#2360;&#2350;&#2352;&#2381;&#2341;&#2344; &#2325;&#2367;&#2351;&#2366; &#2361;&#2376;&#2404; &#2357;&#2367;&#2346;&#2325;&#2381;&#2359; &#2344;&#2375; &#2349;&#2368; &#2311;&#2360;&#2375; &#2332;&#2344;&#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2348;&#2340;&#2366;&#2351;&#2366; &#2361;&#2376;&#2404; &#2309;&#2348; &#2351;&#2361; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2354;&#2379;&#2325;&#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2349;&#2375;&#2332;&#2366; &#2332;&#2366;&#2319;&#2327;&#2366;&#2404;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[5]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]['author_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (9) \"published\"<div class=\"access-path\">$value[5]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]['featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>views</dfn> =&gt; <var>string</var> (4) \"1800\"<div class=\"access-path\">$value[5]['views']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>published_at</dfn> =&gt; <var>string</var> (19) \"2025-06-23 22:19:54\"<div class=\"access-path\">$value[5]['published_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-23 22:19:54\"<div class=\"access-path\">$value[5]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-23 22:19:54\"<div class=\"access-path\">$value[5]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>UTF-8 string</var> (21) \"&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;\"<div class=\"access-path\">$value[5]['category_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_name</dfn> =&gt; <var>string</var> (13) \"Administrator\"<div class=\"access-path\">$value[5]['author_name']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "categories": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (5)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (5)</li><li>Contents (5)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>name</th><th>slug</th><th>description</th><th>status</th><th>created_at</th><th>updated_at</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">1</td><td title=\"UTF-8 string (21)\">&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;</td><td title=\"string (8)\">politics</td><td title=\"UTF-8 string (66)\">&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;&#2325; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2309;&#2346;&#2337;&#2375;&#2335;</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-06-24 07:33:11</td><td title=\"string (19)\">2025-06-24 07:33:11</td></tr><tr><th>1</th><td title=\"string (1)\">2</td><td title=\"UTF-8 string (9)\">&#2326;&#2375;&#2354;</td><td title=\"string (6)\">sports</td><td title=\"UTF-8 string (55)\">&#2326;&#2375;&#2354; &#2332;&#2327;&#2340; &#2325;&#2368; &#2340;&#2366;&#2332;&#2366; &#2326;&#2348;&#2352;&#2375;&#2306;</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-06-24 07:33:11</td><td title=\"string (19)\">2025-06-24 07:33:11</td></tr><tr><th>2</th><td title=\"string (1)\">3</td><td title=\"UTF-8 string (21)\">&#2350;&#2344;&#2379;&#2352;&#2306;&#2332;&#2344;</td><td title=\"string (13)\">entertainment</td><td title=\"UTF-8 string (76)\">&#2348;&#2377;&#2354;&#2368;&#2357;&#2369;&#2337; &#2324;&#2352; &#2350;&#2344;&#2379;&#2352;&#2306;&#2332;&#2344; &#2325;&#2368; &#2342;&#2369;&#2344;&#2367;&#2351;&#2366;</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-06-24 07:33:11</td><td title=\"string (19)\">2025-06-24 07:33:11</td></tr><tr><th>3</th><td title=\"string (1)\">4</td><td title=\"UTF-8 string (15)\">&#2340;&#2325;&#2344;&#2368;&#2325;</td><td title=\"string (10)\">technology</td><td title=\"UTF-8 string (63)\">&#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2344;&#2357;&#2366;&#2330;&#2366;&#2352;</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-06-24 07:33:11</td><td title=\"string (19)\">2025-06-24 07:33:11</td></tr><tr><th>4</th><td title=\"string (1)\">5</td><td title=\"UTF-8 string (21)\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;</td><td title=\"string (8)\">business</td><td title=\"UTF-8 string (88)\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352; &#2324;&#2352; &#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366; &#2325;&#2368; &#2326;&#2348;&#2352;&#2375;&#2306;</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-06-24 07:33:11</td><td title=\"string (19)\">2025-06-24 07:33:11</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>UTF-8 string</var> (21) \"&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;\"<div class=\"access-path\">$value[0]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (8) \"politics\"<div class=\"access-path\">$value[0]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (66) \"&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;&#2325; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2309;&#2346;&#2337;&#2375;&#2335;\"<div class=\"access-path\">$value[0]['description']</div></dt><dd><pre>&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;&#2325; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2309;&#2346;&#2337;&#2375;&#2335;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 07:33:11\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 07:33:11\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>UTF-8 string</var> (9) \"&#2326;&#2375;&#2354;\"<div class=\"access-path\">$value[1]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (6) \"sports\"<div class=\"access-path\">$value[1]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (55) \"&#2326;&#2375;&#2354; &#2332;&#2327;&#2340; &#2325;&#2368; &#2340;&#2366;&#2332;&#2366; &#2326;&#2348;&#2352;&#2375;&#2306;\"<div class=\"access-path\">$value[1]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 07:33:11\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 07:33:11\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>UTF-8 string</var> (21) \"&#2350;&#2344;&#2379;&#2352;&#2306;&#2332;&#2344;\"<div class=\"access-path\">$value[2]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (13) \"entertainment\"<div class=\"access-path\">$value[2]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (76) \"&#2348;&#2377;&#2354;&#2368;&#2357;&#2369;&#2337; &#2324;&#2352; &#2350;&#2344;&#2379;&#2352;&#2306;&#2332;&#2344; &#2325;&#2368; &#2342;&#2369;&#2344;&#2367;&#2351;&#2366;\"<div class=\"access-path\">$value[2]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 07:33:11\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 07:33:11\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>UTF-8 string</var> (15) \"&#2340;&#2325;&#2344;&#2368;&#2325;\"<div class=\"access-path\">$value[3]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (10) \"technology\"<div class=\"access-path\">$value[3]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (63) \"&#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2344;&#2357;&#2366;&#2330;&#2366;&#2352;\"<div class=\"access-path\">$value[3]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 07:33:11\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 07:33:11\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[4]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>UTF-8 string</var> (21) \"&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;\"<div class=\"access-path\">$value[4]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (8) \"business\"<div class=\"access-path\">$value[4]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (88) \"&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352; &#2324;&#2352; &#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366; &#2325;&#2368; &#2326;&#2348;&#2352;&#2375;&#2306;\"<div class=\"access-path\">$value[4]['description']</div></dt><dd><pre>&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352; &#2324;&#2352; &#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366; &#2325;&#2368; &#2326;&#2348;&#2352;&#2375;&#2306;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[4]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 07:33:11\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 07:33:11\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "headerAds": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>array</var> (0)</dt></dl></div>", "sidebarAds": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>array</var> (0)</dt></dl></div>", "footerAds": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>array</var> (0)</dt></dl></div>"}}, "headers": {"Host": "localhost", "Connection": "keep-alive", "Sec-Ch-Ua": "&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Referer": "http://localhost/bbc_news/public/category/politics", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "en-US,en;q=0.9,eu;q=0.8,hi;q=0.7,bho;q=0.6,ne;q=0.5,nl;q=0.4,eo;q=0.3,haw;q=0.2", "Cookie": "csrf_cookie_name=8c60c7c238567f457ab076f4385ea33e; ci_session=80a96d00be22d9a20a3525a6143ead13"}, "cookies": {"csrf_cookie_name": "8c60c7c238567f457ab076f4385ea33e", "ci_session": "80a96d00be22d9a20a3525a6143ead13"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Cache-Control": "no-store, max-age=0, no-cache", "Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.12", "phpSAPI": "apache2handler", "environment": "development", "baseURL": "http://localhost/bbc_news/public/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}