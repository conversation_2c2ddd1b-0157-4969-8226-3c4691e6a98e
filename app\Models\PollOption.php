<?php

namespace App\Models;

use CodeIgniter\Model;

class PollOption extends Model
{
    protected $table            = 'poll_options';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'poll_id',
        'option_text',
        'vote_count',
        'sort_order'
    ];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules = [
        'poll_id'     => 'required|integer',
        'option_text' => 'required|min_length[1]|max_length[500]',
        'sort_order'  => 'required|integer',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Increment vote count for option
     */
    public function incrementVoteCount($optionId)
    {
        $builder = $this->db->table($this->table);
        $builder->set('vote_count', 'vote_count + 1', false);
        $builder->where('id', $optionId);
        return $builder->update();
    }

    /**
     * Get options with vote percentages
     */
    public function getOptionsWithPercentages($pollId)
    {
        $options = $this->where('poll_id', $pollId)
            ->orderBy('sort_order', 'ASC')
            ->findAll();

        $totalVotes = array_sum(array_column($options, 'vote_count'));

        foreach ($options as &$option) {
            $option['percentage'] = $totalVotes > 0 ?
                round(($option['vote_count'] / $totalVotes) * 100, 1) : 0;
        }

        return $options;
    }
}
