<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateEpapersTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'title' => [
                'type'       => 'VARCHAR',
                'constraint' => 500,
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'publication_date' => [
                'type' => 'DATE',
            ],
            'edition' => [
                'type'       => 'VARCHAR',
                'constraint' => 100,
                'default'    => 'Main Edition',
            ],
            'language' => [
                'type'       => 'VARCHAR',
                'constraint' => 50,
                'default'    => 'Hindi',
            ],
            'pdf_file' => [
                'type'       => 'VARCHAR',
                'constraint' => 255,
            ],
            'thumbnail' => [
                'type'       => 'VARCHAR',
                'constraint' => 255,
                'null'       => true,
            ],
            'file_size' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'default'    => 0,
                'comment'    => 'File size in bytes',
            ],
            'page_count' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'default'    => 0,
            ],
            'download_count' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'default'    => 0,
            ],
            'view_count' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'default'    => 0,
            ],
            'status' => [
                'type'       => 'ENUM',
                'constraint' => ['active', 'inactive', 'archived'],
                'default'    => 'active',
            ],
            'featured' => [
                'type'       => 'TINYINT',
                'constraint' => 1,
                'default'    => 0,
            ],
            'uploaded_by' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('publication_date');
        $this->forge->addKey('status');
        $this->forge->addKey('featured');
        $this->forge->addKey(['publication_date', 'edition']);
        $this->forge->addForeignKey('uploaded_by', 'users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('epapers');
    }

    public function down()
    {
        $this->forge->dropTable('epapers');
    }
}
