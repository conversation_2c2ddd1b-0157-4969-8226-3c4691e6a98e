<?php

namespace App\Controllers;

use App\Models\News;
use App\Models\Category;
use App\Models\Ad;

class Home extends BaseController
{
    protected $newsModel;
    protected $categoryModel;
    protected $adModel;

    public function __construct()
    {
        $this->newsModel = new News();
        $this->categoryModel = new Category();
        $this->adModel = new Ad();
    }

    public function index(): string
    {
        $data = [
            'title' => 'BBC News Portal - Latest Hindi News',
            'featuredNews' => $this->newsModel->getFeaturedNews(5),
            'latestNews' => $this->newsModel->getNewsWithDetails(10),
            'categories' => $this->categoryModel->where('status', 'active')->findAll(),
            'headerAds' => $this->adModel->getActiveAdsByPosition('header'),
            'sidebarAds' => $this->adModel->getActiveAdsByPosition('sidebar'),
            'footerAds' => $this->adModel->getActiveAdsByPosition('footer'),
        ];

        return view('frontend/home', $data);
    }

    public function news($slug)
    {
        // Decode URL and clean the slug
        $slug = urldecode($slug);
        $slug = trim($slug);

        // Get news with category and author details
        $news = $this->newsModel->select('news.*, categories.name as category_name, categories.slug as category_slug, users.full_name as author_name')
            ->join('categories', 'categories.id = news.category_id')
            ->join('users', 'users.id = news.author_id')
            ->where('news.slug', $slug)
            ->where('news.status', 'published')
            ->first();

        if (!$news) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('News article not found');
        }

        // Increment views
        $this->newsModel->incrementViews($news['id']);

        // Get related news from same category
        $relatedNews = $this->newsModel->getNewsByCategory($news['category_id'], 5);
        $relatedNews = array_filter($relatedNews, function ($item) use ($news) {
            return $item['id'] !== $news['id'];
        });

        $data = [
            'title' => $news['title'] . ' - BBC News Portal',
            'news' => $news,
            'relatedNews' => array_slice($relatedNews, 0, 4),
            'categories' => $this->categoryModel->where('status', 'active')->findAll(),
            'sidebarAds' => $this->adModel->getActiveAdsByPosition('sidebar'),
            'contentAds' => $this->adModel->getActiveAdsByPosition('content_top'),
        ];

        return view('frontend/news_detail', $data);
    }

    public function category($slug)
    {
        // Decode URL and clean the slug
        $slug = urldecode($slug);
        $slug = trim($slug);

        $category = $this->categoryModel->where('slug', $slug)
            ->where('status', 'active')
            ->first();

        if (!$category) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Category not found');
        }

        $news = $this->newsModel->getNewsByCategory($category['id'], 20);

        $data = [
            'title' => $category['name'] . ' - BBC News Portal',
            'category' => $category,
            'news' => $news,
            'categories' => $this->categoryModel->where('status', 'active')->findAll(),
            'sidebarAds' => $this->adModel->getActiveAdsByPosition('sidebar'),
        ];

        return view('frontend/category', $data);
    }
}
