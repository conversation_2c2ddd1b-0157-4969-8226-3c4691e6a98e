<?php

namespace App\Controllers;

use App\Models\News;
use App\Models\Category;
use App\Models\Ad;
use App\Models\BreakingNews;

class Home extends BaseController
{
    protected $newsModel;
    protected $categoryModel;
    protected $adModel;
    protected $breakingNewsModel;
    protected $pollModel;

    public function __construct()
    {
        $this->newsModel = new News();
        $this->categoryModel = new Category();
        $this->adModel = new Ad();
        $this->breakingNewsModel = new BreakingNews();
        $this->pollModel = new \App\Models\Poll();
    }

    public function index(): string
    {
        // Get site settings for dynamic title
        $siteSettings = getSiteSettings();

        $data = [
            'title' => $siteSettings['site_name'] . ' - ' . $siteSettings['site_tagline'],
            'featuredNews' => $this->newsModel->getFeaturedNews(5),
            'latestNews' => $this->newsModel->getNewsWithDetails(10),
            'categories' => $this->categoryModel->where('status', 'active')->findAll(),
            'breakingNews' => $this->breakingNewsModel->getActiveBreakingNews(5),
            'polls' => $this->pollModel->getActivePolls(2),
            'headerAds' => $this->adModel->getActiveAdsByPosition('header'),
            'sidebarAds' => $this->adModel->getActiveAdsByPosition('sidebar'),
            'footerAds' => $this->adModel->getActiveAdsByPosition('footer'),
            'betweenNewsAds' => $this->adModel->getRotatingAdsByPosition('between_news', 1),
            'floatingAds' => $this->adModel->getActiveAdsByPosition('floating'),
            'stickyBottomAds' => $this->adModel->getActiveAdsByPosition('sticky_bottom'),
        ];

        return view('frontend/home', $data);
    }

    public function news($slug)
    {
        // Decode URL and clean the slug
        $slug = urldecode($slug);
        $slug = trim($slug);

        // Get news with category and author details
        $news = $this->newsModel->select('news.*, categories.name as category_name, categories.slug as category_slug, users.full_name as author_name')
            ->join('categories', 'categories.id = news.category_id')
            ->join('users', 'users.id = news.author_id')
            ->where('news.slug', $slug)
            ->where('news.status', 'published')
            ->first();

        if (!$news) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('News article not found');
        }

        // Increment views
        $this->newsModel->incrementViews($news['id']);

        // Get related news from same category
        $relatedNews = $this->newsModel->getNewsByCategory($news['category_id'], 5);
        $relatedNews = array_filter($relatedNews, function ($item) use ($news) {
            return $item['id'] !== $news['id'];
        });

        // Get site settings for dynamic title
        $siteSettings = getSiteSettings();

        $data = [
            'title' => $news['title'] . ' - ' . $siteSettings['site_name'],
            'news' => $news,
            'relatedNews' => array_slice($relatedNews, 0, 4),
            'categories' => $this->categoryModel->where('status', 'active')->findAll(),
            'breakingNews' => $this->breakingNewsModel->getActiveBreakingNews(5),
            'polls' => $this->pollModel->getActivePolls(1),
            'headerAds' => $this->adModel->getActiveAdsByPosition('header'),
            'sidebarAds' => $this->adModel->getActiveAdsByPosition('sidebar'),
            'footerAds' => $this->adModel->getActiveAdsByPosition('footer'),
            'contentAds' => $this->adModel->getActiveAdsByPosition('content_top'),
            'contentBottomAds' => $this->adModel->getActiveAdsByPosition('content_bottom'),
            'inArticleAds' => $this->adModel->getRotatingAdsByPosition('in_article', 1),
            'floatingAds' => $this->adModel->getActiveAdsByPosition('floating'),
        ];

        return view('frontend/news_detail', $data);
    }

    public function category($slug)
    {
        // Decode URL and clean the slug
        $slug = urldecode($slug);
        $slug = trim($slug);

        $category = $this->categoryModel->where('slug', $slug)
            ->where('status', 'active')
            ->first();

        if (!$category) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Category not found');
        }

        $news = $this->newsModel->getNewsByCategory($category['id'], 20);

        // Get site settings for dynamic title
        $siteSettings = getSiteSettings();

        $data = [
            'title' => $category['name'] . ' - ' . $siteSettings['site_name'],
            'category' => $category,
            'news' => $news,
            'categories' => $this->categoryModel->where('status', 'active')->findAll(),
            'breakingNews' => $this->breakingNewsModel->getActiveBreakingNews(5),
            'polls' => $this->pollModel->getActivePolls(1),
            'headerAds' => $this->adModel->getActiveAdsByPosition('header'),
            'sidebarAds' => $this->adModel->getActiveAdsByPosition('sidebar'),
            'footerAds' => $this->adModel->getActiveAdsByPosition('footer'),
            'betweenNewsAds' => $this->adModel->getRotatingAdsByPosition('between_news', 1),
        ];

        return view('frontend/category', $data);
    }
}
