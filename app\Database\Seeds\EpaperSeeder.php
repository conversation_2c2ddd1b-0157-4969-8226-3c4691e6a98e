<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class EpaperSeeder extends Seeder
{
    public function run()
    {
        $epapers = [
            [
                'title' => 'BBC News - 24 जून 2025',
                'description' => 'आज के मुख्य समाचार: राष्ट्रीय और अंतर्राष्ट्रीय घटनाओं की विस्तृत कवरेज',
                'publication_date' => '2025-06-24',
                'edition' => 'Main Edition',
                'language' => 'Hindi',
                'pdf_file' => 'sample_epaper_1.pdf',
                'thumbnail' => 'thumb_epaper_1.jpg',
                'file_size' => 15728640, // 15MB
                'page_count' => 16,
                'download_count' => 245,
                'view_count' => 1250,
                'status' => 'active',
                'featured' => 1,
                'uploaded_by' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'title' => 'BBC News - 23 जून 2025',
                'description' => 'कल के प्रमुख समाचार और विश्लेषण',
                'publication_date' => '2025-06-23',
                'edition' => 'Main Edition',
                'language' => 'Hindi',
                'pdf_file' => 'sample_epaper_2.pdf',
                'thumbnail' => 'thumb_epaper_2.jpg',
                'file_size' => 18874368, // 18MB
                'page_count' => 20,
                'download_count' => 189,
                'view_count' => 890,
                'status' => 'active',
                'featured' => 0,
                'uploaded_by' => 1,
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
            ],
            [
                'title' => 'BBC News - 22 जून 2025',
                'description' => 'सप्ताहांत विशेष संस्करण',
                'publication_date' => '2025-06-22',
                'edition' => 'Weekend Edition',
                'language' => 'Hindi',
                'pdf_file' => 'sample_epaper_3.pdf',
                'thumbnail' => 'thumb_epaper_3.jpg',
                'file_size' => 22020096, // 21MB
                'page_count' => 24,
                'download_count' => 156,
                'view_count' => 678,
                'status' => 'active',
                'featured' => 0,
                'uploaded_by' => 2,
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-2 days')),
            ],
            [
                'title' => 'BBC News - 21 जून 2025',
                'description' => 'शुक्रवार के मुख्य समाचार',
                'publication_date' => '2025-06-21',
                'edition' => 'Main Edition',
                'language' => 'Hindi',
                'pdf_file' => 'sample_epaper_4.pdf',
                'thumbnail' => null,
                'file_size' => 14680064, // 14MB
                'page_count' => 14,
                'download_count' => 134,
                'view_count' => 567,
                'status' => 'active',
                'featured' => 0,
                'uploaded_by' => 1,
                'created_at' => date('Y-m-d H:i:s', strtotime('-3 days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-3 days')),
            ],
            [
                'title' => 'BBC News - 20 जून 2025',
                'description' => 'गुरुवार के प्रमुख समाचार और विशेष रिपोर्ट',
                'publication_date' => '2025-06-20',
                'edition' => 'Main Edition',
                'language' => 'Hindi',
                'pdf_file' => 'sample_epaper_5.pdf',
                'thumbnail' => 'thumb_epaper_5.jpg',
                'file_size' => 16777216, // 16MB
                'page_count' => 18,
                'download_count' => 98,
                'view_count' => 445,
                'status' => 'archived',
                'featured' => 0,
                'uploaded_by' => 2,
                'created_at' => date('Y-m-d H:i:s', strtotime('-4 days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-4 days')),
            ],
        ];

        foreach ($epapers as $epaper) {
            $this->db->table('epapers')->insert($epaper);
        }

        echo "Epapers seeded successfully!\n";
    }
}
