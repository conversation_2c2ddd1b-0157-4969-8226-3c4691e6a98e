<?= $this->extend('frontend/layout') ?>

<?= $this->section('content') ?>
<!-- Header Ads -->
<?= view('components/ad_display', [
    'ads' => $headerAds,
    'position' => 'header',
    'showLabel' => true
]) ?>



<div class="container-fluid mt-4">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-9">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-3">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>">होम</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('epaper') ?>">ई-पेपर</a></li>
                    <li class="breadcrumb-item active" aria-current="page"><?= esc($epaper['title']) ?></li>
                </ol>
            </nav>

            <!-- Epaper Details -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-newspaper me-2"></i><?= esc($epaper['title']) ?>
                        </h4>
                        <?php if ($epaper['featured']): ?>
                            <span class="badge bg-warning text-dark">
                                <i class="fas fa-star me-1"></i>विशेष
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Thumbnail/Preview -->
                        <div class="col-md-4">
                            <?php if ($epaper['thumbnail']): ?>
                                <img src="<?= base_url('uploads/epapers/thumbnails/' . $epaper['thumbnail']) ?>"
                                    alt="<?= esc($epaper['title']) ?>"
                                    class="img-fluid rounded shadow-sm mb-3">
                            <?php else: ?>
                                <div class="bg-light rounded d-flex align-items-center justify-content-center mb-3" style="height: 300px;">
                                    <div class="text-center">
                                        <i class="fas fa-file-pdf fa-4x text-muted mb-2"></i>
                                        <div class="text-muted">PDF Preview</div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Action Buttons -->
                            <div class="d-grid gap-2">
                                <a href="<?= base_url('uploads/epapers/' . $epaper['pdf_file']) ?>"
                                    target="_blank" class="btn btn-primary btn-lg">
                                    <i class="fas fa-eye me-2"></i>PDF में देखें
                                </a>
                                <a href="<?= base_url('epaper/download/' . $epaper['id']) ?>"
                                    class="btn btn-success btn-lg">
                                    <i class="fas fa-download me-2"></i>डाउनलोड करें
                                </a>
                                <button class="btn btn-outline-secondary" onclick="shareEpaper()">
                                    <i class="fas fa-share-alt me-2"></i>शेयर करें
                                </button>
                            </div>
                        </div>

                        <!-- Details -->
                        <div class="col-md-8">
                            <?php if ($epaper['description']): ?>
                                <div class="mb-4">
                                    <h6><i class="fas fa-info-circle me-2"></i>विवरण</h6>
                                    <p class="text-muted"><?= esc($epaper['description']) ?></p>
                                </div>
                            <?php endif; ?>

                            <!-- Epaper Information -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-calendar-alt me-2"></i>प्रकाशन की जानकारी</h6>
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td><strong>तिथि:</strong></td>
                                            <td><?= formatDateHindi($epaper['publication_date']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>दिन:</strong></td>
                                            <td><?= date('l', strtotime($epaper['publication_date'])) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>संस्करण:</strong></td>
                                            <td><?= esc($epaper['edition']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>भाषा:</strong></td>
                                            <td><?= esc($epaper['language']) ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-file-alt me-2"></i>फ़ाइल की जानकारी</h6>
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td><strong>आकार:</strong></td>
                                            <td><?= formatBytes($epaper['file_size']) ?></td>
                                        </tr>
                                        <?php if ($epaper['page_count']): ?>
                                            <tr>
                                                <td><strong>पृष्ठ:</strong></td>
                                                <td><?= $epaper['page_count'] ?> पृष्ठ</td>
                                            </tr>
                                        <?php endif; ?>
                                        <tr>
                                            <td><strong>देखा गया:</strong></td>
                                            <td><?= number_format($epaper['view_count']) ?> बार</td>
                                        </tr>
                                        <tr>
                                            <td><strong>डाउनलोड:</strong></td>
                                            <td><?= number_format($epaper['download_count']) ?> बार</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- PDF Embed (Optional) -->
                            <div class="mb-3">
                                <h6><i class="fas fa-file-pdf me-2"></i>त्वरित पूर्वावलोकन</h6>
                                <div class="border rounded p-2">
                                    <embed src="<?= base_url('uploads/epapers/' . $epaper['pdf_file']) ?>#toolbar=0&navpanes=0&scrollbar=0"
                                        type="application/pdf"
                                        width="100%"
                                        height="400px"
                                        style="border: none;">
                                    <div class="text-center mt-2">
                                        <small class="text-muted">
                                            यदि PDF दिखाई नहीं दे रहा है, तो
                                            <a href="<?= base_url('uploads/epapers/' . $epaper['pdf_file']) ?>" target="_blank">यहाँ क्लिक करें</a>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Epapers -->
            <?php if (!empty($relatedEpapers)): ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-newspaper me-2"></i>अन्य ई-पेपर
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach (array_slice($relatedEpapers, 0, 6) as $related): ?>
                                <?php if ($related['id'] != $epaper['id']): ?>
                                    <div class="col-md-4 col-lg-2 mb-3">
                                        <div class="card h-100">
                                            <div class="card-body p-2">
                                                <?php if ($related['thumbnail']): ?>
                                                    <img src="<?= base_url('uploads/epapers/thumbnails/' . $related['thumbnail']) ?>"
                                                        alt="<?= esc($related['title']) ?>"
                                                        class="img-fluid rounded mb-2">
                                                <?php else: ?>
                                                    <div class="bg-light rounded d-flex align-items-center justify-content-center mb-2" style="height: 80px;">
                                                        <i class="fas fa-file-pdf text-muted"></i>
                                                    </div>
                                                <?php endif; ?>

                                                <h6 class="card-title small"><?= esc(substr($related['title'], 0, 30)) ?>...</h6>
                                                <p class="card-text small text-muted">
                                                    <?= date('d M Y', strtotime($related['publication_date'])) ?>
                                                </p>

                                                <div class="d-flex gap-1">
                                                    <a href="<?= base_url('epaper/view/' . $related['id']) ?>"
                                                        class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('epaper/download/' . $related['id']) ?>"
                                                        class="btn btn-sm btn-outline-success">
                                                        <i class="fas fa-download"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-3">
            <!-- Quick Navigation -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-compass me-2"></i>नेवीगेशन
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= base_url('epaper') ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-home me-2"></i>ई-पेपर होम
                        </a>
                        <a href="<?= base_url('epaper/archive') ?>" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-archive me-2"></i>आर्काइव
                        </a>
                        <a href="<?= base_url('epaper/search') ?>" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-search me-2"></i>खोजें
                        </a>
                    </div>
                </div>
            </div>

            <!-- Sidebar Ads -->
            <?= view('components/ad_display', [
                'ads' => $sidebarAds,
                'position' => 'sidebar',
                'showLabel' => true
            ]) ?>
        </div>
    </div>
</div>

<!-- Footer Ads -->
<?= view('components/ad_display', [
    'ads' => $footerAds,
    'position' => 'footer',
    'showLabel' => true
]) ?>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    function shareEpaper() {
        const url = window.location.href;
        const title = '<?= esc($epaper['title']) ?>';

        if (navigator.share) {
            navigator.share({
                title: title,
                url: url
            }).then(() => {
                console.log('Shared successfully');
            }).catch((error) => {
                console.log('Error sharing:', error);
                fallbackShare(url, title);
            });
        } else {
            fallbackShare(url, title);
        }
    }

    function fallbackShare(url, title) {
        // Copy to clipboard
        navigator.clipboard.writeText(url).then(() => {
            alert('लिंक कॉपी हो गया है! आप इसे कहीं भी शेयर कर सकते हैं।');
        }).catch(() => {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = url;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert('लिंक कॉपी हो गया है! आप इसे कहीं भी शेयर कर सकते हैं।');
        });
    }

    // Track view (optional analytics)
    document.addEventListener('DOMContentLoaded', function() {
        // You can add analytics tracking here
        console.log('Epaper viewed: <?= $epaper['id'] ?>');
    });
</script>
<?= $this->endSection() ?>