<?= $this->extend('frontend/layout') ?>

<?= $this->section('content') ?>

<div class="container">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Featured News Section -->
            <?php if (!empty($featuredNews)): ?>
                <section class="mb-5">
                    <h2 class="mb-4"><i class="fas fa-star text-warning me-2"></i>मुख्य समाचार</h2>

                    <?php $mainFeatured = $featuredNews[0]; ?>
                    <div class="card news-card featured-news mb-4">
                        <div class="row g-0">
                            <div class="col-md-6">
                                <?php if ($mainFeatured['image']): ?>
                                    <img src="<?= base_url('/uploads/news/' . $mainFeatured['image']) ?>"
                                        class="img-fluid rounded-start h-100" style="object-fit: cover;"
                                        alt="<?= esc($mainFeatured['title']) ?>">
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6">
                                <div class="card-body h-100 d-flex flex-column">
                                    <span class="badge category-badge mb-2 align-self-start">
                                        <?= esc($mainFeatured['category_name']) ?>
                                    </span>
                                    <h3 class="card-title">
                                        <a href="<?= base_url('news/' . $mainFeatured['slug']) ?>"
                                            class="text-white text-decoration-none">
                                            <?= esc($mainFeatured['title']) ?>
                                        </a>
                                    </h3>
                                    <p class="card-text flex-grow-1"><?= esc(substr($mainFeatured['description'], 0, 150)) ?>...</p>
                                    <div class="news-meta">
                                        <small>
                                            <i class="fas fa-user me-1"></i><?= esc($mainFeatured['author_name']) ?>
                                            <i class="fas fa-clock ms-3 me-1"></i><?= date('d M Y, H:i', strtotime($mainFeatured['created_at'])) ?>
                                            <i class="fas fa-eye ms-3 me-1"></i><?= number_format($mainFeatured['views']) ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Other Featured News -->
                    <?php if (count($featuredNews) > 1): ?>
                        <div class="row">
                            <?php foreach (array_slice($featuredNews, 1, 4) as $news): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="card news-card h-100">
                                        <?php if ($news['image']): ?>
                                            <img src="<?= base_url('uploads/news/' . $news['image']) ?>"
                                                class="card-img-top" style="height: 200px; object-fit: cover;"
                                                alt="<?= esc($news['title']) ?>">
                                        <?php endif; ?>
                                        <div class="card-body d-flex flex-column">
                                            <span class="badge bg-primary mb-2 align-self-start">
                                                <?= esc($news['category_name']) ?>
                                            </span>
                                            <h5 class="card-title">
                                                <a href="<?= base_url('news/' . $news['slug']) ?>"
                                                    class="text-dark text-decoration-none">
                                                    <?= esc($news['title']) ?>
                                                </a>
                                            </h5>
                                            <p class="card-text flex-grow-1"><?= esc(substr($news['description'], 0, 100)) ?>...</p>
                                            <div class="news-meta">
                                                <small>
                                                    <i class="fas fa-clock me-1"></i><?= date('d M Y', strtotime($news['created_at'])) ?>
                                                    <i class="fas fa-eye ms-2 me-1"></i><?= number_format($news['views']) ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </section>
            <?php endif; ?>

            <!-- Latest News Section -->
            <section class="mb-5">
                <h2 class="mb-4"><i class="fas fa-clock text-primary me-2"></i>ताजा समाचार</h2>

                <?php if (!empty($latestNews)): ?>
                    <div class="row">
                        <?php foreach ($latestNews as $index => $news): ?>
                            <div class="col-md-6 mb-4">
                                <div class="card news-card h-100">
                                    <?php if ($news['image']): ?>
                                        <img src="<?= base_url('uploads/news/' . $news['image']) ?>"
                                            class="card-img-top" style="height: 200px; object-fit: cover;"
                                            alt="<?= esc($news['title']) ?>">
                                    <?php endif; ?>
                                    <div class="card-body d-flex flex-column">
                                        <span class="badge bg-secondary mb-2 align-self-start">
                                            <?= esc($news['category_name']) ?>
                                        </span>
                                        <h5 class="card-title">
                                            <a href="<?= base_url('news/' . $news['slug']) ?>"
                                                class="text-dark text-decoration-none">
                                                <?= esc($news['title']) ?>
                                            </a>
                                        </h5>
                                        <p class="card-text flex-grow-1"><?= esc(substr($news['description'], 0, 120)) ?>...</p>
                                        <div class="news-meta">
                                            <small>
                                                <i class="fas fa-user me-1"></i><?= esc($news['author_name']) ?>
                                                <br>
                                                <i class="fas fa-clock me-1"></i><?= date('d M Y, H:i', strtotime($news['created_at'])) ?>
                                                <i class="fas fa-eye ms-2 me-1"></i><?= number_format($news['views']) ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Insert ad between news items -->
                            <?php if ($index == 3 && !empty($betweenNewsAds)): ?>
                                <div class="col-12">
                                    <?= view('components/ad_display', [
                                        'ads' => $betweenNewsAds,
                                        'position' => 'between_news',
                                        'showLabel' => true
                                    ]) ?>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>

                    <div class="text-center mt-4">
                        <a href="#" class="btn btn-primary btn-lg">
                            <i class="fas fa-plus me-2"></i>और समाचार देखें
                        </a>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">कोई समाचार उपलब्ध नहीं</h4>
                        <p class="text-muted">जल्द ही नए समाचार उपलब्ध होंगे।</p>
                    </div>
                <?php endif; ?>
            </section>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Poll Sidebar -->
            <?= view('components/poll_sidebar', ['polls' => $polls]) ?>

            <!-- Sidebar Ads -->
            <?= view('components/ad_display', [
                'ads' => $sidebarAds,
                'position' => 'sidebar',
                'showLabel' => true
            ]) ?>

            <!-- Categories Widget -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>श्रेणियां</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($categories)): ?>
                        <div class="d-flex flex-wrap gap-2">
                            <?php foreach ($categories as $category): ?>
                                <a href="<?= base_url('category/' . $category['slug']) ?>"
                                    class="btn btn-outline-primary btn-sm">
                                    <?= esc($category['name']) ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Popular News Widget -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-fire me-2"></i>लोकप्रिय समाचार</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($latestNews)): ?>
                        <?php foreach (array_slice($latestNews, 0, 5) as $index => $news): ?>
                            <div class="d-flex mb-3 <?= $index < 4 ? 'border-bottom pb-3' : '' ?>">
                                <div class="flex-shrink-0 me-3">
                                    <span class="badge bg-primary rounded-pill"><?= $index + 1 ?></span>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="<?= base_url('news/' . $news['slug']) ?>"
                                            class="text-dark text-decoration-none">
                                            <?= esc(substr($news['title'], 0, 60)) ?>...
                                        </a>
                                    </h6>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i><?= number_format($news['views']) ?> views
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>