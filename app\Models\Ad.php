<?php

namespace App\Models;

use CodeIgniter\Model;

class Ad extends Model
{
    protected $table            = 'ads';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['title', 'description', 'image', 'link_url', 'position', 'type', 'status', 'start_date', 'end_date'];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'title'    => 'required|min_length[3]|max_length[255]',
        'position' => 'required|in_list[header,sidebar,footer,content_top,content_bottom,between_news]',
        'type'     => 'required|in_list[banner,text,video]',
        'status'   => 'required|in_list[active,inactive]',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    public function getActiveAdsByPosition($position)
    {
        return $this->where('position', $position)
            ->where('status', 'active')
            ->where('(start_date IS NULL OR start_date <= NOW())', null, false)
            ->where('(end_date IS NULL OR end_date >= NOW())', null, false)
            ->findAll();
    }

    public function incrementClicks($id)
    {
        $this->set('clicks', 'clicks + 1', false)
            ->where('id', $id)
            ->update();
    }

    public function incrementImpressions($id)
    {
        $this->set('impressions', 'impressions + 1', false)
            ->where('id', $id)
            ->update();
    }

    public function getAdStats($id)
    {
        $ad = $this->find($id);
        if (!$ad) return null;

        $ctr = $ad['impressions'] > 0 ? ($ad['clicks'] / $ad['impressions']) * 100 : 0;

        return [
            'clicks' => $ad['clicks'],
            'impressions' => $ad['impressions'],
            'ctr' => round($ctr, 2)
        ];
    }
}
