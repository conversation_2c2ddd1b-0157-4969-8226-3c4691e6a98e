<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-search me-2"></i>SEO Settings</h2>
                <a href="<?= base_url('admin/settings') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Settings
                </a>
            </div>

            <!-- Success/Error Messages -->
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-search-plus me-2"></i>Search Engine Optimization
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="<?= base_url('admin/settings/seo/update') ?>" method="post">
                                <?= csrf_field() ?>
                                
                                <!-- Meta Description -->
                                <div class="mb-4">
                                    <label for="meta_description" class="form-label">
                                        <i class="fas fa-align-left me-2"></i>Default Meta Description
                                    </label>
                                    <textarea class="form-control" id="meta_description" name="meta_description" rows="3" 
                                              maxlength="160" placeholder="Brief description of your website (150-160 characters recommended)"><?= old('meta_description', getSetting('meta_description', 'Latest Hindi news, breaking news, politics, sports, entertainment, technology and business news')) ?></textarea>
                                    <div class="form-text">
                                        <span id="desc-count">0</span>/160 characters. This appears in search engine results.
                                    </div>
                                </div>

                                <!-- Meta Keywords -->
                                <div class="mb-4">
                                    <label for="meta_keywords" class="form-label">
                                        <i class="fas fa-tags me-2"></i>Default Meta Keywords
                                    </label>
                                    <textarea class="form-control" id="meta_keywords" name="meta_keywords" rows="3" 
                                              placeholder="Separate keywords with commas"><?= old('meta_keywords', getSetting('meta_keywords', 'Hindi news, breaking news, राजनीति, खेल, मनोरंजन, तकनीक')) ?></textarea>
                                    <div class="form-text">
                                        Separate keywords with commas. Focus on relevant terms for your content.
                                    </div>
                                </div>

                                <!-- Submit Button -->
                                <div class="d-flex justify-content-between">
                                    <a href="<?= base_url('admin/settings') ?>" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Update SEO Settings
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- SEO Preview -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-eye me-2"></i>Search Result Preview
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="seo-preview border rounded p-3 bg-light">
                                <div class="mb-2">
                                    <h6 class="text-primary mb-1" id="preview-title"><?= getSetting('site_name', 'BBC News Portal') ?></h6>
                                    <small class="text-success">www.yourwebsite.com</small>
                                </div>
                                <p class="small text-muted mb-0" id="preview-description">
                                    <?= getSetting('meta_description', 'Latest Hindi news, breaking news, politics, sports, entertainment, technology and business news') ?>
                                </p>
                            </div>
                            <small class="text-muted mt-2 d-block">This is how your site might appear in Google search results</small>
                        </div>
                    </div>

                    <!-- SEO Guidelines -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-lightbulb me-2"></i>SEO Best Practices
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled small mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>Meta Description:</strong> 150-160 characters, compelling and descriptive
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>Keywords:</strong> Use relevant, specific terms your audience searches for
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>Language:</strong> Include both Hindi and English keywords
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>Relevance:</strong> Ensure keywords match your content
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <strong>Updates:</strong> Review and update regularly
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- Keyword Suggestions -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-tags me-2"></i>Keyword Suggestions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="small">
                                <div class="mb-2">
                                    <strong>News Categories:</strong><br>
                                    <span class="text-muted">समाचार, ब्रेकिंग न्यूज़, राजनीति, खेल, मनोरंजन, तकनीक, व्यापार</span>
                                </div>
                                <div class="mb-2">
                                    <strong>English Terms:</strong><br>
                                    <span class="text-muted">Hindi news, breaking news, politics, sports, entertainment, technology</span>
                                </div>
                                <div class="mb-2">
                                    <strong>Location-based:</strong><br>
                                    <span class="text-muted">India news, Delhi news, Mumbai news, भारत समाचार</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- SEO Tools -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-tools me-2"></i>SEO Tools
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="https://search.google.com/search-console" target="_blank" class="btn btn-outline-primary btn-sm">
                                    <i class="fab fa-google me-2"></i>Google Search Console
                                </a>
                                <a href="https://www.google.com/webmasters/tools/keyword-planner" target="_blank" class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-search me-2"></i>Keyword Planner
                                </a>
                                <a href="https://developers.google.com/speed/pagespeed/insights/" target="_blank" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-tachometer-alt me-2"></i>PageSpeed Insights
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Character counter for meta description
    const metaDescInput = document.getElementById('meta_description');
    const descCount = document.getElementById('desc-count');
    const previewDesc = document.getElementById('preview-description');
    
    function updateDescriptionCount() {
        const length = metaDescInput.value.length;
        descCount.textContent = length;
        
        // Color coding based on length
        if (length < 120) {
            descCount.className = 'text-warning';
        } else if (length <= 160) {
            descCount.className = 'text-success';
        } else {
            descCount.className = 'text-danger';
        }
        
        // Update preview
        previewDesc.textContent = metaDescInput.value || 'Latest Hindi news, breaking news, politics, sports, entertainment, technology and business news';
    }
    
    metaDescInput.addEventListener('input', updateDescriptionCount);
    
    // Initialize counter
    updateDescriptionCount();
    
    // Keywords formatting helper
    const keywordsInput = document.getElementById('meta_keywords');
    keywordsInput.addEventListener('blur', function() {
        // Clean up keywords: remove extra spaces, ensure comma separation
        const keywords = this.value
            .split(',')
            .map(keyword => keyword.trim())
            .filter(keyword => keyword.length > 0)
            .join(', ');
        
        this.value = keywords;
    });
    
    // Keyword suggestions click to add
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('keyword-suggestion')) {
            const keyword = e.target.textContent;
            const currentKeywords = keywordsInput.value;
            
            if (currentKeywords) {
                keywordsInput.value = currentKeywords + ', ' + keyword;
            } else {
                keywordsInput.value = keyword;
            }
        }
    });
});
</script>
<?= $this->endSection() ?>
