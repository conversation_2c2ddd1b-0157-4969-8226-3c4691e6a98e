DEBUG - 2025-06-24 07:40:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:40:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:40:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:40:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:40:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:40:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:40:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:40:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:44:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:44:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:44:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:44:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:45:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:45:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:46:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:46:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:48:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:48:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:48:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-24 07:48:48 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "admin/news/create.php"
[Method: GET, Route: admin/news/create]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('admin/news/create.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('admin/news/create', [], true)
 3 APPPATH\Controllers\Admin\News.php(37): view('admin/news/create', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Admin\News->create()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\News))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-06-24 07:48:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:48:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:48:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:51:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:51:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:51:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:51:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:51:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:51:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:51:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:51:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:52:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:53:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-24 07:53:01 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "admin/categories/index.php"
[Method: GET, Route: admin/categories]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('admin/categories/index.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('admin/categories/index', [], true)
 3 APPPATH\Controllers\Admin\Categories.php(24): view('admin/categories/index', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Admin\Categories->index()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Categories))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-06-24 07:53:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-24 07:53:07 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "admin/categories/index.php"
[Method: GET, Route: admin/categories]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('admin/categories/index.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('admin/categories/index', [], true)
 3 APPPATH\Controllers\Admin\Categories.php(24): view('admin/categories/index', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Admin\Categories->index()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Categories))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-06-24 07:53:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:54:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:54:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:54:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-24 07:54:20 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "admin/categories/edit.php"
[Method: GET, Route: admin/categories/edit/5]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('admin/categories/edit.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('admin/categories/edit', [], true)
 3 APPPATH\Controllers\Admin\Categories.php(75): view('admin/categories/edit', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Admin\Categories->edit('5')
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Categories))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-06-24 07:54:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-24 07:54:24 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "admin/categories/create.php"
[Method: GET, Route: admin/categories/create]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('admin/categories/create.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('admin/categories/create', [], true)
 3 APPPATH\Controllers\Admin\Categories.php(33): view('admin/categories/create', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Admin\Categories->create()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Categories))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-06-24 07:55:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:55:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:55:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:55:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:55:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:55:26 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:55:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:55:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:55:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 07:55:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-24 07:55:31 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "admin/tags/create.php"
[Method: GET, Route: admin/tags/create]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('admin/tags/create.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('admin/tags/create', [], true)
 3 APPPATH\Controllers\Admin\Tags.php(33): view('admin/tags/create', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Admin\Tags->create()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Tags))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-06-24 07:55:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:05:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:05:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-24 08:05:43 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "admin/ads/index.php"
[Method: GET, Route: admin/ads]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('admin/ads/index.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('admin/ads/index', [], true)
 3 APPPATH\Controllers\Admin\Ads.php(24): view('admin/ads/index', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Admin\Ads->index()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Ads))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-06-24 08:05:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-24 08:05:45 --> CodeIgniter\View\Exceptions\ViewException: Invalid file: "admin/ads/index.php"
[Method: GET, Route: admin/ads]
in SYSTEMPATH\Exceptions\FrameworkException.php on line 39.
 1 SYSTEMPATH\View\View.php(212): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('admin/ads/index.php')
 2 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('admin/ads/index', [], true)
 3 APPPATH\Controllers\Admin\Ads.php(24): view('admin/ads/index', [...])
 4 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Admin\Ads->index()
 5 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Ads))
 6 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 8 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 9 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-06-24 08:08:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:08:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:08:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:09:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:09:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:09:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:15:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:15:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:16:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:16:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:16:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:16:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:16:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:16:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:16:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:16:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:16:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:16:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:16:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:17:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:17:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:17:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:17:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:17:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:17:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-24 08:20:21 --> CodeIgniter\Database\Exceptions\DataException: There is no data to update.
[Method: GET, Route: news/cricket-vishwa-cup-mein-bharat-ki-shandar-jeet]
in SYSTEMPATH\BaseModel.php on line 1014.
 1 SYSTEMPATH\BaseModel.php(1014): CodeIgniter\Database\Exceptions\DataException::forEmptyDataset('update')
 2 SYSTEMPATH\Model.php(863): CodeIgniter\BaseModel->update(null, [])
 3 APPPATH\Models\News.php(111): CodeIgniter\Model->update()
 4 APPPATH\Controllers\Home.php(48): App\Models\News->incrementViews('2')
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->news('cricket-vishwa-cup-mein-bharat-ki-shandar-jeet')
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-06-24 08:21:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:22:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:22:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:22:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:22:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-24 08:22:09 --> CodeIgniter\Database\Exceptions\DataException: There is no data to update.
[Method: GET, Route: news/bharat-mein-takneeki-kranti-ka-naya-daur]
in SYSTEMPATH\BaseModel.php on line 1014.
 1 SYSTEMPATH\BaseModel.php(1014): CodeIgniter\Database\Exceptions\DataException::forEmptyDataset('update')
 2 SYSTEMPATH\Model.php(863): CodeIgniter\BaseModel->update(null, [])
 3 APPPATH\Models\News.php(111): CodeIgniter\Model->update()
 4 APPPATH\Controllers\Home.php(48): App\Models\News->incrementViews('1')
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->news('bharat-mein-takneeki-kranti-ka-naya-daur')
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('D:\\xampp\\htdocs\\bbc_news\\public\\index.php')
DEBUG - 2025-06-24 08:22:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:22:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:22:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:22:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:22:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-24 08:24:05 --> CodeIgniter\Database\Exceptions\DataException: There is no data to update.
[Method: GET, Route: news/bharat-mein-takneeki-kranti-ka-naya-daur]
in SYSTEMPATH\BaseModel.php on line 1014.
 1 SYSTEMPATH\BaseModel.php(1014): CodeIgniter\Database\Exceptions\DataException::forEmptyDataset('update')
 2 SYSTEMPATH\Model.php(863): CodeIgniter\BaseModel->update(null, [])
 3 APPPATH\Models\News.php(111): CodeIgniter\Model->update()
 4 APPPATH\Controllers\Home.php(52): App\Models\News->incrementViews('1')
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->news('bharat-mein-takneeki-kranti-ka-naya-daur')
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('D:\\xampp\\htdocs\\bbc_news\\public\\index.php')
DEBUG - 2025-06-24 08:24:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-24 08:24:50 --> CodeIgniter\Database\Exceptions\DataException: There is no data to update.
[Method: GET, Route: news/bharat-mein-takneeki-kranti-ka-naya-daur]
in SYSTEMPATH\BaseModel.php on line 1014.
 1 SYSTEMPATH\BaseModel.php(1014): CodeIgniter\Database\Exceptions\DataException::forEmptyDataset('update')
 2 SYSTEMPATH\Model.php(863): CodeIgniter\BaseModel->update(null, [])
 3 APPPATH\Models\News.php(111): CodeIgniter\Model->update()
 4 APPPATH\Controllers\Home.php(52): App\Models\News->incrementViews('1')
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->news('bharat-mein-takneeki-kranti-ka-naya-daur')
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('D:\\xampp\\htdocs\\bbc_news\\public\\index.php')
CRITICAL - 2025-06-24 08:25:01 --> CodeIgniter\Database\Exceptions\DataException: There is no data to update.
[Method: GET, Route: news/cricket-vishwa-cup-mein-bharat-ki-shandar-jeet]
in SYSTEMPATH\BaseModel.php on line 1014.
 1 SYSTEMPATH\BaseModel.php(1014): CodeIgniter\Database\Exceptions\DataException::forEmptyDataset('update')
 2 SYSTEMPATH\Model.php(863): CodeIgniter\BaseModel->update(null, [])
 3 APPPATH\Models\News.php(111): CodeIgniter\Model->update()
 4 APPPATH\Controllers\Home.php(52): App\Models\News->incrementViews('2')
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->news('cricket-vishwa-cup-mein-bharat-ki-shandar-jeet')
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('D:\\xampp\\htdocs\\bbc_news\\public\\index.php')
DEBUG - 2025-06-24 08:25:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:25:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:25:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:25:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:25:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:25:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-24 08:26:08 --> CodeIgniter\Database\Exceptions\DataException: There is no data to update.
[Method: GET, Route: news/cricket-vishwa-cup-mein-bharat-ki-shandar-jeet]
in SYSTEMPATH\BaseModel.php on line 1014.
 1 SYSTEMPATH\BaseModel.php(1014): CodeIgniter\Database\Exceptions\DataException::forEmptyDataset('update')
 2 SYSTEMPATH\Model.php(863): CodeIgniter\BaseModel->update(null, [])
 3 APPPATH\Models\News.php(111): CodeIgniter\Model->update()
 4 APPPATH\Controllers\Home.php(52): App\Models\News->incrementViews('2')
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->news('cricket-vishwa-cup-mein-bharat-ki-shandar-jeet')
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('D:\\xampp\\htdocs\\bbc_news\\public\\index.php')
DEBUG - 2025-06-24 08:26:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:27:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:27:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:28:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-24 08:30:07 --> CodeIgniter\HTTP\Exceptions\BadRequestException: The URI you submitted has disallowed characters: "पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे"
[Method: GET, Route: news/%E0%A4%AA%E0%A5%80%E0%A4%8F%E0%A4%AE-%E0%A4%AE%E0%A5%8B%E0%A4%A6%E0%A5%80-12-%E0%A4%9C%E0%A5%82%E0%A4%A8-%E0%A4%95%E0%A5%8B-%E0%A4%9F%E0%A5%80%E0%A4%A1%E0%A5%80%E0%A4%AA%E0%A5%80-%E0%A4%AA%E0%A5%8D%E0%A4%B0%E0%A4%AE%E0%A5%81%E0%A4%96-%E0%A4%9A%E0%A4%82%E0%A4%A6%E0%A5%8D%E0%A4%B0%E0%A4%AC%E0%A4%BE%E0%A4%AC%E0%A5%82-%E0%A4%A8%E0%A4%BE%E0%A4%AF%E0%A4%A1%E0%A5%82-%E0%A4%95%E0%A5%87-%E0%A4%B6%E0%A4%AA%E0%A4%A5-%E0%A4%97%E0%A5%8D%E0%A4%B0%E0%A4%B9%E0%A4%A3-%E0%A4%B8%E0%A4%AE%E0%A4%BE%E0%A4%B0%E0%A5%8B%E0%A4%B9-%E0%A4%AE%E0%A5%87%E0%A4%82-%E0%A4%B6%E0%A4%BE%E0%A4%AE%E0%A4%BF%E0%A4%B2-%E0%A4%B9%E0%A5%8B%E0%A4%82%E0%A4%97%E0%A5%87]
in SYSTEMPATH\Router\Router.php on line 741.
 1 SYSTEMPATH\Router\Router.php(207): CodeIgniter\Router\Router->checkDisallowedChars('news/पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे')
 2 SYSTEMPATH\CodeIgniter.php(832): CodeIgniter\Router\Router->handle('news/पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे')
 3 SYSTEMPATH\CodeIgniter.php(455): CodeIgniter\CodeIgniter->tryToRouteIt(Object(CodeIgniter\Router\RouteCollection))
 4 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 7 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-24 08:32:05 --> CodeIgniter\HTTP\Exceptions\BadRequestException: The URI you submitted has disallowed characters: "पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे"
[Method: GET, Route: news/%E0%A4%AA%E0%A5%80%E0%A4%8F%E0%A4%AE-%E0%A4%AE%E0%A5%8B%E0%A4%A6%E0%A5%80-12-%E0%A4%9C%E0%A5%82%E0%A4%A8-%E0%A4%95%E0%A5%8B-%E0%A4%9F%E0%A5%80%E0%A4%A1%E0%A5%80%E0%A4%AA%E0%A5%80-%E0%A4%AA%E0%A5%8D%E0%A4%B0%E0%A4%AE%E0%A5%81%E0%A4%96-%E0%A4%9A%E0%A4%82%E0%A4%A6%E0%A5%8D%E0%A4%B0%E0%A4%AC%E0%A4%BE%E0%A4%AC%E0%A5%82-%E0%A4%A8%E0%A4%BE%E0%A4%AF%E0%A4%A1%E0%A5%82-%E0%A4%95%E0%A5%87-%E0%A4%B6%E0%A4%AA%E0%A4%A5-%E0%A4%97%E0%A5%8D%E0%A4%B0%E0%A4%B9%E0%A4%A3-%E0%A4%B8%E0%A4%AE%E0%A4%BE%E0%A4%B0%E0%A5%8B%E0%A4%B9-%E0%A4%AE%E0%A5%87%E0%A4%82-%E0%A4%B6%E0%A4%BE%E0%A4%AE%E0%A4%BF%E0%A4%B2-%E0%A4%B9%E0%A5%8B%E0%A4%82%E0%A4%97%E0%A5%87]
in SYSTEMPATH\Router\Router.php on line 741.
 1 SYSTEMPATH\Router\Router.php(207): CodeIgniter\Router\Router->checkDisallowedChars('news/पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे')
 2 SYSTEMPATH\CodeIgniter.php(832): CodeIgniter\Router\Router->handle('news/पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे')
 3 SYSTEMPATH\CodeIgniter.php(455): CodeIgniter\CodeIgniter->tryToRouteIt(Object(CodeIgniter\Router\RouteCollection))
 4 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 7 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-24 08:34:51 --> CodeIgniter\HTTP\Exceptions\BadRequestException: The URI you submitted has disallowed characters: "पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे"
[Method: GET, Route: news/%E0%A4%AA%E0%A5%80%E0%A4%8F%E0%A4%AE-%E0%A4%AE%E0%A5%8B%E0%A4%A6%E0%A5%80-12-%E0%A4%9C%E0%A5%82%E0%A4%A8-%E0%A4%95%E0%A5%8B-%E0%A4%9F%E0%A5%80%E0%A4%A1%E0%A5%80%E0%A4%AA%E0%A5%80-%E0%A4%AA%E0%A5%8D%E0%A4%B0%E0%A4%AE%E0%A5%81%E0%A4%96-%E0%A4%9A%E0%A4%82%E0%A4%A6%E0%A5%8D%E0%A4%B0%E0%A4%AC%E0%A4%BE%E0%A4%AC%E0%A5%82-%E0%A4%A8%E0%A4%BE%E0%A4%AF%E0%A4%A1%E0%A5%82-%E0%A4%95%E0%A5%87-%E0%A4%B6%E0%A4%AA%E0%A4%A5-%E0%A4%97%E0%A5%8D%E0%A4%B0%E0%A4%B9%E0%A4%A3-%E0%A4%B8%E0%A4%AE%E0%A4%BE%E0%A4%B0%E0%A5%8B%E0%A4%B9-%E0%A4%AE%E0%A5%87%E0%A4%82-%E0%A4%B6%E0%A4%BE%E0%A4%AE%E0%A4%BF%E0%A4%B2-%E0%A4%B9%E0%A5%8B%E0%A4%82%E0%A4%97%E0%A5%87]
in SYSTEMPATH\Router\Router.php on line 741.
 1 SYSTEMPATH\Router\Router.php(207): CodeIgniter\Router\Router->checkDisallowedChars('news/पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे')
 2 SYSTEMPATH\CodeIgniter.php(832): CodeIgniter\Router\Router->handle('news/पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे')
 3 SYSTEMPATH\CodeIgniter.php(455): CodeIgniter\CodeIgniter->tryToRouteIt(Object(CodeIgniter\Router\RouteCollection))
 4 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 7 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-06-24 08:35:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:36:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:36:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:36:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:36:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:36:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:36:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:37:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:37:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:40:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 08:40:18 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-24 08:40:39 --> CodeIgniter\HTTP\Exceptions\BadRequestException: The URI you submitted has disallowed characters: "पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे"
[Method: GET, Route: news/%E0%A4%AA%E0%A5%80%E0%A4%8F%E0%A4%AE-%E0%A4%AE%E0%A5%8B%E0%A4%A6%E0%A5%80-12-%E0%A4%9C%E0%A5%82%E0%A4%A8-%E0%A4%95%E0%A5%8B-%E0%A4%9F%E0%A5%80%E0%A4%A1%E0%A5%80%E0%A4%AA%E0%A5%80-%E0%A4%AA%E0%A5%8D%E0%A4%B0%E0%A4%AE%E0%A5%81%E0%A4%96-%E0%A4%9A%E0%A4%82%E0%A4%A6%E0%A5%8D%E0%A4%B0%E0%A4%AC%E0%A4%BE%E0%A4%AC%E0%A5%82-%E0%A4%A8%E0%A4%BE%E0%A4%AF%E0%A4%A1%E0%A5%82-%E0%A4%95%E0%A5%87-%E0%A4%B6%E0%A4%AA%E0%A4%A5-%E0%A4%97%E0%A5%8D%E0%A4%B0%E0%A4%B9%E0%A4%A3-%E0%A4%B8%E0%A4%AE%E0%A4%BE%E0%A4%B0%E0%A5%8B%E0%A4%B9-%E0%A4%AE%E0%A5%87%E0%A4%82-%E0%A4%B6%E0%A4%BE%E0%A4%AE%E0%A4%BF%E0%A4%B2-%E0%A4%B9%E0%A5%8B%E0%A4%82%E0%A4%97%E0%A5%87]
in SYSTEMPATH\Router\Router.php on line 741.
 1 SYSTEMPATH\Router\Router.php(207): CodeIgniter\Router\Router->checkDisallowedChars('news/पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे')
 2 SYSTEMPATH\CodeIgniter.php(832): CodeIgniter\Router\Router->handle('news/पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे')
 3 SYSTEMPATH\CodeIgniter.php(455): CodeIgniter\CodeIgniter->tryToRouteIt(Object(CodeIgniter\Router\RouteCollection))
 4 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 7 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-24 08:42:50 --> CodeIgniter\HTTP\Exceptions\BadRequestException: The URI you submitted has disallowed characters: "पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे"
[Method: GET, Route: news/%E0%A4%AA%E0%A5%80%E0%A4%8F%E0%A4%AE-%E0%A4%AE%E0%A5%8B%E0%A4%A6%E0%A5%80-12-%E0%A4%9C%E0%A5%82%E0%A4%A8-%E0%A4%95%E0%A5%8B-%E0%A4%9F%E0%A5%80%E0%A4%A1%E0%A5%80%E0%A4%AA%E0%A5%80-%E0%A4%AA%E0%A5%8D%E0%A4%B0%E0%A4%AE%E0%A5%81%E0%A4%96-%E0%A4%9A%E0%A4%82%E0%A4%A6%E0%A5%8D%E0%A4%B0%E0%A4%AC%E0%A4%BE%E0%A4%AC%E0%A5%82-%E0%A4%A8%E0%A4%BE%E0%A4%AF%E0%A4%A1%E0%A5%82-%E0%A4%95%E0%A5%87-%E0%A4%B6%E0%A4%AA%E0%A4%A5-%E0%A4%97%E0%A5%8D%E0%A4%B0%E0%A4%B9%E0%A4%A3-%E0%A4%B8%E0%A4%AE%E0%A4%BE%E0%A4%B0%E0%A5%8B%E0%A4%B9-%E0%A4%AE%E0%A5%87%E0%A4%82-%E0%A4%B6%E0%A4%BE%E0%A4%AE%E0%A4%BF%E0%A4%B2-%E0%A4%B9%E0%A5%8B%E0%A4%82%E0%A4%97%E0%A5%87]
in SYSTEMPATH\Router\Router.php on line 741.
 1 SYSTEMPATH\Router\Router.php(207): CodeIgniter\Router\Router->checkDisallowedChars('news/पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे')
 2 SYSTEMPATH\CodeIgniter.php(832): CodeIgniter\Router\Router->handle('news/पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे')
 3 SYSTEMPATH\CodeIgniter.php(455): CodeIgniter\CodeIgniter->tryToRouteIt(Object(CodeIgniter\Router\RouteCollection))
 4 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 7 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-24 08:42:53 --> CodeIgniter\Database\Exceptions\DataException: There is no data to update.
[Method: GET, Route: news/cricket-vishwa-cup-mein-bharat-ki-shandar-jeet]
in SYSTEMPATH\BaseModel.php on line 1014.
 1 SYSTEMPATH\BaseModel.php(1014): CodeIgniter\Database\Exceptions\DataException::forEmptyDataset('update')
 2 SYSTEMPATH\Model.php(863): CodeIgniter\BaseModel->update(null, [])
 3 APPPATH\Models\News.php(125): CodeIgniter\Model->update()
 4 APPPATH\Controllers\Home.php(52): App\Models\News->incrementViews('2')
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->news('cricket-vishwa-cup-mein-bharat-ki-shandar-jeet')
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-06-24 08:43:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-24 08:43:46 --> CodeIgniter\HTTP\Exceptions\BadRequestException: The URI you submitted has disallowed characters: "पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे"
[Method: GET, Route: news/%E0%A4%AA%E0%A5%80%E0%A4%8F%E0%A4%AE-%E0%A4%AE%E0%A5%8B%E0%A4%A6%E0%A5%80-12-%E0%A4%9C%E0%A5%82%E0%A4%A8-%E0%A4%95%E0%A5%8B-%E0%A4%9F%E0%A5%80%E0%A4%A1%E0%A5%80%E0%A4%AA%E0%A5%80-%E0%A4%AA%E0%A5%8D%E0%A4%B0%E0%A4%AE%E0%A5%81%E0%A4%96-%E0%A4%9A%E0%A4%82%E0%A4%A6%E0%A5%8D%E0%A4%B0%E0%A4%AC%E0%A4%BE%E0%A4%AC%E0%A5%82-%E0%A4%A8%E0%A4%BE%E0%A4%AF%E0%A4%A1%E0%A5%82-%E0%A4%95%E0%A5%87-%E0%A4%B6%E0%A4%AA%E0%A4%A5-%E0%A4%97%E0%A5%8D%E0%A4%B0%E0%A4%B9%E0%A4%A3-%E0%A4%B8%E0%A4%AE%E0%A4%BE%E0%A4%B0%E0%A5%8B%E0%A4%B9-%E0%A4%AE%E0%A5%87%E0%A4%82-%E0%A4%B6%E0%A4%BE%E0%A4%AE%E0%A4%BF%E0%A4%B2-%E0%A4%B9%E0%A5%8B%E0%A4%82%E0%A4%97%E0%A5%87]
in SYSTEMPATH\Router\Router.php on line 741.
 1 SYSTEMPATH\Router\Router.php(207): CodeIgniter\Router\Router->checkDisallowedChars('news/पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे')
 2 SYSTEMPATH\CodeIgniter.php(832): CodeIgniter\Router\Router->handle('news/पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे')
 3 SYSTEMPATH\CodeIgniter.php(455): CodeIgniter\CodeIgniter->tryToRouteIt(Object(CodeIgniter\Router\RouteCollection))
 4 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 7 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-24 08:44:02 --> CodeIgniter\HTTP\Exceptions\BadRequestException: The URI you submitted has disallowed characters: "पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे"
[Method: GET, Route: news/%E0%A4%AA%E0%A5%80%E0%A4%8F%E0%A4%AE-%E0%A4%AE%E0%A5%8B%E0%A4%A6%E0%A5%80-12-%E0%A4%9C%E0%A5%82%E0%A4%A8-%E0%A4%95%E0%A5%8B-%E0%A4%9F%E0%A5%80%E0%A4%A1%E0%A5%80%E0%A4%AA%E0%A5%80-%E0%A4%AA%E0%A5%8D%E0%A4%B0%E0%A4%AE%E0%A5%81%E0%A4%96-%E0%A4%9A%E0%A4%82%E0%A4%A6%E0%A5%8D%E0%A4%B0%E0%A4%AC%E0%A4%BE%E0%A4%AC%E0%A5%82-%E0%A4%A8%E0%A4%BE%E0%A4%AF%E0%A4%A1%E0%A5%82-%E0%A4%95%E0%A5%87-%E0%A4%B6%E0%A4%AA%E0%A4%A5-%E0%A4%97%E0%A5%8D%E0%A4%B0%E0%A4%B9%E0%A4%A3-%E0%A4%B8%E0%A4%AE%E0%A4%BE%E0%A4%B0%E0%A5%8B%E0%A4%B9-%E0%A4%AE%E0%A5%87%E0%A4%82-%E0%A4%B6%E0%A4%BE%E0%A4%AE%E0%A4%BF%E0%A4%B2-%E0%A4%B9%E0%A5%8B%E0%A4%82%E0%A4%97%E0%A5%87]
in SYSTEMPATH\Router\Router.php on line 741.
 1 SYSTEMPATH\Router\Router.php(207): CodeIgniter\Router\Router->checkDisallowedChars('news/पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे')
 2 SYSTEMPATH\CodeIgniter.php(832): CodeIgniter\Router\Router->handle('news/पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे')
 3 SYSTEMPATH\CodeIgniter.php(455): CodeIgniter\CodeIgniter->tryToRouteIt(Object(CodeIgniter\Router\RouteCollection))
 4 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 7 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-24 08:44:11 --> CodeIgniter\Database\Exceptions\DataException: There is no data to update.
[Method: GET, Route: news/bharata-ma-technology-karata-ka-naya-dara]
in SYSTEMPATH\BaseModel.php on line 1014.
 1 SYSTEMPATH\BaseModel.php(1014): CodeIgniter\Database\Exceptions\DataException::forEmptyDataset('update')
 2 SYSTEMPATH\Model.php(863): CodeIgniter\BaseModel->update(null, [])
 3 APPPATH\Models\News.php(125): CodeIgniter\Model->update()
 4 APPPATH\Controllers\Home.php(56): App\Models\News->incrementViews('1')
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->news('bharata-ma-technology-karata-ka-naya-dara')
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-24 08:44:18 --> CodeIgniter\Database\Exceptions\DataException: There is no data to update.
[Method: GET, Route: news/balavada-ma-naee-phalama-ka-dhamakadara-taralara-ralaja]
in SYSTEMPATH\BaseModel.php on line 1014.
 1 SYSTEMPATH\BaseModel.php(1014): CodeIgniter\Database\Exceptions\DataException::forEmptyDataset('update')
 2 SYSTEMPATH\Model.php(863): CodeIgniter\BaseModel->update(null, [])
 3 APPPATH\Models\News.php(125): CodeIgniter\Model->update()
 4 APPPATH\Controllers\Home.php(56): App\Models\News->incrementViews('3')
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->news('balavada-ma-naee-phalama-ka-dhamakadara-taralara-ralaja')
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-24 08:44:45 --> CodeIgniter\Database\Exceptions\DataException: There is no data to update.
[Method: GET, Route: news/bharata-ma-technology-karata-ka-naya-dara]
in SYSTEMPATH\BaseModel.php on line 1014.
 1 SYSTEMPATH\BaseModel.php(1014): CodeIgniter\Database\Exceptions\DataException::forEmptyDataset('update')
 2 SYSTEMPATH\Model.php(863): CodeIgniter\BaseModel->update(null, [])
 3 APPPATH\Models\News.php(125): CodeIgniter\Model->update()
 4 APPPATH\Controllers\Home.php(56): App\Models\News->incrementViews('1')
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->news('bharata-ma-technology-karata-ka-naya-dara')
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('D:\\xampp\\htdocs\\bbc_news\\public\\index.php')
CRITICAL - 2025-06-24 08:44:56 --> CodeIgniter\Database\Exceptions\DataException: There is no data to update.
[Method: GET, Route: news/rajaya-sabha-ma-important-vadhayaka-parata]
in SYSTEMPATH\BaseModel.php on line 1014.
 1 SYSTEMPATH\BaseModel.php(1014): CodeIgniter\Database\Exceptions\DataException::forEmptyDataset('update')
 2 SYSTEMPATH\Model.php(863): CodeIgniter\BaseModel->update(null, [])
 3 APPPATH\Models\News.php(125): CodeIgniter\Model->update()
 4 APPPATH\Controllers\Home.php(56): App\Models\News->incrementViews('5')
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->news('rajaya-sabha-ma-important-vadhayaka-parata')
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-24 08:45:34 --> CodeIgniter\Database\Exceptions\DataException: There is no data to update.
[Method: GET, Route: news/karakata-vashava-kapa-ma-bharata-ka-shanadara-jata]
in SYSTEMPATH\BaseModel.php on line 1014.
 1 SYSTEMPATH\BaseModel.php(1014): CodeIgniter\Database\Exceptions\DataException::forEmptyDataset('update')
 2 SYSTEMPATH\Model.php(863): CodeIgniter\BaseModel->update(null, [])
 3 APPPATH\Models\News.php(125): CodeIgniter\Model->update()
 4 APPPATH\Controllers\Home.php(56): App\Models\News->incrementViews('2')
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->news('karakata-vashava-kapa-ma-bharata-ka-shanadara-jata')
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('D:\\xampp\\htdocs\\bbc_news\\public\\index.php')
DEBUG - 2025-06-24 08:45:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-24 08:45:57 --> CodeIgniter\Database\Exceptions\DataException: There is no data to update.
[Method: GET, Route: news/karakata-vashava-kapa-ma-bharata-ka-shanadara-jata]
in SYSTEMPATH\BaseModel.php on line 1014.
 1 SYSTEMPATH\BaseModel.php(1014): CodeIgniter\Database\Exceptions\DataException::forEmptyDataset('update')
 2 SYSTEMPATH\Model.php(863): CodeIgniter\BaseModel->update(null, [])
 3 APPPATH\Models\News.php(125): CodeIgniter\Model->update()
 4 APPPATH\Controllers\Home.php(56): App\Models\News->incrementViews('2')
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->news('karakata-vashava-kapa-ma-bharata-ka-shanadara-jata')
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
CRITICAL - 2025-06-24 08:46:15 --> CodeIgniter\Database\Exceptions\DataException: There is no data to update.
[Method: GET, Route: news/karakata-vashava-kapa-ma-bharata-ka-shanadara-jata]
in SYSTEMPATH\BaseModel.php on line 1014.
 1 SYSTEMPATH\BaseModel.php(1014): CodeIgniter\Database\Exceptions\DataException::forEmptyDataset('update')
 2 SYSTEMPATH\Model.php(863): CodeIgniter\BaseModel->update(null, [])
 3 APPPATH\Models\News.php(125): CodeIgniter\Model->update()
 4 APPPATH\Controllers\Home.php(56): App\Models\News->incrementViews('2')
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->news('karakata-vashava-kapa-ma-bharata-ka-shanadara-jata')
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-06-24 09:10:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:10:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:14:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:14:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:14:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:14:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:14:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:14:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:14:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:14:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:14:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:14:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:14:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:14:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:14:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:14:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:14:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:14:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:14:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:14:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:14:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:14:51 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:14:52 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:14:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:15:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:15:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:15:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:15:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:15:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:15:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:15:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:15:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:15:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:15:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:16:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:16:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:16:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:16:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:20:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:20:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:20:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:20:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:20:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:20:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:21:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:21:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:21:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:21:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:25:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:27:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:27:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:27:50 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:27:54 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:28:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:29:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:29:30 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:29:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:29:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:30:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:30:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:30:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:33:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:34:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:34:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:34:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:35:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:35:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:35:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:35:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:36:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:37:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:37:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:37:02 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:37:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:37:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:37:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:39:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:39:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:39:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:39:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:40:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:40:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:40:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:40:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:40:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:40:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:40:13 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:40:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:40:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:40:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:40:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:40:34 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:40:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:40:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:43:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:44:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:44:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:44:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:44:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:45:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:45:24 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:45:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:45:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:45:33 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:45:35 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:45:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:45:47 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:45:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:46:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:46:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:46:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:46:37 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:46:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:46:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:46:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:49:25 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:49:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:52:55 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:53:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:53:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:56:03 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:56:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:56:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-24 09:56:16 --> ErrorException: Undefined array key "options"
[Method: GET, Route: /]
in APPPATH\Views\components\poll_sidebar.php on line 20.
 1 APPPATH\Views\components\poll_sidebar.php(20): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "options"', 'D:\\xampp\\htdocs\\bbc_news\\app\\Views\\components\\poll_sidebar.php', 20)
 2 SYSTEMPATH\View\View.php(224): include('D:\\xampp\\htdocs\\bbc_news\\app\\Views\\components\\poll_sidebar.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('components/poll_sidebar', [], true)
 5 APPPATH\Views\frontend\home.php(153): view('components/poll_sidebar', [...])
 6 SYSTEMPATH\View\View.php(224): include('D:\\xampp\\htdocs\\bbc_news\\app\\Views\\frontend\\home.php')
 7 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 8 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('frontend/home', [], true)
 9 APPPATH\Controllers\Home.php(44): view('frontend/home', [...])
10 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->index()
11 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
12 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
15 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
16 SYSTEMPATH\rewrite.php(44): require_once('D:\\xampp\\htdocs\\bbc_news\\public\\index.php')
CRITICAL - 2025-06-24 09:56:26 --> ErrorException: Undefined array key "options"
[Method: GET, Route: news/bharata-ma-technology-karata-ka-naya-dara]
in APPPATH\Views\components\poll_sidebar.php on line 20.
 1 APPPATH\Views\components\poll_sidebar.php(20): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "options"', 'D:\\xampp\\htdocs\\bbc_news\\app\\Views\\components\\poll_sidebar.php', 20)
 2 SYSTEMPATH\View\View.php(224): include('D:\\xampp\\htdocs\\bbc_news\\app\\Views\\components\\poll_sidebar.php')
 3 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 4 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('components/poll_sidebar', [], true)
 5 APPPATH\Views\frontend\news_detail.php(166): view('components/poll_sidebar', [...])
 6 SYSTEMPATH\View\View.php(224): include('D:\\xampp\\htdocs\\bbc_news\\app\\Views\\frontend\\news_detail.php')
 7 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 8 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('frontend/news_detail', [], true)
 9 APPPATH\Controllers\Home.php(90): view('frontend/news_detail', [...])
10 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\Home->news('bharata-ma-technology-karata-ka-naya-dara')
11 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Home))
12 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
14 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
15 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
16 SYSTEMPATH\rewrite.php(44): require_once('D:\\xampp\\htdocs\\bbc_news\\public\\index.php')
DEBUG - 2025-06-24 09:58:01 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:58:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:58:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:58:17 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:58:23 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:58:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:58:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:58:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 09:58:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 10:01:28 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 10:01:31 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 10:02:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-24 10:02:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
