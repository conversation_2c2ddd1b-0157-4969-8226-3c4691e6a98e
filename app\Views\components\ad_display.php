<?php
/**
 * Enhanced Ad Display Component
 * Usage: <?= view('components/ad_display', ['ads' => $ads, 'position' => 'sidebar', 'class' => 'custom-class']) ?>
 */

$position = $position ?? 'default';
$class = $class ?? '';
$showLabel = $showLabel ?? false;
$responsive = $responsive ?? true;
?>

<?php if (!empty($ads)): ?>
    <div class="ad-container ad-position-<?= $position ?> <?= $class ?>">
        <?php if ($showLabel): ?>
            <div class="ad-label">
                <small class="text-muted">विज्ञापन</small>
            </div>
        <?php endif; ?>
        
        <?php foreach ($ads as $ad): ?>
            <div class="ad-item" data-ad-id="<?= $ad['id'] ?>" data-position="<?= $position ?>">
                <?php if ($ad['type'] === 'banner' && $ad['image']): ?>
                    <!-- Banner Ad -->
                    <div class="ad-banner <?= $responsive ? 'ad-responsive' : '' ?>">
                        <?php if ($ad['link_url']): ?>
                            <a href="<?= esc($ad['link_url']) ?>" 
                               target="_blank" 
                               class="ad-link"
                               onclick="trackAdClick(<?= $ad['id'] ?>)"
                               rel="noopener nofollow">
                                <img src="<?= base_url('uploads/ads/' . $ad['image']) ?>" 
                                     alt="<?= esc($ad['title']) ?>" 
                                     class="ad-image img-fluid"
                                     loading="lazy">
                            </a>
                        <?php else: ?>
                            <img src="<?= base_url('uploads/ads/' . $ad['image']) ?>" 
                                 alt="<?= esc($ad['title']) ?>" 
                                 class="ad-image img-fluid"
                                 loading="lazy">
                        <?php endif; ?>
                    </div>
                    
                <?php elseif ($ad['type'] === 'text'): ?>
                    <!-- Text Ad -->
                    <div class="ad-text">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <h6 class="card-title text-primary"><?= esc($ad['title']) ?></h6>
                                <?php if ($ad['description']): ?>
                                    <p class="card-text small"><?= esc($ad['description']) ?></p>
                                <?php endif; ?>
                                <?php if ($ad['link_url']): ?>
                                    <a href="<?= esc($ad['link_url']) ?>" 
                                       target="_blank" 
                                       class="btn btn-primary btn-sm"
                                       onclick="trackAdClick(<?= $ad['id'] ?>)"
                                       rel="noopener nofollow">
                                        और पढ़ें
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                <?php elseif ($ad['type'] === 'video' && $ad['link_url']): ?>
                    <!-- Video Ad -->
                    <div class="ad-video">
                        <div class="embed-responsive embed-responsive-16by9">
                            <?php if (strpos($ad['link_url'], 'youtube.com') !== false || strpos($ad['link_url'], 'youtu.be') !== false): ?>
                                <!-- YouTube Video -->
                                <?php
                                $videoId = '';
                                if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/', $ad['link_url'], $matches)) {
                                    $videoId = $matches[1];
                                }
                                ?>
                                <?php if ($videoId): ?>
                                    <iframe class="embed-responsive-item" 
                                            src="https://www.youtube.com/embed/<?= $videoId ?>?rel=0" 
                                            allowfullscreen
                                            onclick="trackAdClick(<?= $ad['id'] ?>)"></iframe>
                                <?php endif; ?>
                            <?php else: ?>
                                <!-- Generic Video -->
                                <video class="embed-responsive-item" controls onclick="trackAdClick(<?= $ad['id'] ?>)">
                                    <source src="<?= esc($ad['link_url']) ?>" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>
                            <?php endif; ?>
                        </div>
                        <?php if ($ad['title']): ?>
                            <div class="ad-video-title mt-2">
                                <small class="text-muted"><?= esc($ad['title']) ?></small>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>
    </div>

    <style>
        .ad-container {
            margin: 15px 0;
        }
        
        .ad-label {
            text-align: center;
            margin-bottom: 5px;
            font-size: 0.75rem;
            opacity: 0.7;
        }
        
        .ad-item {
            margin-bottom: 15px;
        }
        
        .ad-banner {
            text-align: center;
            overflow: hidden;
            border-radius: 8px;
        }
        
        .ad-responsive {
            max-width: 100%;
        }
        
        .ad-image {
            transition: transform 0.3s ease;
            border-radius: 8px;
        }
        
        .ad-link:hover .ad-image {
            transform: scale(1.02);
        }
        
        .ad-text .card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
        }
        
        .ad-text .card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
            transform: translateY(-2px);
            transition: all 0.3s ease;
        }
        
        .ad-video {
            border-radius: 8px;
            overflow: hidden;
        }
        
        /* Position-specific styles */
        .ad-position-header {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 8px;
        }
        
        .ad-position-sidebar .ad-item {
            margin-bottom: 20px;
        }
        
        .ad-position-footer {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
        }
        
        .ad-position-floating {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            z-index: 1000;
            max-width: 300px;
        }
        
        .ad-position-sticky_bottom {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 999;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            padding: 10px;
        }
        
        .ad-position-between_news {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
            margin: 20px 0;
        }
        
        .ad-position-in_article {
            float: right;
            margin: 0 0 15px 15px;
            max-width: 300px;
        }
        
        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .ad-position-floating {
                position: relative;
                right: auto;
                top: auto;
                transform: none;
                max-width: 100%;
                margin: 15px 0;
            }
            
            .ad-position-in_article {
                float: none;
                margin: 15px 0;
                max-width: 100%;
            }
            
            .ad-position-sticky_bottom {
                position: relative;
                box-shadow: none;
            }
        }
        
        /* Close button for floating ads */
        .ad-position-floating .ad-close {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(0,0,0,0.7);
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            font-size: 12px;
            cursor: pointer;
            z-index: 1001;
        }
    </style>

    <script>
        // Enhanced ad tracking
        document.addEventListener('DOMContentLoaded', function() {
            // Track impressions for ads in this component
            const ads = document.querySelectorAll('.ad-item[data-ad-id]');
            ads.forEach(function(adElement) {
                const adId = adElement.getAttribute('data-ad-id');
                const position = adElement.getAttribute('data-position');
                
                // Use Intersection Observer for better impression tracking
                const observer = new IntersectionObserver(function(entries) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting) {
                            trackAdImpression(adId, position);
                            observer.unobserve(entry.target);
                        }
                    });
                }, { threshold: 0.5 });
                
                observer.observe(adElement);
            });
            
            // Add close functionality for floating ads
            const floatingAds = document.querySelectorAll('.ad-position-floating');
            floatingAds.forEach(function(ad) {
                const closeBtn = document.createElement('button');
                closeBtn.className = 'ad-close';
                closeBtn.innerHTML = '×';
                closeBtn.onclick = function() {
                    ad.style.display = 'none';
                };
                ad.appendChild(closeBtn);
            });
        });
        
        // Enhanced tracking functions
        function trackAdImpression(adId, position) {
            fetch('<?= base_url("api/track-ad-impression") ?>/' + adId, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ position: position })
            });
        }
    </script>
<?php endif; ?>
