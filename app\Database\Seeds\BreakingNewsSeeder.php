<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class BreakingNewsSeeder extends Seeder
{
    public function run()
    {
        $data = [
            [
                'title' => 'प्रधानमंत्री मोदी ने आज नई शिक्षा नीति की घोषणा की',
                'content' => 'नई शिक्षा नीति से देश की शिक्षा व्यवस्था में क्रांतिकारी बदलाव आएगा।',
                'link_url' => null,
                'priority' => 4,
                'status' => 'active',
                'start_time' => null,
                'end_time' => null,
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'title' => 'भारतीय क्रिकेट टीम ने विश्व कप जीता - देश में जश्न का माहौल',
                'content' => 'भारतीय क्रिकेट टीम की शानदार जीत से पूरे देश में खुशी की लहर।',
                'link_url' => base_url('news/karakata-vashava-kapa-ma-bharata-ka-shanadara-jata'),
                'priority' => 3,
                'status' => 'active',
                'start_time' => null,
                'end_time' => null,
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'title' => 'दिल्ली में भारी बारिश - कई इलाकों में जलभराव',
                'content' => 'मौसम विभाग ने अगले 24 घंटों के लिए भारी बारिश की चेतावनी जारी की है।',
                'link_url' => null,
                'priority' => 2,
                'status' => 'active',
                'start_time' => null,
                'end_time' => date('Y-m-d H:i:s', strtotime('+2 days')),
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'title' => 'शेयर बाजार में तेजी - सेंसेक्स 500 अंक ऊपर',
                'content' => 'आर्थिक सुधारों की उम्मीदों से शेयर बाजार में तेजी देखी जा रही है।',
                'link_url' => null,
                'priority' => 1,
                'status' => 'active',
                'start_time' => null,
                'end_time' => null,
                'created_by' => 2,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        // Insert data
        $this->db->table('breaking_news')->insertBatch($data);

        echo "Breaking news seeded successfully!\n";
    }
}
