<?= $this->extend('frontend/layout') ?>

<?= $this->section('content') ?>
<!-- Header Ads -->
<?= view('components/ad_display', [
    'ads' => $headerAds,
    'position' => 'header',
    'showLabel' => true
]) ?>



<div class="container mt-4">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-9">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-newspaper me-2"></i>ई-पेपर आर्काइव
                </h1>
                <div>
                    <a href="<?= base_url('epaper/search') ?>" class="btn btn-outline-primary">
                        <i class="fas fa-search me-2"></i>खोजें
                    </a>
                </div>
            </div>

            <!-- Latest Epaper -->
            <?php if ($latestEpaper): ?>
                <div class="card mb-4 border-primary">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-star me-2"></i>आज का ई-पेपर
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <?php if ($latestEpaper['thumbnail']): ?>
                                    <img src="<?= base_url('uploads/epapers/thumbnails/' . $latestEpaper['thumbnail']) ?>"
                                        alt="<?= esc($latestEpaper['title']) ?>"
                                        class="img-fluid rounded shadow-sm">
                                <?php else: ?>
                                    <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 200px;">
                                        <i class="fas fa-file-pdf fa-3x text-muted"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-9">
                                <h4 class="card-title"><?= esc($latestEpaper['title']) ?></h4>
                                <?php if ($latestEpaper['description']): ?>
                                    <p class="card-text text-muted"><?= esc($latestEpaper['description']) ?></p>
                                <?php endif; ?>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            <strong>प्रकाशन तिथि:</strong> <?= formatDateHindi($latestEpaper['publication_date']) ?>
                                        </small>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">
                                            <i class="fas fa-bookmark me-1"></i>
                                            <strong>संस्करण:</strong> <?= esc($latestEpaper['edition']) ?>
                                        </small>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <small class="text-muted">
                                            <i class="fas fa-file-alt me-1"></i>
                                            <?= formatBytes($latestEpaper['file_size']) ?>
                                        </small>
                                    </div>
                                    <div class="col-md-4">
                                        <small class="text-muted">
                                            <i class="fas fa-eye me-1"></i>
                                            <?= number_format($latestEpaper['view_count']) ?> बार देखा गया
                                        </small>
                                    </div>
                                    <div class="col-md-4">
                                        <small class="text-muted">
                                            <i class="fas fa-download me-1"></i>
                                            <?= number_format($latestEpaper['download_count']) ?> डाउनलोड
                                        </small>
                                    </div>
                                </div>

                                <div class="d-flex gap-2">
                                    <a href="<?= base_url('epaper/view/' . $latestEpaper['id']) ?>"
                                        class="btn btn-primary">
                                        <i class="fas fa-eye me-2"></i>देखें
                                    </a>
                                    <a href="<?= base_url('epaper/download/' . $latestEpaper['id']) ?>"
                                        class="btn btn-success">
                                        <i class="fas fa-download me-2"></i>डाउनलोड करें
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Featured Epapers -->
            <?php if (!empty($featuredEpapers)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-star me-2"></i>विशेष ई-पेपर
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($featuredEpapers as $epaper): ?>
                                <div class="col-md-4 mb-3">
                                    <div class="card h-100 border-warning">
                                        <div class="card-body">
                                            <?php if ($epaper['thumbnail']): ?>
                                                <img src="<?= base_url('uploads/epapers/thumbnails/' . $epaper['thumbnail']) ?>"
                                                    alt="<?= esc($epaper['title']) ?>"
                                                    class="img-fluid rounded mb-2">
                                            <?php endif; ?>

                                            <h6 class="card-title"><?= esc($epaper['title']) ?></h6>
                                            <p class="card-text small text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                <?= date('d M Y', strtotime($epaper['publication_date'])) ?>
                                            </p>

                                            <div class="d-flex gap-1">
                                                <a href="<?= base_url('epaper/view/' . $epaper['id']) ?>"
                                                    class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?= base_url('epaper/download/' . $epaper['id']) ?>"
                                                    class="btn btn-sm btn-outline-success">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Recent Epapers -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>हाल के ई-पेपर
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($recentEpapers)): ?>
                        <div class="row">
                            <?php foreach ($recentEpapers as $epaper): ?>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card h-100">
                                        <div class="card-body">
                                            <?php if ($epaper['thumbnail']): ?>
                                                <img src="<?= base_url('uploads/epapers/thumbnails/' . $epaper['thumbnail']) ?>"
                                                    alt="<?= esc($epaper['title']) ?>"
                                                    class="img-fluid rounded mb-2">
                                            <?php else: ?>
                                                <div class="bg-light rounded d-flex align-items-center justify-content-center mb-2" style="height: 120px;">
                                                    <i class="fas fa-file-pdf fa-2x text-muted"></i>
                                                </div>
                                            <?php endif; ?>

                                            <h6 class="card-title"><?= esc($epaper['title']) ?></h6>

                                            <div class="small text-muted mb-2">
                                                <div><i class="fas fa-calendar me-1"></i><?= date('d M Y', strtotime($epaper['publication_date'])) ?></div>
                                                <div><i class="fas fa-bookmark me-1"></i><?= esc($epaper['edition']) ?></div>
                                                <div><i class="fas fa-file-alt me-1"></i><?= formatBytes($epaper['file_size']) ?></div>
                                            </div>

                                            <div class="d-flex gap-1">
                                                <a href="<?= base_url('epaper/view/' . $epaper['id']) ?>"
                                                    class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?= base_url('epaper/download/' . $epaper['id']) ?>"
                                                    class="btn btn-sm btn-outline-success">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">कोई ई-पेपर उपलब्ध नहीं</h5>
                            <p class="text-muted">जल्द ही नए ई-पेपर उपलब्ध होंगे।</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-3">
            <!-- Archive Navigation -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-archive me-2"></i>आर्काइव
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($archiveYears)): ?>
                        <?php foreach ($archiveYears as $yearData): ?>
                            <div class="mb-2">
                                <a href="<?= base_url('epaper/archive/' . $yearData['year']) ?>"
                                    class="btn btn-outline-secondary btn-sm w-100">
                                    <i class="fas fa-calendar-alt me-2"></i><?= $yearData['year'] ?>
                                </a>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="text-muted small">कोई आर्काइव उपलब्ध नहीं</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Sidebar Ads -->
            <?= view('components/ad_display', [
                'ads' => $sidebarAds,
                'position' => 'sidebar',
                'showLabel' => true
            ]) ?>
        </div>
    </div>
</div>

<!-- Footer Ads -->
<?= view('components/ad_display', [
    'ads' => $footerAds,
    'position' => 'footer',
    'showLabel' => true
]) ?>
<?= $this->endSection() ?>