{"name": "laminas/laminas-escaper", "description": "Securely and safely escape HTML, HTML attributes, JavaScript, CSS, and URLs", "license": "BSD-3-<PERSON><PERSON>", "keywords": ["laminas", "escaper"], "homepage": "https://laminas.dev", "support": {"docs": "https://docs.laminas.dev/laminas-escaper/", "issues": "https://github.com/laminas/laminas-escaper/issues", "source": "https://github.com/laminas/laminas-escaper", "rss": "https://github.com/laminas/laminas-escaper/releases.atom", "chat": "https://laminas.dev/chat", "forum": "https://discourse.laminas.dev"}, "config": {"sort-packages": true, "platform": {"php": "8.1.99"}, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "composer/package-versions-deprecated": true, "infection/extension-installer": true}}, "extra": {}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "ext-ctype": "*", "ext-mbstring": "*"}, "require-dev": {"infection/infection": "^0.29.8", "laminas/laminas-coding-standard": "~3.0.1", "phpunit/phpunit": "^10.5.45", "psalm/plugin-phpunit": "^0.19.2", "vimeo/psalm": "^6.6.2"}, "autoload": {"psr-4": {"Laminas\\Escaper\\": "src/"}}, "autoload-dev": {"psr-4": {"LaminasTest\\Escaper\\": "test/"}}, "scripts": {"check": ["@cs-check", "@test"], "cs-check": "phpcs", "cs-fix": "phpcbf", "static-analysis": "psalm --shepherd --stats", "test": "phpunit --colors=always", "test-coverage": "phpunit --colors=always --coverage-clover clover.xml"}, "conflict": {"zendframework/zend-escaper": "*"}}