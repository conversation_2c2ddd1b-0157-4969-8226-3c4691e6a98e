<?php

namespace App\Models;

use CodeIgniter\Model;

class Epaper extends Model
{
    protected $table            = 'epapers';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'title',
        'description',
        'publication_date',
        'edition',
        'language',
        'pdf_file',
        'thumbnail',
        'file_size',
        'page_count',
        'download_count',
        'view_count',
        'status',
        'featured',
        'uploaded_by'
    ];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules = [
        'title'            => 'required|min_length[5]|max_length[500]',
        'publication_date' => 'required|valid_date',
        'edition'          => 'required|max_length[100]',
        'language'         => 'required|max_length[50]',
        'pdf_file'         => 'required|max_length[255]',
        'status'           => 'required|in_list[active,inactive,archived]',
        'featured'         => 'in_list[0,1]',
        'uploaded_by'      => 'required|integer',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Get epapers with uploader details
     */
    public function getEpapersWithDetails($limit = null)
    {
        $builder = $this->select('epapers.*, users.full_name as uploaded_by_name')
            ->join('users', 'users.id = epapers.uploaded_by')
            ->orderBy('epapers.publication_date', 'DESC')
            ->orderBy('epapers.created_at', 'DESC');

        if ($limit) {
            $builder->limit($limit);
        }

        return $builder->findAll();
    }

    /**
     * Get active epapers
     */
    public function getActiveEpapers($limit = null)
    {
        $builder = $this->select('epapers.*, users.full_name as uploaded_by_name')
            ->join('users', 'users.id = epapers.uploaded_by')
            ->where('epapers.status', 'active')
            ->orderBy('epapers.publication_date', 'DESC')
            ->orderBy('epapers.created_at', 'DESC');

        if ($limit) {
            $builder->limit($limit);
        }

        return $builder->findAll();
    }

    /**
     * Get featured epapers
     */
    public function getFeaturedEpapers($limit = null)
    {
        $builder = $this->select('epapers.*, users.full_name as uploaded_by_name')
            ->join('users', 'users.id = epapers.uploaded_by')
            ->where('epapers.status', 'active')
            ->where('epapers.featured', 1)
            ->orderBy('epapers.publication_date', 'DESC');

        if ($limit) {
            $builder->limit($limit);
        }

        return $builder->findAll();
    }

    /**
     * Get epapers by date range
     */
    public function getEpapersByDateRange($startDate, $endDate, $limit = null)
    {
        $builder = $this->select('epapers.*, users.full_name as uploaded_by_name')
            ->join('users', 'users.id = epapers.uploaded_by')
            ->where('epapers.status', 'active')
            ->where('epapers.publication_date >=', $startDate)
            ->where('epapers.publication_date <=', $endDate)
            ->orderBy('epapers.publication_date', 'DESC');

        if ($limit) {
            $builder->limit($limit);
        }

        return $builder->findAll();
    }

    /**
     * Get epapers by year and month
     */
    public function getEpapersByMonth($year, $month)
    {
        return $this->select('epapers.*, users.full_name as uploaded_by_name')
            ->join('users', 'users.id = epapers.uploaded_by')
            ->where('epapers.status', 'active')
            ->where('YEAR(epapers.publication_date)', $year)
            ->where('MONTH(epapers.publication_date)', $month)
            ->orderBy('epapers.publication_date', 'DESC')
            ->findAll();
    }

    /**
     * Get archive years
     */
    public function getArchiveYears()
    {
        return $this->select('YEAR(publication_date) as year')
            ->where('status', 'active')
            ->groupBy('YEAR(publication_date)')
            ->orderBy('year', 'DESC')
            ->findAll();
    }

    /**
     * Get archive months for a year
     */
    public function getArchiveMonths($year)
    {
        return $this->select('MONTH(publication_date) as month, MONTHNAME(publication_date) as month_name, COUNT(*) as count')
            ->where('status', 'active')
            ->where('YEAR(publication_date)', $year)
            ->groupBy('MONTH(publication_date)')
            ->orderBy('month', 'DESC')
            ->findAll();
    }

    /**
     * Increment view count
     */
    public function incrementViewCount($id)
    {
        $builder = $this->db->table($this->table);
        $builder->set('view_count', 'view_count + 1', false);
        $builder->where('id', $id);
        return $builder->update();
    }

    /**
     * Increment download count
     */
    public function incrementDownloadCount($id)
    {
        $builder = $this->db->table($this->table);
        $builder->set('download_count', 'download_count + 1', false);
        $builder->where('id', $id);
        return $builder->update();
    }

    /**
     * Get latest epaper
     */
    public function getLatestEpaper()
    {
        return $this->select('epapers.*, users.full_name as uploaded_by_name')
            ->join('users', 'users.id = epapers.uploaded_by')
            ->where('epapers.status', 'active')
            ->orderBy('epapers.publication_date', 'DESC')
            ->orderBy('epapers.created_at', 'DESC')
            ->first();
    }

    /**
     * Search epapers
     */
    public function searchEpapers($keyword, $limit = null)
    {
        $builder = $this->select('epapers.*, users.full_name as uploaded_by_name')
            ->join('users', 'users.id = epapers.uploaded_by')
            ->where('epapers.status', 'active')
            ->groupStart()
            ->like('epapers.title', $keyword)
            ->orLike('epapers.description', $keyword)
            ->orLike('epapers.edition', $keyword)
            ->groupEnd()
            ->orderBy('epapers.publication_date', 'DESC');

        if ($limit) {
            $builder->limit($limit);
        }

        return $builder->findAll();
    }

    /**
     * Get statistics
     */
    public function getStatistics()
    {
        $totalEpapers = $this->countAllResults();
        $activeEpapers = $this->where('status', 'active')->countAllResults();
        $totalDownloads = $this->selectSum('download_count')->first()['download_count'] ?? 0;
        $totalViews = $this->selectSum('view_count')->first()['view_count'] ?? 0;

        return [
            'total_epapers' => $totalEpapers,
            'active_epapers' => $activeEpapers,
            'total_downloads' => $totalDownloads,
            'total_views' => $totalViews,
        ];
    }
}
