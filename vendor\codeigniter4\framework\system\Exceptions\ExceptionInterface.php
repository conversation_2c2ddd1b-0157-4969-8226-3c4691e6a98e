<?php

declare(strict_types=1);

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

namespace CodeIgniter\Exceptions;

/**
 * Provides a domain-level interface for broad capture
 * of all framework-related exceptions.
 *
 * catch (\CodeIgniter\Exceptions\ExceptionInterface) { ... }
 */
interface ExceptionInterface
{
}
