<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreatePollOptionsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'poll_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
            ],
            'option_text' => [
                'type'       => 'VARCHAR',
                'constraint' => 500,
            ],
            'vote_count' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'default'    => 0,
            ],
            'sort_order' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'default'    => 0,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('poll_id');
        $this->forge->addKey('sort_order');
        $this->forge->addForeignKey('poll_id', 'polls', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('poll_options');
    }

    public function down()
    {
        $this->forge->dropTable('poll_options');
    }
}
