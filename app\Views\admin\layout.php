<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title . ' - ' : '' ?>BBC News Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans Devanagari', sans-serif;
            background-color: #f8f9fa;
        }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        .sidebar.collapsed {
            width: 70px;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 2px 10px;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        .main-content {
            margin-left: 250px;
            transition: all 0.3s ease;
        }
        .main-content.expanded {
            margin-left: 70px;
        }
        .navbar {
            background: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        .sidebar-brand {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .sidebar-brand h4 {
            color: white;
            margin: 0;
        }
        .nav-text {
            transition: opacity 0.3s ease;
        }
        .sidebar.collapsed .nav-text {
            opacity: 0;
            display: none;
        }
        .toggle-btn {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            padding: 10px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .toggle-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
            }
            .main-content {
                margin-left: 70px;
            }
            .sidebar .nav-text {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-brand">
            <h4><i class="fas fa-newspaper me-2"></i><span class="nav-text">BBC News</span></h4>
            <button class="toggle-btn" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <ul class="nav flex-column mt-3">
            <li class="nav-item">
                <a class="nav-link <?= (current_url() == base_url('admin/dashboard')) ? 'active' : '' ?>" 
                   href="<?= base_url('admin/dashboard') ?>">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    <span class="nav-text">Dashboard</span>
                </a>
            </li>
            
            <?php if (session()->get('role') === 'admin'): ?>
            <li class="nav-item">
                <a class="nav-link <?= (strpos(current_url(), 'admin/users') !== false) ? 'active' : '' ?>" 
                   href="<?= base_url('admin/users') ?>">
                    <i class="fas fa-users me-2"></i>
                    <span class="nav-text">User Management</span>
                </a>
            </li>
            <?php endif; ?>
            
            <li class="nav-item">
                <a class="nav-link <?= (strpos(current_url(), 'admin/news') !== false) ? 'active' : '' ?>" 
                   href="<?= base_url('admin/news') ?>">
                    <i class="fas fa-newspaper me-2"></i>
                    <span class="nav-text">News Management</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= (strpos(current_url(), 'admin/categories') !== false) ? 'active' : '' ?>" 
                   href="<?= base_url('admin/categories') ?>">
                    <i class="fas fa-tags me-2"></i>
                    <span class="nav-text">Categories</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= (strpos(current_url(), 'admin/tags') !== false) ? 'active' : '' ?>" 
                   href="<?= base_url('admin/tags') ?>">
                    <i class="fas fa-hashtag me-2"></i>
                    <span class="nav-text">Tags</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= (strpos(current_url(), 'admin/ads') !== false) ? 'active' : '' ?>" 
                   href="<?= base_url('admin/ads') ?>">
                    <i class="fas fa-ad me-2"></i>
                    <span class="nav-text">Advertisements</span>
                </a>
            </li>
        </ul>
        
        <div class="mt-auto p-3">
            <a class="nav-link text-light" href="<?= base_url('admin/logout') ?>">
                <i class="fas fa-sign-out-alt me-2"></i>
                <span class="nav-text">Logout</span>
            </a>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content" id="main-content">
        <!-- Top Navbar -->
        <nav class="navbar navbar-expand-lg navbar-light">
            <div class="container-fluid">
                <h5 class="mb-0"><?= isset($title) ? $title : 'Dashboard' ?></h5>
                
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" 
                           id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-2"></i>
                            <?= session()->get('full_name') ?>
                            <span class="badge bg-primary ms-2"><?= ucfirst(session()->get('role')) ?></span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?= base_url('admin/logout') ?>">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <div class="container-fluid p-4">
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?= $this->renderSection('content') ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('main-content');
            
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
        }
    </script>
    <?= $this->renderSection('scripts') ?>
</body>
</html>
