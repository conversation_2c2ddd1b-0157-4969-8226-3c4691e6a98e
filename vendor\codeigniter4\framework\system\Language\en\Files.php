<?php

declare(strict_types=1);

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Files language settings
return [
    'fileNotFound'      => 'File not found: "{0}"',
    'cannotMove'        => 'Could not move file "{0}" to "{1}". Reason: {2}',
    'expectedDirectory' => '{0} expects a valid directory.',
    'expectedFile'      => '{0} expects a valid file.',
];
