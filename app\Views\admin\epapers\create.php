<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-plus me-2"></i>Upload New Epaper</h2>
                <a href="<?= base_url('admin/epapers') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Epapers
                </a>
            </div>

            <!-- Error Messages -->
            <?php if (session()->getFlashdata('errors')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <h6>Please fix the following errors:</h6>
                    <ul class="mb-0">
                        <?php foreach (session()->getFlashdata('errors') as $error): ?>
                            <li><?= esc($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Upload Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-newspaper me-2"></i>Epaper Details
                    </h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('admin/epapers/store') ?>" method="post" enctype="multipart/form-data">
                        <?= csrf_field() ?>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <!-- Title -->
                                <div class="mb-3">
                                    <label for="title" class="form-label">
                                        <i class="fas fa-heading me-2"></i>Epaper Title *
                                    </label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="<?= old('title') ?>" required maxlength="500"
                                           placeholder="Enter epaper title (e.g., BBC News - 24 June 2025)">
                                    <div class="form-text">Maximum 500 characters</div>
                                </div>

                                <!-- Description -->
                                <div class="mb-3">
                                    <label for="description" class="form-label">
                                        <i class="fas fa-align-left me-2"></i>Description
                                    </label>
                                    <textarea class="form-control" id="description" name="description" rows="3"
                                              placeholder="Optional description about this epaper edition"><?= old('description') ?></textarea>
                                    <div class="form-text">Optional field for additional information</div>
                                </div>

                                <!-- PDF File Upload -->
                                <div class="mb-3">
                                    <label for="pdf_file" class="form-label">
                                        <i class="fas fa-file-pdf me-2"></i>PDF File *
                                    </label>
                                    <input type="file" class="form-control" id="pdf_file" name="pdf_file" 
                                           accept=".pdf" required>
                                    <div class="form-text">
                                        <strong>Requirements:</strong> PDF format only, Maximum size: 50MB
                                    </div>
                                </div>

                                <!-- Thumbnail Upload -->
                                <div class="mb-3">
                                    <label for="thumbnail" class="form-label">
                                        <i class="fas fa-image me-2"></i>Thumbnail Image
                                    </label>
                                    <input type="file" class="form-control" id="thumbnail" name="thumbnail" 
                                           accept="image/*">
                                    <div class="form-text">
                                        Optional: Upload a thumbnail image for the epaper (JPG, PNG, GIF)
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- Publication Date -->
                                <div class="mb-3">
                                    <label for="publication_date" class="form-label">
                                        <i class="fas fa-calendar-alt me-2"></i>Publication Date *
                                    </label>
                                    <input type="date" class="form-control" id="publication_date" name="publication_date" 
                                           value="<?= old('publication_date', date('Y-m-d')) ?>" required>
                                </div>

                                <!-- Edition -->
                                <div class="mb-3">
                                    <label for="edition" class="form-label">
                                        <i class="fas fa-bookmark me-2"></i>Edition *
                                    </label>
                                    <input type="text" class="form-control" id="edition" name="edition" 
                                           value="<?= old('edition', 'Main Edition') ?>" required maxlength="100"
                                           placeholder="e.g., Main Edition, Evening Edition">
                                    <div class="form-text">Maximum 100 characters</div>
                                </div>

                                <!-- Language -->
                                <div class="mb-3">
                                    <label for="language" class="form-label">
                                        <i class="fas fa-language me-2"></i>Language *
                                    </label>
                                    <select class="form-select" id="language" name="language" required>
                                        <option value="">Select Language</option>
                                        <option value="Hindi" <?= old('language') == 'Hindi' ? 'selected' : '' ?>>Hindi</option>
                                        <option value="English" <?= old('language') == 'English' ? 'selected' : '' ?>>English</option>
                                        <option value="Bilingual" <?= old('language') == 'Bilingual' ? 'selected' : '' ?>>Bilingual</option>
                                    </select>
                                </div>

                                <!-- Page Count -->
                                <div class="mb-3">
                                    <label for="page_count" class="form-label">
                                        <i class="fas fa-file-alt me-2"></i>Page Count
                                    </label>
                                    <input type="number" class="form-control" id="page_count" name="page_count" 
                                           value="<?= old('page_count') ?>" min="1" max="999"
                                           placeholder="Number of pages">
                                    <div class="form-text">Optional: Total number of pages in the PDF</div>
                                </div>

                                <!-- Status -->
                                <div class="mb-3">
                                    <label for="status" class="form-label">
                                        <i class="fas fa-toggle-on me-2"></i>Status *
                                    </label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="">Select Status</option>
                                        <option value="active" <?= old('status') == 'active' ? 'selected' : '' ?>>Active</option>
                                        <option value="inactive" <?= old('status') == 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                        <option value="archived" <?= old('status') == 'archived' ? 'selected' : '' ?>>Archived</option>
                                    </select>
                                </div>

                                <!-- Featured -->
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="featured" name="featured" value="1"
                                               <?= old('featured') ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="featured">
                                            <i class="fas fa-star me-2"></i>Mark as Featured
                                        </label>
                                    </div>
                                    <div class="form-text">Featured epapers will be highlighted on the website</div>
                                </div>

                                <!-- Upload Info -->
                                <div class="card bg-light">
                                    <div class="card-body p-3">
                                        <h6 class="card-title">
                                            <i class="fas fa-info-circle me-2"></i>Upload Guidelines
                                        </h6>
                                        <ul class="small mb-0">
                                            <li>PDF files only (max 50MB)</li>
                                            <li>Use clear, descriptive titles</li>
                                            <li>Include publication date</li>
                                            <li>Add thumbnail for better presentation</li>
                                            <li>Specify correct edition and language</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <a href="<?= base_url('admin/epapers') ?>" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-upload me-2"></i>Upload Epaper
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // File size validation
    const pdfInput = document.getElementById('pdf_file');
    const thumbnailInput = document.getElementById('thumbnail');
    
    pdfInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const maxSize = 50 * 1024 * 1024; // 50MB in bytes
            if (file.size > maxSize) {
                alert('PDF file size must be less than 50MB. Current size: ' + (file.size / (1024 * 1024)).toFixed(2) + 'MB');
                this.value = '';
                return;
            }
            
            if (file.type !== 'application/pdf') {
                alert('Please select a valid PDF file.');
                this.value = '';
                return;
            }
        }
    });
    
    thumbnailInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const maxSize = 5 * 1024 * 1024; // 5MB for images
            if (file.size > maxSize) {
                alert('Thumbnail image size must be less than 5MB. Current size: ' + (file.size / (1024 * 1024)).toFixed(2) + 'MB');
                this.value = '';
                return;
            }
            
            if (!file.type.startsWith('image/')) {
                alert('Please select a valid image file.');
                this.value = '';
                return;
            }
        }
    });
    
    // Auto-generate title based on publication date
    const publicationDate = document.getElementById('publication_date');
    const titleInput = document.getElementById('title');
    
    publicationDate.addEventListener('change', function() {
        if (this.value && !titleInput.value) {
            const date = new Date(this.value);
            const formattedDate = date.toLocaleDateString('en-GB', {
                day: 'numeric',
                month: 'long',
                year: 'numeric'
            });
            titleInput.value = `BBC News - ${formattedDate}`;
        }
    });
});
</script>
<?= $this->endSection() ?>
