<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">Advertisement Management</h4>
    <a href="<?= base_url('admin/ads/create') ?>" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Add New Advertisement
    </a>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-left-primary">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Ads
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= count($ads) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-ad fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card border-left-success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Active Ads
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= count(array_filter($ads, function($a) { return $a['status'] === 'active'; })) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card border-left-info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Total Clicks
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= number_format(array_sum(array_column($ads, 'clicks'))) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-mouse-pointer fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card border-left-warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Total Impressions
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= number_format(array_sum(array_column($ads, 'impressions'))) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-eye fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <?php if (empty($ads)): ?>
            <div class="text-center py-5">
                <i class="fas fa-ad fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Advertisements Found</h5>
                <p class="text-muted">Start by creating your first advertisement.</p>
                <a href="<?= base_url('admin/ads/create') ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Create First Advertisement
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Preview</th>
                            <th>Title</th>
                            <th>Type</th>
                            <th>Position</th>
                            <th>Status</th>
                            <th>Performance</th>
                            <th>Schedule</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($ads as $ad): ?>
                            <tr>
                                <td>
                                    <?php if ($ad['type'] === 'banner' && $ad['image']): ?>
                                        <img src="<?= base_url('writable/uploads/ads/' . $ad['image']) ?>" 
                                             alt="<?= esc($ad['title']) ?>" 
                                             class="img-thumbnail" style="width: 60px; height: 40px; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="bg-light d-flex align-items-center justify-content-center" 
                                             style="width: 60px; height: 40px; border-radius: 4px;">
                                            <i class="fas fa-<?= $ad['type'] === 'video' ? 'video' : 'file-text' ?> text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong><?= esc($ad['title']) ?></strong>
                                    <?php if ($ad['description']): ?>
                                        <br><small class="text-muted"><?= esc(substr($ad['description'], 0, 50)) ?>...</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    $typeClass = [
                                        'banner' => 'bg-primary',
                                        'text' => 'bg-info',
                                        'video' => 'bg-success'
                                    ];
                                    ?>
                                    <span class="badge <?= $typeClass[$ad['type']] ?? 'bg-secondary' ?>">
                                        <?= ucfirst($ad['type']) ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?= ucfirst(str_replace('_', ' ', $ad['position'])) ?></span>
                                </td>
                                <td>
                                    <?php if ($ad['status'] === 'active'): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small>
                                        <i class="fas fa-mouse-pointer me-1"></i><?= number_format($ad['clicks']) ?><br>
                                        <i class="fas fa-eye me-1"></i><?= number_format($ad['impressions']) ?>
                                        <?php if ($ad['impressions'] > 0): ?>
                                            <br><span class="text-muted">CTR: <?= round(($ad['clicks'] / $ad['impressions']) * 100, 2) ?>%</span>
                                        <?php endif; ?>
                                    </small>
                                </td>
                                <td>
                                    <?php if ($ad['start_date']): ?>
                                        <small>From: <?= date('M d, Y', strtotime($ad['start_date'])) ?></small><br>
                                    <?php endif; ?>
                                    <?php if ($ad['end_date']): ?>
                                        <small>To: <?= date('M d, Y', strtotime($ad['end_date'])) ?></small>
                                    <?php else: ?>
                                        <small class="text-muted">No end date</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= base_url('admin/ads/edit/' . $ad['id']) ?>" 
                                           class="btn btn-sm btn-outline-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="<?= base_url('admin/ads/delete/' . $ad['id']) ?>" 
                                           class="btn btn-sm btn-outline-danger" 
                                           onclick="return confirm('Are you sure you want to delete this advertisement?')" 
                                           title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<style>
    .border-left-primary {
        border-left: 4px solid #667eea !important;
    }
    .border-left-success {
        border-left: 4px solid #28a745 !important;
    }
    .border-left-info {
        border-left: 4px solid #17a2b8 !important;
    }
    .border-left-warning {
        border-left: 4px solid #ffc107 !important;
    }
</style>
<?= $this->endSection() ?>
