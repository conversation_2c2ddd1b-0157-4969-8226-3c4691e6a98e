<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class PollSeeder extends Seeder
{
    public function run()
    {
        // Create polls
        $pollsData = [
            [
                'title' => 'भारत की सबसे बड़ी समस्या क्या है?',
                'description' => 'आपके अनुसार वर्तमान में भारत की सबसे बड़ी समस्या कौन सी है?',
                'status' => 'active',
                'multiple_choice' => 0,
                'show_results' => 'after_vote',
                'start_date' => null,
                'end_date' => null,
                'total_votes' => 0,
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'title' => 'आपको कौन से समाचार विषय सबसे ज्यादा पसंद हैं?',
                'description' => 'हमारी वेबसाइट पर आप किस प्रकार के समाचार सबसे ज्यादा पढ़ना पसंद करते हैं?',
                'status' => 'active',
                'multiple_choice' => 1,
                'show_results' => 'after_vote',
                'start_date' => null,
                'end_date' => null,
                'total_votes' => 0,
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'title' => 'डिजिटल इंडिया मिशन कितना सफल है?',
                'description' => 'आपके अनुसार डिजिटल इंडिया मिशन की सफलता का स्तर क्या है?',
                'status' => 'active',
                'multiple_choice' => 0,
                'show_results' => 'always',
                'start_date' => null,
                'end_date' => date('Y-m-d H:i:s', strtotime('+30 days')),
                'total_votes' => 0,
                'created_by' => 2,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        foreach ($pollsData as $pollData) {
            $pollId = $this->db->table('polls')->insert($pollData);
            $pollId = $this->db->insertID();

            // Add options for each poll
            if ($pollId == 1) {
                // Options for "भारत की सबसे बड़ी समस्या क्या है?"
                $options = [
                    'बेरोजगारी',
                    'भ्रष्टाचार',
                    'गरीबी',
                    'शिक्षा की कमी',
                    'स्वास्थ्य सेवाओं की कमी'
                ];
            } elseif ($pollId == 2) {
                // Options for "आपको कौन से समाचार विषय सबसे ज्यादा पसंद हैं?"
                $options = [
                    'राजनीति',
                    'खेल',
                    'मनोरंजन',
                    'तकनीक',
                    'व्यापार',
                    'अंतर्राष्ट्रीय समाचार'
                ];
            } else {
                // Options for "डिजिटल इंडिया मिशन कितना सफल है?"
                $options = [
                    'बहुत सफल',
                    'सफल',
                    'औसत',
                    'असफल',
                    'बहुत असफल'
                ];
            }

            foreach ($options as $index => $optionText) {
                $optionData = [
                    'poll_id' => $pollId,
                    'option_text' => $optionText,
                    'vote_count' => rand(0, 50), // Random vote counts for demo
                    'sort_order' => $index + 1,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ];
                $this->db->table('poll_options')->insert($optionData);
            }

            // Update total votes for the poll
            $this->db->table('polls')->where('id', $pollId)->update(['total_votes' => count($options) * 25]); // Average votes
        }

        echo "Polls seeded successfully!\n";
    }
}
