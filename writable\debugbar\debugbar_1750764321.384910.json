{"url": "http://localhost/bbc_news/public/index.php/admin/epapers", "method": "GET", "isAJAX": false, "startTime": **********.295713, "totalTime": 61.1, "totalMemory": "7.195", "segmentDuration": 10, "segmentCount": 7, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.299084, "duration": 0.*****************}, {"name": "Required Before Filters", "component": "Timer", "start": **********.317313, "duration": 0.0022280216217041016}, {"name": "Routing", "component": "Timer", "start": **********.319546, "duration": 0.0008349418640136719}, {"name": "Before Filters", "component": "Timer", "start": **********.320588, "duration": 0.005041837692260742}, {"name": "Controller", "component": "Timer", "start": **********.325634, "duration": 0.030688047409057617}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.325635, "duration": 0.009354114532470703}, {"name": "After Filters", "component": "Timer", "start": **********.356337, "duration": 1.0013580322265625e-05}, {"name": "Required After Filters", "component": "Timer", "start": **********.356382, "duration": 0.00047206878662109375}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(1 total Query, 1  unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "11.15 ms", "sql": "<strong>SELECT</strong> `epapers`.*, `users`.`full_name` as `uploaded_by_name`\n<strong>FROM</strong> `epapers`\n<strong>JOIN</strong> `users` <strong>ON</strong> `users`.`id` = `epapers`.`uploaded_by`\n<strong>ORDER</strong> <strong>BY</strong> `epapers`.`publication_date` <strong>DESC</strong>, `epapers`.`created_at` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\Epaper.php:85", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Admin\\Epapers.php:21", "function": "        App\\Models\\Epaper->getEpapersWithDetails()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Admin\\Epapers->index()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\Epaper.php:85", "qid": "d34a9f2cd83d50d26e1cff644df2634d"}]}, "badgeValue": 1, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.338314, "duration": "0.001356"}, {"name": "Query", "component": "Database", "start": **********.340189, "duration": "0.011145", "query": "<strong>SELECT</strong> `epapers`.*, `users`.`full_name` as `uploaded_by_name`\n<strong>FROM</strong> `epapers`\n<strong>JOIN</strong> `users` <strong>ON</strong> `users`.`id` = `epapers`.`uploaded_by`\n<strong>ORDER</strong> <strong>BY</strong> `epapers`.`publication_date` <strong>DESC</strong>, `epapers`.`created_at` <strong>DESC</strong>"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "debug", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 2, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: admin/layout.php", "component": "Views", "start": **********.355099, "duration": 0.0009369850158691406}, {"name": "View: admin/epapers/index.php", "component": "Views", "start": **********.35413, "duration": 0.0021109580993652344}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 164 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH\\Database\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH\\Database\\Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\I18n\\Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Session\\Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH\\Session\\SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH\\Validation\\FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Session.php", "name": "Session.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\Admin\\Epapers.php", "name": "Epapers.php"}, {"path": "APPPATH\\Controllers\\BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH\\Filters\\AuthFilter.php", "name": "AuthFilter.php"}, {"path": "APPPATH\\Helpers\\common_helper.php", "name": "common_helper.php"}, {"path": "APPPATH\\Helpers\\hindi_helper.php", "name": "hindi_helper.php"}, {"path": "APPPATH\\Models\\Epaper.php", "name": "Epaper.php"}, {"path": "APPPATH\\Views\\admin\\epapers\\index.php", "name": "index.php"}, {"path": "APPPATH\\Views\\admin\\layout.php", "name": "layout.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}, {"path": "VENDORPATH\\autoload.php", "name": "autoload.php"}, {"path": "VENDORPATH\\composer\\ClassLoader.php", "name": "ClassLoader.php"}, {"path": "VENDORPATH\\composer\\InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "VENDORPATH\\composer\\autoload_real.php", "name": "autoload_real.php"}, {"path": "VENDORPATH\\composer\\autoload_static.php", "name": "autoload_static.php"}, {"path": "VENDORPATH\\composer\\installed.php", "name": "installed.php"}, {"path": "VENDORPATH\\composer\\platform_check.php", "name": "platform_check.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\Escaper.php", "name": "Escaper.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "VENDORPATH\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php", "name": "deep_copy.php"}, {"path": "VENDORPATH\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php", "name": "Functions.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LogLevel.php", "name": "LogLevel.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "VENDORPATH\\symfony\\deprecation-contracts\\function.php", "name": "function.php"}]}, "badgeValue": 164, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Admin\\Epapers", "method": "index", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "news/(.*)", "handler": "\\App\\Controllers\\Home::news/$1"}, {"method": "GET", "route": "category/(.*)", "handler": "\\App\\Controllers\\Home::category/$1"}, {"method": "GET", "route": "poll/results/([0-9]+)", "handler": "\\App\\Controllers\\PollController::getResults/$1"}, {"method": "GET", "route": "epaper", "handler": "\\App\\Controllers\\EpaperController::index"}, {"method": "GET", "route": "epaper/view/([0-9]+)", "handler": "\\App\\Controllers\\EpaperController::view/$1"}, {"method": "GET", "route": "epaper/download/([0-9]+)", "handler": "\\App\\Controllers\\EpaperController::download/$1"}, {"method": "GET", "route": "epaper/archive", "handler": "\\App\\Controllers\\EpaperController::archive"}, {"method": "GET", "route": "epaper/archive/([0-9]+)", "handler": "\\App\\Controllers\\EpaperController::archive/$1"}, {"method": "GET", "route": "epaper/archive/([0-9]+)/([0-9]+)", "handler": "\\App\\Controllers\\EpaperController::archive/$1/$2"}, {"method": "GET", "route": "epaper/search", "handler": "\\App\\Controllers\\EpaperController::search"}, {"method": "GET", "route": "admin/login", "handler": "\\App\\Controllers\\Auth::login"}, {"method": "GET", "route": "admin/logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "admin/dashboard", "handler": "\\App\\Controllers\\Admin\\Dashboard::index"}, {"method": "GET", "route": "admin/users", "handler": "\\App\\Controllers\\Admin\\Users::index"}, {"method": "GET", "route": "admin/users/create", "handler": "\\App\\Controllers\\Admin\\Users::create"}, {"method": "GET", "route": "admin/users/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Users::edit/$1"}, {"method": "GET", "route": "admin/users/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Users::delete/$1"}, {"method": "GET", "route": "admin/news", "handler": "\\App\\Controllers\\Admin\\News::index"}, {"method": "GET", "route": "admin/news/create", "handler": "\\App\\Controllers\\Admin\\News::create"}, {"method": "GET", "route": "admin/news/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\News::edit/$1"}, {"method": "GET", "route": "admin/news/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\News::delete/$1"}, {"method": "GET", "route": "admin/categories", "handler": "\\App\\Controllers\\Admin\\Categories::index"}, {"method": "GET", "route": "admin/categories/create", "handler": "\\App\\Controllers\\Admin\\Categories::create"}, {"method": "GET", "route": "admin/categories/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Categories::edit/$1"}, {"method": "GET", "route": "admin/categories/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Categories::delete/$1"}, {"method": "GET", "route": "admin/tags", "handler": "\\App\\Controllers\\Admin\\Tags::index"}, {"method": "GET", "route": "admin/tags/create", "handler": "\\App\\Controllers\\Admin\\Tags::create"}, {"method": "GET", "route": "admin/tags/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Tags::edit/$1"}, {"method": "GET", "route": "admin/tags/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Tags::delete/$1"}, {"method": "GET", "route": "admin/ads", "handler": "\\App\\Controllers\\Admin\\Ads::index"}, {"method": "GET", "route": "admin/ads/analytics", "handler": "\\App\\Controllers\\Admin\\Ads::analytics"}, {"method": "GET", "route": "admin/ads/create", "handler": "\\App\\Controllers\\Admin\\Ads::create"}, {"method": "GET", "route": "admin/ads/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Ads::edit/$1"}, {"method": "GET", "route": "admin/ads/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Ads::delete/$1"}, {"method": "GET", "route": "admin/breaking-news", "handler": "\\App\\Controllers\\Admin\\BreakingNews::index"}, {"method": "GET", "route": "admin/breaking-news/create", "handler": "\\App\\Controllers\\Admin\\BreakingNews::create"}, {"method": "GET", "route": "admin/breaking-news/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BreakingNews::edit/$1"}, {"method": "GET", "route": "admin/breaking-news/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BreakingNews::delete/$1"}, {"method": "GET", "route": "admin/polls", "handler": "\\App\\Controllers\\Admin\\Polls::index"}, {"method": "GET", "route": "admin/polls/analytics", "handler": "\\App\\Controllers\\Admin\\Polls::analytics"}, {"method": "GET", "route": "admin/polls/results/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Polls::results/$1"}, {"method": "GET", "route": "admin/polls/create", "handler": "\\App\\Controllers\\Admin\\Polls::create"}, {"method": "GET", "route": "admin/polls/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Polls::edit/$1"}, {"method": "GET", "route": "admin/polls/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Polls::delete/$1"}, {"method": "GET", "route": "admin/epapers", "handler": "\\App\\Controllers\\Admin\\Epapers::index"}, {"method": "GET", "route": "admin/epapers/analytics", "handler": "\\App\\Controllers\\Admin\\Epapers::analytics"}, {"method": "GET", "route": "admin/epapers/create", "handler": "\\App\\Controllers\\Admin\\Epapers::create"}, {"method": "GET", "route": "admin/epapers/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Epapers::edit/$1"}, {"method": "GET", "route": "admin/epapers/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Epapers::delete/$1"}, {"method": "POST", "route": "poll/vote", "handler": "\\App\\Controllers\\PollController::vote"}, {"method": "POST", "route": "api/track-ad-click/([0-9]+)", "handler": "\\App\\Controllers\\Api\\AdTracking::trackClick/$1"}, {"method": "POST", "route": "api/track-ad-impression/([0-9]+)", "handler": "\\App\\Controllers\\Api\\AdTracking::trackImpression/$1"}, {"method": "POST", "route": "admin/authenticate", "handler": "\\App\\Controllers\\Auth::authenticate"}, {"method": "POST", "route": "admin/users/store", "handler": "\\App\\Controllers\\Admin\\Users::store"}, {"method": "POST", "route": "admin/users/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Users::update/$1"}, {"method": "POST", "route": "admin/news/store", "handler": "\\App\\Controllers\\Admin\\News::store"}, {"method": "POST", "route": "admin/news/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\News::update/$1"}, {"method": "POST", "route": "admin/categories/store", "handler": "\\App\\Controllers\\Admin\\Categories::store"}, {"method": "POST", "route": "admin/categories/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Categories::update/$1"}, {"method": "POST", "route": "admin/tags/store", "handler": "\\App\\Controllers\\Admin\\Tags::store"}, {"method": "POST", "route": "admin/tags/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Tags::update/$1"}, {"method": "POST", "route": "admin/ads/store", "handler": "\\App\\Controllers\\Admin\\Ads::store"}, {"method": "POST", "route": "admin/ads/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Ads::update/$1"}, {"method": "POST", "route": "admin/breaking-news/store", "handler": "\\App\\Controllers\\Admin\\BreakingNews::store"}, {"method": "POST", "route": "admin/breaking-news/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BreakingNews::update/$1"}, {"method": "POST", "route": "admin/breaking-news/toggle-status/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BreakingNews::toggleStatus/$1"}, {"method": "POST", "route": "admin/polls/store", "handler": "\\App\\Controllers\\Admin\\Polls::store"}, {"method": "POST", "route": "admin/polls/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Polls::update/$1"}, {"method": "POST", "route": "admin/epapers/store", "handler": "\\App\\Controllers\\Admin\\Epapers::store"}, {"method": "POST", "route": "admin/epapers/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Epapers::update/$1"}]}, "badgeValue": 51, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "8.23", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "0.03", "count": 1}}}, "badgeValue": 2, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.309075, "duration": 0.008231878280639648}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.351341, "duration": 2.9087066650390625e-05}]}], "vars": {"varData": {"View Data": {"title": "Epaper Management", "epapers": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (5)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (5)</li><li>Contents (5)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>title</th><th>description</th><th>publication_date</th><th>edition</th><th>language</th><th>pdf_file</th><th>thumbnail</th><th>file_size</th><th>page_count</th><th>download_count</th><th>view_count</th><th>status</th><th>featured</th><th>uploaded_by</th><th>created_at</th><th>updated_at</th><th>uploaded_by_name</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">1</td><td title=\"UTF-8 string (28)\">BBC News - 24 &#2332;&#2370;&#2344; 2025</td><td title=\"UTF-8 string (191)\">&#2310;&#2332; &#2325;&#2375; &#2350;&#2369;&#2326;&#2381;&#2351; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352;: &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2324;&#2352; &#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2328;&#2335;&#2344;&#2366;&#2323;&#2306; &#2325;&#2368; &#2357;&#2367;&#2360;&#2381;&#2340;&#2371;&#2340; &#2325;&#2357;&#2352;&#2375;&#2332;</td><td title=\"string (10)\">2025-06-24</td><td title=\"string (12)\">Main Edition</td><td title=\"string (5)\">Hindi</td><td title=\"string (57)\">2025-06-24_1750764312_1750764312_7d54ac4cbf5c9f6cc396.pdf</td><td title=\"string (52)\">thumb_1750764312_1750764312_ff7b2262bbfbb6394754.png</td><td title=\"string (8)\">15080402</td><td title=\"string (2)\">16</td><td title=\"string (3)\">245</td><td title=\"string (4)\">1250</td><td title=\"string (6)\">active</td><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"string (19)\">2025-06-24 11:01:22</td><td title=\"string (19)\">2025-06-24 11:25:12</td><td title=\"string (13)\">Administrator</td></tr><tr><th>1</th><td title=\"string (1)\">2</td><td title=\"UTF-8 string (28)\">BBC News - 23 &#2332;&#2370;&#2344; 2025</td><td title=\"UTF-8 string (83)\">&#2325;&#2354; &#2325;&#2375; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2357;&#2367;&#2358;&#2381;&#2354;&#2375;&#2359;&#2339;</td><td title=\"string (10)\">2025-06-23</td><td title=\"string (12)\">Main Edition</td><td title=\"string (5)\">Hindi</td><td title=\"string (19)\">sample_epaper_2.pdf</td><td title=\"string (18)\">thumb_epaper_2.jpg</td><td title=\"string (8)\">18874368</td><td title=\"string (2)\">20</td><td title=\"string (3)\">189</td><td title=\"string (3)\">890</td><td title=\"string (6)\">active</td><td title=\"string (1)\">0</td><td title=\"string (1)\">1</td><td title=\"string (19)\">2025-06-23 11:01:22</td><td title=\"string (19)\">2025-06-23 11:01:22</td><td title=\"string (13)\">Administrator</td></tr><tr><th>2</th><td title=\"string (1)\">3</td><td title=\"UTF-8 string (28)\">BBC News - 22 &#2332;&#2370;&#2344; 2025</td><td title=\"UTF-8 string (65)\">&#2360;&#2346;&#2381;&#2340;&#2366;&#2361;&#2366;&#2306;&#2340; &#2357;&#2367;&#2358;&#2375;&#2359; &#2360;&#2306;&#2360;&#2381;&#2325;&#2352;&#2339;</td><td title=\"string (10)\">2025-06-22</td><td title=\"string (15)\">Weekend Edition</td><td title=\"string (5)\">Hindi</td><td title=\"string (19)\">sample_epaper_3.pdf</td><td title=\"string (18)\">thumb_epaper_3.jpg</td><td title=\"string (8)\">22020096</td><td title=\"string (2)\">24</td><td title=\"string (3)\">156</td><td title=\"string (3)\">678</td><td title=\"string (6)\">active</td><td title=\"string (1)\">0</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-06-22 11:01:22</td><td title=\"string (19)\">2025-06-22 11:01:22</td><td title=\"string (12)\">News Manager</td></tr><tr><th>3</th><td title=\"string (1)\">4</td><td title=\"UTF-8 string (28)\">BBC News - 21 &#2332;&#2370;&#2344; 2025</td><td title=\"UTF-8 string (66)\">&#2358;&#2369;&#2325;&#2381;&#2352;&#2357;&#2366;&#2352; &#2325;&#2375; &#2350;&#2369;&#2326;&#2381;&#2351; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352;</td><td title=\"string (10)\">2025-06-21</td><td title=\"string (12)\">Main Edition</td><td title=\"string (5)\">Hindi</td><td title=\"string (19)\">sample_epaper_4.pdf</td><td title=\"null\"><var>null</var></td><td title=\"string (8)\">14680064</td><td title=\"string (2)\">14</td><td title=\"string (3)\">134</td><td title=\"string (3)\">567</td><td title=\"string (6)\">active</td><td title=\"string (1)\">0</td><td title=\"string (1)\">1</td><td title=\"string (19)\">2025-06-21 11:01:22</td><td title=\"string (19)\">2025-06-21 11:01:22</td><td title=\"string (13)\">Administrator</td></tr><tr><th>4</th><td title=\"string (1)\">5</td><td title=\"UTF-8 string (28)\">BBC News - 20 &#2332;&#2370;&#2344; 2025</td><td title=\"UTF-8 string (111)\">&#2327;&#2369;&#2352;&#2369;&#2357;&#2366;&#2352; &#2325;&#2375; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2357;&#2367;&#2358;&#2375;&#2359; &#2352;&#2367;&#2346;&#2379;&#2352;&#2381;&#2335;</td><td title=\"string (10)\">2025-06-20</td><td title=\"string (12)\">Main Edition</td><td title=\"string (5)\">Hindi</td><td title=\"string (19)\">sample_epaper_5.pdf</td><td title=\"string (18)\">thumb_epaper_5.jpg</td><td title=\"string (8)\">16777216</td><td title=\"string (2)\">18</td><td title=\"string (2)\">98</td><td title=\"string (3)\">445</td><td title=\"string (8)\">archived</td><td title=\"string (1)\">0</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-06-20 11:01:22</td><td title=\"string (19)\">2025-06-20 11:01:22</td><td title=\"string (12)\">News Manager</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (18)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (28) \"BBC News - 24 &#2332;&#2370;&#2344; 2025\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (191) \"&#2310;&#2332; &#2325;&#2375; &#2350;&#2369;&#2326;&#2381;&#2351; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352;: &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2324;&#2352; &#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2328;&#2335;&#2344;&#2366;&#2323;&#2306; &#2325;&#2368; &#2357;&#2367;&#2360;&#2381;&#2340;&#2371;&#2340; &#2325;&#2357;&#2352;&#2375;&#2332;\"<div class=\"access-path\">$value[0]['description']</div></dt><dd><pre>&#2310;&#2332; &#2325;&#2375; &#2350;&#2369;&#2326;&#2381;&#2351; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352;: &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2324;&#2352; &#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2328;&#2335;&#2344;&#2366;&#2323;&#2306; &#2325;&#2368; &#2357;&#2367;&#2360;&#2381;&#2340;&#2371;&#2340; &#2325;&#2357;&#2352;&#2375;&#2332;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>publication_date</dfn> =&gt; <var>string</var> (10) \"2025-06-24\"<div class=\"access-path\">$value[0]['publication_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>edition</dfn> =&gt; <var>string</var> (12) \"Main Edition\"<div class=\"access-path\">$value[0]['edition']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>language</dfn> =&gt; <var>string</var> (5) \"Hindi\"<div class=\"access-path\">$value[0]['language']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>pdf_file</dfn> =&gt; <var>string</var> (57) \"2025-06-24_1750764312_1750764312_7d54ac4cbf5c9f6cc396.pdf\"<div class=\"access-path\">$value[0]['pdf_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>thumbnail</dfn> =&gt; <var>string</var> (52) \"thumb_1750764312_1750764312_ff7b2262bbfbb6394754.png\"<div class=\"access-path\">$value[0]['thumbnail']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>file_size</dfn> =&gt; <var>string</var> (8) \"15080402\"<div class=\"access-path\">$value[0]['file_size']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_count</dfn> =&gt; <var>string</var> (2) \"16\"<div class=\"access-path\">$value[0]['page_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>download_count</dfn> =&gt; <var>string</var> (3) \"245\"<div class=\"access-path\">$value[0]['download_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>view_count</dfn> =&gt; <var>string</var> (4) \"1250\"<div class=\"access-path\">$value[0]['view_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>uploaded_by</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['uploaded_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 11:01:22\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 11:25:12\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>uploaded_by_name</dfn> =&gt; <var>string</var> (13) \"Administrator\"<div class=\"access-path\">$value[0]['uploaded_by_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (18)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (28) \"BBC News - 23 &#2332;&#2370;&#2344; 2025\"<div class=\"access-path\">$value[1]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (83) \"&#2325;&#2354; &#2325;&#2375; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2357;&#2367;&#2358;&#2381;&#2354;&#2375;&#2359;&#2339;\"<div class=\"access-path\">$value[1]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>publication_date</dfn> =&gt; <var>string</var> (10) \"2025-06-23\"<div class=\"access-path\">$value[1]['publication_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>edition</dfn> =&gt; <var>string</var> (12) \"Main Edition\"<div class=\"access-path\">$value[1]['edition']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>language</dfn> =&gt; <var>string</var> (5) \"Hindi\"<div class=\"access-path\">$value[1]['language']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>pdf_file</dfn> =&gt; <var>string</var> (19) \"sample_epaper_2.pdf\"<div class=\"access-path\">$value[1]['pdf_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>thumbnail</dfn> =&gt; <var>string</var> (18) \"thumb_epaper_2.jpg\"<div class=\"access-path\">$value[1]['thumbnail']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>file_size</dfn> =&gt; <var>string</var> (8) \"18874368\"<div class=\"access-path\">$value[1]['file_size']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_count</dfn> =&gt; <var>string</var> (2) \"20\"<div class=\"access-path\">$value[1]['page_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>download_count</dfn> =&gt; <var>string</var> (3) \"189\"<div class=\"access-path\">$value[1]['download_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>view_count</dfn> =&gt; <var>string</var> (3) \"890\"<div class=\"access-path\">$value[1]['view_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>featured</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[1]['featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>uploaded_by</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['uploaded_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-23 11:01:22\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-23 11:01:22\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>uploaded_by_name</dfn> =&gt; <var>string</var> (13) \"Administrator\"<div class=\"access-path\">$value[1]['uploaded_by_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (18)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (28) \"BBC News - 22 &#2332;&#2370;&#2344; 2025\"<div class=\"access-path\">$value[2]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (65) \"&#2360;&#2346;&#2381;&#2340;&#2366;&#2361;&#2366;&#2306;&#2340; &#2357;&#2367;&#2358;&#2375;&#2359; &#2360;&#2306;&#2360;&#2381;&#2325;&#2352;&#2339;\"<div class=\"access-path\">$value[2]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>publication_date</dfn> =&gt; <var>string</var> (10) \"2025-06-22\"<div class=\"access-path\">$value[2]['publication_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>edition</dfn> =&gt; <var>string</var> (15) \"Weekend Edition\"<div class=\"access-path\">$value[2]['edition']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>language</dfn> =&gt; <var>string</var> (5) \"Hindi\"<div class=\"access-path\">$value[2]['language']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>pdf_file</dfn> =&gt; <var>string</var> (19) \"sample_epaper_3.pdf\"<div class=\"access-path\">$value[2]['pdf_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>thumbnail</dfn> =&gt; <var>string</var> (18) \"thumb_epaper_3.jpg\"<div class=\"access-path\">$value[2]['thumbnail']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>file_size</dfn> =&gt; <var>string</var> (8) \"22020096\"<div class=\"access-path\">$value[2]['file_size']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_count</dfn> =&gt; <var>string</var> (2) \"24\"<div class=\"access-path\">$value[2]['page_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>download_count</dfn> =&gt; <var>string</var> (3) \"156\"<div class=\"access-path\">$value[2]['download_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>view_count</dfn> =&gt; <var>string</var> (3) \"678\"<div class=\"access-path\">$value[2]['view_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>featured</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[2]['featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>uploaded_by</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[2]['uploaded_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-22 11:01:22\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-22 11:01:22\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>uploaded_by_name</dfn> =&gt; <var>string</var> (12) \"News Manager\"<div class=\"access-path\">$value[2]['uploaded_by_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (18)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (28) \"BBC News - 21 &#2332;&#2370;&#2344; 2025\"<div class=\"access-path\">$value[3]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (66) \"&#2358;&#2369;&#2325;&#2381;&#2352;&#2357;&#2366;&#2352; &#2325;&#2375; &#2350;&#2369;&#2326;&#2381;&#2351; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352;\"<div class=\"access-path\">$value[3]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>publication_date</dfn> =&gt; <var>string</var> (10) \"2025-06-21\"<div class=\"access-path\">$value[3]['publication_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>edition</dfn> =&gt; <var>string</var> (12) \"Main Edition\"<div class=\"access-path\">$value[3]['edition']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>language</dfn> =&gt; <var>string</var> (5) \"Hindi\"<div class=\"access-path\">$value[3]['language']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>pdf_file</dfn> =&gt; <var>string</var> (19) \"sample_epaper_4.pdf\"<div class=\"access-path\">$value[3]['pdf_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>thumbnail</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['thumbnail']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>file_size</dfn> =&gt; <var>string</var> (8) \"14680064\"<div class=\"access-path\">$value[3]['file_size']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_count</dfn> =&gt; <var>string</var> (2) \"14\"<div class=\"access-path\">$value[3]['page_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>download_count</dfn> =&gt; <var>string</var> (3) \"134\"<div class=\"access-path\">$value[3]['download_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>view_count</dfn> =&gt; <var>string</var> (3) \"567\"<div class=\"access-path\">$value[3]['view_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>featured</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[3]['featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>uploaded_by</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]['uploaded_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-21 11:01:22\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-21 11:01:22\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>uploaded_by_name</dfn> =&gt; <var>string</var> (13) \"Administrator\"<div class=\"access-path\">$value[3]['uploaded_by_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (18)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[4]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (28) \"BBC News - 20 &#2332;&#2370;&#2344; 2025\"<div class=\"access-path\">$value[4]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (111) \"&#2327;&#2369;&#2352;&#2369;&#2357;&#2366;&#2352; &#2325;&#2375; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2357;&#2367;&#2358;&#2375;&#2359; &#2352;&#2367;&#2346;&#2379;&#2352;&#2381;&#2335;\"<div class=\"access-path\">$value[4]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>publication_date</dfn> =&gt; <var>string</var> (10) \"2025-06-20\"<div class=\"access-path\">$value[4]['publication_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>edition</dfn> =&gt; <var>string</var> (12) \"Main Edition\"<div class=\"access-path\">$value[4]['edition']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>language</dfn> =&gt; <var>string</var> (5) \"Hindi\"<div class=\"access-path\">$value[4]['language']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>pdf_file</dfn> =&gt; <var>string</var> (19) \"sample_epaper_5.pdf\"<div class=\"access-path\">$value[4]['pdf_file']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>thumbnail</dfn> =&gt; <var>string</var> (18) \"thumb_epaper_5.jpg\"<div class=\"access-path\">$value[4]['thumbnail']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>file_size</dfn> =&gt; <var>string</var> (8) \"16777216\"<div class=\"access-path\">$value[4]['file_size']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_count</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[4]['page_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>download_count</dfn> =&gt; <var>string</var> (2) \"98\"<div class=\"access-path\">$value[4]['download_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>view_count</dfn> =&gt; <var>string</var> (3) \"445\"<div class=\"access-path\">$value[4]['view_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (8) \"archived\"<div class=\"access-path\">$value[4]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>featured</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[4]['featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>uploaded_by</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[4]['uploaded_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 11:01:22\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 11:01:22\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>uploaded_by_name</dfn> =&gt; <var>string</var> (12) \"News Manager\"<div class=\"access-path\">$value[4]['uploaded_by_name']</div></dt></dl></dd></dl></li></ul></dd></dl></div>"}}, "session": {"__ci_last_regenerate": "<pre>1750764075</pre>", "_ci_previous_url": "http://localhost/bbc_news/public/index.php/admin/epapers", "user_id": "1", "username": "admin", "email": "<EMAIL>", "full_name": "Administrator", "role": "admin", "isLoggedIn": "<pre>1</pre>", "__ci_vars": "<pre>Array\n(\n)\n</pre>"}, "headers": {"Host": "localhost", "Connection": "keep-alive", "Sec-Ch-Ua": "&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Referer": "http://localhost/bbc_news/public/admin/epapers/analytics", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "en-US,en;q=0.9,eu;q=0.8,hi;q=0.7,bho;q=0.6,ne;q=0.5,nl;q=0.4,eo;q=0.3,haw;q=0.2", "Cookie": "csrf_cookie_name=8dd1fe241d9faddc254f1a605fda76b6; ci_session=f9a10b589ac364b61bc7ce012645ce04"}, "cookies": {"csrf_cookie_name": "8dd1fe241d9faddc254f1a605fda76b6", "ci_session": "f9a10b589ac364b61bc7ce012645ce04"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.12", "phpSAPI": "apache2handler", "environment": "development", "baseURL": "http://localhost/bbc_news/public/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}