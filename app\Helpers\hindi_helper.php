<?php

/**
 * Hindi Language Helper Functions
 * Provides utility functions for Hindi language support
 */

if (!function_exists('hindi_date')) {
    /**
     * Convert English date to Hindi format
     */
    function hindi_date($date, $format = 'd M Y')
    {
        $englishMonths = [
            'Jan' => 'जन', 'Feb' => 'फर', 'Mar' => 'मार', 'Apr' => 'अप्र',
            'May' => 'मई', 'Jun' => 'जून', 'Jul' => 'जुल', 'Aug' => 'अग',
            'Sep' => 'सित', 'Oct' => 'अक्ट', 'Nov' => 'नव', 'Dec' => 'दिस'
        ];
        
        $englishDays = [
            'Monday' => 'सोमवार', 'Tuesday' => 'मंगलवार', 'Wednesday' => 'बुधवार',
            'Thursday' => 'गुरुवार', 'Friday' => 'शुक्रवार', 'Saturday' => 'शनिवार', 'Sunday' => 'रविवार'
        ];
        
        $formattedDate = date($format, strtotime($date));
        
        foreach ($englishMonths as $eng => $hindi) {
            $formattedDate = str_replace($eng, $hindi, $formattedDate);
        }
        
        foreach ($englishDays as $eng => $hindi) {
            $formattedDate = str_replace($eng, $hindi, $formattedDate);
        }
        
        return $formattedDate;
    }
}

if (!function_exists('hindi_numbers')) {
    /**
     * Convert English numbers to Hindi (Devanagari) numbers
     */
    function hindi_numbers($number)
    {
        $englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        $hindiNumbers = ['०', '१', '२', '३', '४', '५', '६', '७', '८', '९'];
        
        return str_replace($englishNumbers, $hindiNumbers, $number);
    }
}

if (!function_exists('format_hindi_views')) {
    /**
     * Format view count in Hindi
     */
    function format_hindi_views($views)
    {
        if ($views >= 10000000) {
            return round($views / 10000000, 1) . ' करोड़';
        } elseif ($views >= 100000) {
            return round($views / 100000, 1) . ' लाख';
        } elseif ($views >= 1000) {
            return round($views / 1000, 1) . ' हजार';
        }
        
        return $views;
    }
}

if (!function_exists('transliterate_to_english')) {
    /**
     * Simple transliteration from Hindi to English for URLs
     */
    function transliterate_to_english($hindi)
    {
        $transliterations = [
            // Common Hindi words to English
            'राजनीति' => 'politics',
            'खेल' => 'sports',
            'मनोरंजन' => 'entertainment',
            'तकनीक' => 'technology',
            'व्यापार' => 'business',
            'स्वास्थ्य' => 'health',
            'शिक्षा' => 'education',
            'विज्ञान' => 'science',
            'समाचार' => 'news',
            'ब्रेकिंग न्यूज़' => 'breaking-news',
            'ट्रेंडिंग' => 'trending',
            'महत्वपूर्ण' => 'important',
            'लाइव अपडेट' => 'live-update',
            'विशेष रिपोर्ट' => 'special-report',
            'इंटरव्यू' => 'interview',
            'दिल्ली' => 'delhi',
            'मुंबई' => 'mumbai',
            'राष्ट्रीय' => 'national',
            'अंतर्राष्ट्रीय' => 'international',
            
            // Common characters
            'क' => 'ka', 'ख' => 'kha', 'ग' => 'ga', 'घ' => 'gha',
            'च' => 'cha', 'छ' => 'chha', 'ज' => 'ja', 'झ' => 'jha',
            'ट' => 'ta', 'ठ' => 'tha', 'ड' => 'da', 'ढ' => 'dha',
            'त' => 'ta', 'थ' => 'tha', 'द' => 'da', 'ध' => 'dha',
            'न' => 'na', 'प' => 'pa', 'फ' => 'pha', 'ब' => 'ba',
            'भ' => 'bha', 'म' => 'ma', 'य' => 'ya', 'र' => 'ra',
            'ल' => 'la', 'व' => 'va', 'श' => 'sha', 'ष' => 'sha',
            'स' => 'sa', 'ह' => 'ha',
            
            // Vowels
            'अ' => 'a', 'आ' => 'aa', 'इ' => 'i', 'ई' => 'ee',
            'उ' => 'u', 'ऊ' => 'oo', 'ए' => 'e', 'ऐ' => 'ai',
            'ओ' => 'o', 'औ' => 'au',
        ];
        
        // First try exact matches
        if (isset($transliterations[$hindi])) {
            return $transliterations[$hindi];
        }
        
        // Then try character by character
        $result = $hindi;
        foreach ($transliterations as $hindiChar => $englishChar) {
            $result = str_replace($hindiChar, $englishChar, $result);
        }
        
        // Clean up and make URL friendly
        $result = strtolower($result);
        $result = preg_replace('/[^a-z0-9\s-]/', '', $result);
        $result = preg_replace('/\s+/', '-', $result);
        $result = trim($result, '-');
        
        return $result;
    }
}

if (!function_exists('get_hindi_time_ago')) {
    /**
     * Get time ago in Hindi
     */
    function get_hindi_time_ago($datetime)
    {
        $time = time() - strtotime($datetime);
        
        if ($time < 60) {
            return 'अभी अभी';
        } elseif ($time < 3600) {
            $minutes = floor($time / 60);
            return $minutes . ' मिनट पहले';
        } elseif ($time < 86400) {
            $hours = floor($time / 3600);
            return $hours . ' घंटे पहले';
        } elseif ($time < 2592000) {
            $days = floor($time / 86400);
            return $days . ' दिन पहले';
        } elseif ($time < 31536000) {
            $months = floor($time / 2592000);
            return $months . ' महीने पहले';
        } else {
            $years = floor($time / 31536000);
            return $years . ' साल पहले';
        }
    }
}

if (!function_exists('validate_hindi_text')) {
    /**
     * Validate if text contains Hindi characters
     */
    function validate_hindi_text($text)
    {
        // Check if text contains Devanagari characters
        return preg_match('/[\x{0900}-\x{097F}]/u', $text);
    }
}

if (!function_exists('clean_hindi_text')) {
    /**
     * Clean and sanitize Hindi text
     */
    function clean_hindi_text($text)
    {
        // Remove extra spaces and normalize
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);
        
        // Remove any non-Devanagari, non-ASCII characters except common punctuation
        $text = preg_replace('/[^\x{0900}-\x{097F}\x{0020}-\x{007E}\s]/u', '', $text);
        
        return $text;
    }
}

if (!function_exists('get_reading_time_hindi')) {
    /**
     * Calculate reading time for Hindi text
     */
    function get_reading_time_hindi($text)
    {
        // Average Hindi reading speed is about 200 words per minute
        $wordCount = str_word_count(strip_tags($text));
        $readingTime = ceil($wordCount / 200);
        
        if ($readingTime < 1) {
            return '1 मिनट';
        } elseif ($readingTime == 1) {
            return '1 मिनट';
        } else {
            return $readingTime . ' मिनट';
        }
    }
}
