<?php

namespace App\Models;

use CodeIgniter\Model;

class BreakingNews extends Model
{
    protected $table            = 'breaking_news';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'title',
        'content',
        'link_url',
        'priority',
        'status',
        'start_time',
        'end_time',
        'created_by'
    ];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules = [
        'title'      => 'required|min_length[5]|max_length[500]',
        'priority'   => 'required|in_list[1,2,3,4]',
        'status'     => 'required|in_list[active,inactive]',
        'created_by' => 'required|integer',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Get active breaking news for display
     */
    public function getActiveBreakingNews($limit = null)
    {
        $builder = $this->select('breaking_news.*, users.full_name as created_by_name')
            ->join('users', 'users.id = breaking_news.created_by')
            ->where('breaking_news.status', 'active')
            ->where('(breaking_news.start_time IS NULL OR breaking_news.start_time <= NOW())', null, false)
            ->where('(breaking_news.end_time IS NULL OR breaking_news.end_time >= NOW())', null, false)
            ->orderBy('breaking_news.priority', 'DESC')
            ->orderBy('breaking_news.created_at', 'DESC');

        if ($limit) {
            $builder->limit($limit);
        }

        return $builder->findAll();
    }

    /**
     * Get breaking news with details for admin
     */
    public function getBreakingNewsWithDetails($limit = null)
    {
        $builder = $this->select('breaking_news.*, users.full_name as created_by_name')
            ->join('users', 'users.id = breaking_news.created_by')
            ->orderBy('breaking_news.created_at', 'DESC');

        if ($limit) {
            $builder->limit($limit);
        }

        return $builder->findAll();
    }

    /**
     * Get priority label
     */
    public function getPriorityLabel($priority)
    {
        $labels = [
            1 => 'Low',
            2 => 'Medium',
            3 => 'High',
            4 => 'Critical'
        ];

        return $labels[$priority] ?? 'Unknown';
    }

    /**
     * Get priority badge class
     */
    public function getPriorityBadgeClass($priority)
    {
        $classes = [
            1 => 'bg-secondary',
            2 => 'bg-info',
            3 => 'bg-warning',
            4 => 'bg-danger'
        ];

        return $classes[$priority] ?? 'bg-secondary';
    }
}
