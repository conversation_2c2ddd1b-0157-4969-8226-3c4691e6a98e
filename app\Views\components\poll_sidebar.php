<?php if (!empty($polls)): ?>
    <?php foreach ($polls as $poll): ?>
        <div class="card mb-4 poll-widget" data-poll-id="<?= $poll['id'] ?>">
            <div class="card-header bg-primary text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-poll me-2"></i>राय सर्वेक्षण
                </h6>
            </div>
            <div class="card-body">
                <h6 class="poll-question"><?= esc($poll['title']) ?></h6>

                <?php if ($poll['description']): ?>
                    <p class="text-muted small mb-3"><?= esc($poll['description']) ?></p>
                <?php endif; ?>

                <form class="poll-form" data-poll-id="<?= $poll['id'] ?>" data-multiple="<?= $poll['multiple_choice'] ?>">
                    <?= csrf_field() ?>

                    <div class="poll-options">
                        <?php if (!empty($poll['options'])): ?>
                            <?php foreach ($poll['options'] as $option): ?>
                                <div class="form-check mb-2">
                                    <?php if ($poll['multiple_choice']): ?>
                                        <input class="form-check-input poll-option"
                                            type="checkbox"
                                            name="option_ids[]"
                                            value="<?= $option['id'] ?>"
                                            id="option_<?= $poll['id'] ?>_<?= $option['id'] ?>">
                                    <?php else: ?>
                                        <input class="form-check-input poll-option"
                                            type="radio"
                                            name="option_ids"
                                            value="<?= $option['id'] ?>"
                                            id="option_<?= $poll['id'] ?>_<?= $option['id'] ?>">
                                    <?php endif; ?>
                                    <label class="form-check-label" for="option_<?= $poll['id'] ?>_<?= $option['id'] ?>">
                                        <?= esc($option['option_text']) ?>
                                    </label>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="text-muted">
                                <small>No options available for this poll.</small>
                            </div>
                        <?php endif; ?>
                    </div>

                    <?php if (!empty($poll['options'])): ?>
                        <div class="poll-actions mt-3">
                            <button type="submit" class="btn btn-primary btn-sm w-100 vote-btn">
                                <i class="fas fa-vote-yea me-2"></i>वोट दें
                            </button>
                        </div>
                    <?php endif; ?>
                </form>

                <!-- Results Section (Initially Hidden) -->
                <div class="poll-results" style="display: none;">
                    <h6 class="mb-3">
                        <i class="fas fa-chart-bar me-2"></i>परिणाम
                        <span class="badge bg-secondary ms-2 total-votes">0 votes</span>
                    </h6>
                    <div class="results-container">
                        <!-- Results will be populated by JavaScript -->
                    </div>
                    <div class="text-center mt-3">
                        <small class="text-muted">धन्यवाद! आपका वोट दर्ज हो गया है।</small>
                    </div>
                </div>

                <!-- Already Voted Message -->
                <div class="poll-voted" style="display: none;">
                    <div class="alert alert-info mb-0">
                        <i class="fas fa-check-circle me-2"></i>
                        आपने इस सर्वेक्षण में पहले से वोट दिया है।
                        <?php if ($poll['show_results'] === 'always'): ?>
                            <button class="btn btn-link btn-sm p-0 ms-2 show-results-btn" data-poll-id="<?= $poll['id'] ?>">
                                परिणाम देखें
                            </button>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Poll Info -->
                <div class="poll-info mt-3 pt-2 border-top">
                    <div class="row text-center">
                        <div class="col-6">
                            <small class="text-muted">
                                <i class="fas fa-users me-1"></i>
                                <span class="total-votes-count"><?= number_format($poll['total_votes']) ?></span> वोट
                            </small>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                <?= date('d M', strtotime($poll['created_at'])) ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; ?>

    <!-- Poll Styles -->
    <style>
        .poll-widget {
            border: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
        }

        .poll-widget:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .poll-question {
            color: #333;
            font-weight: 600;
            line-height: 1.4;
        }

        .poll-option {
            cursor: pointer;
        }

        .form-check-label {
            cursor: pointer;
            font-size: 14px;
            line-height: 1.4;
        }

        .vote-btn {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .vote-btn:hover {
            background: linear-gradient(45deg, #0056b3, #004085);
            transform: translateY(-1px);
        }

        .vote-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .poll-results .progress {
            height: 20px;
            margin-bottom: 8px;
            border-radius: 10px;
            background-color: #f8f9fa;
        }

        .poll-results .progress-bar {
            border-radius: 10px;
            font-size: 12px;
            font-weight: 600;
            transition: width 0.6s ease;
        }

        .result-option {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }

        .result-label {
            font-size: 13px;
            font-weight: 500;
            margin-bottom: 2px;
        }

        .result-stats {
            font-size: 11px;
            color: #6c757d;
        }

        .poll-loading {
            text-align: center;
            padding: 20px;
        }

        .poll-loading .spinner-border {
            width: 1.5rem;
            height: 1.5rem;
        }

        @media (max-width: 768px) {
            .poll-widget {
                margin-bottom: 1rem;
            }

            .poll-question {
                font-size: 15px;
            }

            .form-check-label {
                font-size: 13px;
            }
        }
    </style>

    <!-- Poll JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user has already voted in polls
            checkVotedPolls();

            // Handle poll form submissions
            document.querySelectorAll('.poll-form').forEach(form => {
                form.addEventListener('submit', handlePollVote);
            });

            // Handle show results buttons
            document.querySelectorAll('.show-results-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const pollId = this.dataset.pollId;
                    showPollResults(pollId);
                });
            });
        });

        function checkVotedPolls() {
            const votedPolls = JSON.parse(localStorage.getItem('votedPolls') || '[]');

            votedPolls.forEach(pollId => {
                const pollWidget = document.querySelector(`[data-poll-id="${pollId}"]`);
                if (pollWidget) {
                    showVotedState(pollWidget);
                }
            });
        }

        function handlePollVote(e) {
            e.preventDefault();

            const form = e.target;
            const pollId = form.dataset.pollId;
            const isMultiple = form.dataset.multiple === '1';

            // Get selected options
            const selectedOptions = [];
            const options = form.querySelectorAll('.poll-option:checked');

            if (options.length === 0) {
                alert('कृपया एक विकल्प चुनें।');
                return;
            }

            options.forEach(option => {
                selectedOptions.push(option.value);
            });

            // Show loading state
            const voteBtn = form.querySelector('.vote-btn');
            const originalText = voteBtn.innerHTML;
            voteBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>वोट दे रहे हैं...';
            voteBtn.disabled = true;

            // Submit vote
            fetch('<?= base_url('poll/vote') ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: new URLSearchParams({
                        poll_id: pollId,
                        'option_ids[]': selectedOptions,
                        '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Mark as voted
                        markAsVoted(pollId);

                        // Show results if available
                        if (data.show_results && data.results) {
                            displayPollResults(pollId, data.results);
                        } else {
                            showVotedState(form.closest('.poll-widget'));
                        }

                        // Show success message
                        showNotification('success', data.message);
                    } else {
                        // Show error message
                        showNotification('error', data.message);

                        // Reset button
                        voteBtn.innerHTML = originalText;
                        voteBtn.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('error', 'वोट देने में त्रुटि हुई। कृपया पुनः प्रयास करें।');

                    // Reset button
                    voteBtn.innerHTML = originalText;
                    voteBtn.disabled = false;
                });
        }

        function markAsVoted(pollId) {
            const votedPolls = JSON.parse(localStorage.getItem('votedPolls') || '[]');
            if (!votedPolls.includes(pollId)) {
                votedPolls.push(pollId);
                localStorage.setItem('votedPolls', JSON.stringify(votedPolls));
            }
        }

        function showVotedState(pollWidget) {
            const form = pollWidget.querySelector('.poll-form');
            const votedMsg = pollWidget.querySelector('.poll-voted');

            if (form) form.style.display = 'none';
            if (votedMsg) votedMsg.style.display = 'block';
        }

        function displayPollResults(pollId, results) {
            const pollWidget = document.querySelector(`[data-poll-id="${pollId}"]`);
            const form = pollWidget.querySelector('.poll-form');
            const resultsDiv = pollWidget.querySelector('.poll-results');
            const resultsContainer = resultsDiv.querySelector('.results-container');
            const totalVotesSpan = resultsDiv.querySelector('.total-votes');

            // Hide form and show results
            form.style.display = 'none';
            resultsDiv.style.display = 'block';

            // Calculate total votes
            const totalVotes = results.reduce((sum, option) => sum + option.vote_count, 0);
            totalVotesSpan.textContent = `${totalVotes} वोट`;

            // Generate results HTML
            let resultsHTML = '';
            results.forEach((option, index) => {
                const colors = ['primary', 'success', 'info', 'warning', 'danger', 'secondary'];
                const color = colors[index % colors.length];

                resultsHTML += `
                <div class="result-option">
                    <div class="w-100">
                        <div class="result-label d-flex justify-content-between">
                            <span>${option.option_text}</span>
                            <span class="result-stats">${option.vote_count} (${option.percentage}%)</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-${color}" 
                                 role="progressbar" 
                                 style="width: ${option.percentage}%"
                                 aria-valuenow="${option.percentage}" 
                                 aria-valuemin="0" 
                                 aria-valuemax="100">
                            </div>
                        </div>
                    </div>
                </div>
            `;
            });

            resultsContainer.innerHTML = resultsHTML;

            // Update total votes count in info section
            const totalVotesCount = pollWidget.querySelector('.total-votes-count');
            if (totalVotesCount) {
                totalVotesCount.textContent = totalVotes.toLocaleString();
            }
        }

        function showPollResults(pollId) {
            fetch(`<?= base_url('poll/results/') ?>${pollId}`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayPollResults(pollId, data.results);
                    } else {
                        showNotification('error', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('error', 'परिणाम लोड करने में त्रुटि हुई।');
                });
        }

        function showNotification(type, message) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    </script>
<?php endif; ?>