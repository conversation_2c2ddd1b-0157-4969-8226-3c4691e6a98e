<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\Poll;
use App\Models\PollOption;
use App\Models\PollVote;

class Polls extends BaseController
{
    protected $pollModel;
    protected $pollOptionModel;
    protected $pollVoteModel;

    public function __construct()
    {
        $this->pollModel = new Poll();
        $this->pollOptionModel = new PollOption();
        $this->pollVoteModel = new PollVote();
    }

    public function index()
    {
        $data = [
            'title' => 'Poll Management',
            'polls' => $this->pollModel->getPollsWithDetails()
        ];

        return view('admin/polls/index', $data);
    }

    public function create()
    {
        $data = [
            'title' => 'Create New Poll'
        ];

        return view('admin/polls/create', $data);
    }

    public function store()
    {
        $rules = [
            'title'          => 'required|min_length[5]|max_length[500]',
            'status'         => 'required|in_list[active,inactive,closed]',
            'multiple_choice' => 'required|in_list[0,1]',
            'show_results'   => 'required|in_list[after_vote,always,never]',
            'options'        => 'required',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $pollData = [
            'title'          => $this->request->getPost('title'),
            'description'    => $this->request->getPost('description'),
            'status'         => $this->request->getPost('status'),
            'multiple_choice' => $this->request->getPost('multiple_choice'),
            'show_results'   => $this->request->getPost('show_results'),
            'start_date'     => $this->request->getPost('start_date') ?: null,
            'end_date'       => $this->request->getPost('end_date') ?: null,
            'created_by'     => session()->get('user_id'),
        ];

        $pollId = $this->pollModel->insert($pollData);

        if ($pollId) {
            // Save poll options
            $options = $this->request->getPost('options');
            if (is_array($options)) {
                foreach ($options as $index => $optionText) {
                    if (!empty(trim($optionText))) {
                        $optionData = [
                            'poll_id'     => $pollId,
                            'option_text' => trim($optionText),
                            'sort_order'  => $index + 1,
                        ];
                        $this->pollOptionModel->save($optionData);
                    }
                }
            }

            return redirect()->to('/admin/polls')->with('success', 'Poll created successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create poll');
        }
    }

    public function edit($id)
    {
        $poll = $this->pollModel->getPollWithOptions($id);

        if (!$poll) {
            return redirect()->to('/admin/polls')->with('error', 'Poll not found');
        }

        $data = [
            'title' => 'Edit Poll',
            'poll' => $poll
        ];

        return view('admin/polls/edit', $data);
    }

    public function update($id)
    {
        $poll = $this->pollModel->find($id);

        if (!$poll) {
            return redirect()->to('/admin/polls')->with('error', 'Poll not found');
        }

        $rules = [
            'title'          => 'required|min_length[5]|max_length[500]',
            'status'         => 'required|in_list[active,inactive,closed]',
            'multiple_choice' => 'required|in_list[0,1]',
            'show_results'   => 'required|in_list[after_vote,always,never]',
            'options'        => 'required',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $pollData = [
            'title'          => $this->request->getPost('title'),
            'description'    => $this->request->getPost('description'),
            'status'         => $this->request->getPost('status'),
            'multiple_choice' => $this->request->getPost('multiple_choice'),
            'show_results'   => $this->request->getPost('show_results'),
            'start_date'     => $this->request->getPost('start_date') ?: null,
            'end_date'       => $this->request->getPost('end_date') ?: null,
        ];

        if ($this->pollModel->update($id, $pollData)) {
            // Delete existing options
            $this->pollOptionModel->where('poll_id', $id)->delete();

            // Save new options
            $options = $this->request->getPost('options');
            if (is_array($options)) {
                foreach ($options as $index => $optionText) {
                    if (!empty(trim($optionText))) {
                        $optionData = [
                            'poll_id'     => $id,
                            'option_text' => trim($optionText),
                            'sort_order'  => $index + 1,
                        ];
                        $this->pollOptionModel->save($optionData);
                    }
                }
            }

            return redirect()->to('/admin/polls')->with('success', 'Poll updated successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update poll');
        }
    }

    public function delete($id)
    {
        $poll = $this->pollModel->find($id);

        if (!$poll) {
            return redirect()->to('/admin/polls')->with('error', 'Poll not found');
        }

        if ($this->pollModel->delete($id)) {
            return redirect()->to('/admin/polls')->with('success', 'Poll deleted successfully');
        } else {
            return redirect()->to('/admin/polls')->with('error', 'Failed to delete poll');
        }
    }
}
