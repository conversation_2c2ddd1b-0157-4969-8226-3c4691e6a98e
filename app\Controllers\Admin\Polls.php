<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\Poll;
use App\Models\PollOption;
use App\Models\PollVote;

class Polls extends BaseController
{
    protected $pollModel;
    protected $pollOptionModel;
    protected $pollVoteModel;

    public function __construct()
    {
        $this->pollModel = new Poll();
        $this->pollOptionModel = new PollOption();
        $this->pollVoteModel = new PollVote();
    }

    public function index()
    {
        $data = [
            'title' => 'Poll Management',
            'polls' => $this->pollModel->getPollsWithDetails()
        ];

        return view('admin/polls/index', $data);
    }

    public function create()
    {
        $data = [
            'title' => 'Create New Poll'
        ];

        return view('admin/polls/create', $data);
    }

    public function store()
    {
        $rules = [
            'title'          => 'required|min_length[5]|max_length[500]',
            'status'         => 'required|in_list[active,inactive,closed]',
            'multiple_choice' => 'required|in_list[0,1]',
            'show_results'   => 'required|in_list[after_vote,always,never]',
            'options'        => 'required',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $pollData = [
            'title'          => $this->request->getPost('title'),
            'description'    => $this->request->getPost('description'),
            'status'         => $this->request->getPost('status'),
            'multiple_choice' => $this->request->getPost('multiple_choice'),
            'show_results'   => $this->request->getPost('show_results'),
            'start_date'     => $this->request->getPost('start_date') ?: null,
            'end_date'       => $this->request->getPost('end_date') ?: null,
            'created_by'     => session()->get('user_id'),
        ];

        $pollId = $this->pollModel->insert($pollData);

        if ($pollId) {
            // Save poll options
            $options = $this->request->getPost('options');
            if (is_array($options)) {
                foreach ($options as $index => $optionText) {
                    if (!empty(trim($optionText))) {
                        $optionData = [
                            'poll_id'     => $pollId,
                            'option_text' => trim($optionText),
                            'sort_order'  => $index + 1,
                        ];
                        $this->pollOptionModel->save($optionData);
                    }
                }
            }

            return redirect()->to('/admin/polls')->with('success', 'Poll created successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create poll');
        }
    }

    public function edit($id)
    {
        $poll = $this->pollModel->getPollWithOptions($id);

        if (!$poll) {
            return redirect()->to('/admin/polls')->with('error', 'Poll not found');
        }

        $data = [
            'title' => 'Edit Poll',
            'poll' => $poll
        ];

        return view('admin/polls/edit', $data);
    }

    public function update($id)
    {
        $poll = $this->pollModel->find($id);

        if (!$poll) {
            return redirect()->to('/admin/polls')->with('error', 'Poll not found');
        }

        $rules = [
            'title'          => 'required|min_length[5]|max_length[500]',
            'status'         => 'required|in_list[active,inactive,closed]',
            'multiple_choice' => 'required|in_list[0,1]',
            'show_results'   => 'required|in_list[after_vote,always,never]',
            'options'        => 'required',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $pollData = [
            'title'          => $this->request->getPost('title'),
            'description'    => $this->request->getPost('description'),
            'status'         => $this->request->getPost('status'),
            'multiple_choice' => $this->request->getPost('multiple_choice'),
            'show_results'   => $this->request->getPost('show_results'),
            'start_date'     => $this->request->getPost('start_date') ?: null,
            'end_date'       => $this->request->getPost('end_date') ?: null,
        ];

        if ($this->pollModel->update($id, $pollData)) {
            // Delete existing options
            $this->pollOptionModel->where('poll_id', $id)->delete();

            // Save new options
            $options = $this->request->getPost('options');
            if (is_array($options)) {
                foreach ($options as $index => $optionText) {
                    if (!empty(trim($optionText))) {
                        $optionData = [
                            'poll_id'     => $id,
                            'option_text' => trim($optionText),
                            'sort_order'  => $index + 1,
                        ];
                        $this->pollOptionModel->save($optionData);
                    }
                }
            }

            return redirect()->to('/admin/polls')->with('success', 'Poll updated successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update poll');
        }
    }

    public function delete($id)
    {
        $poll = $this->pollModel->find($id);

        if (!$poll) {
            return redirect()->to('/admin/polls')->with('error', 'Poll not found');
        }

        if ($this->pollModel->delete($id)) {
            return redirect()->to('/admin/polls')->with('success', 'Poll deleted successfully');
        } else {
            return redirect()->to('/admin/polls')->with('error', 'Failed to delete poll');
        }
    }

    public function analytics()
    {
        $polls = $this->pollModel->getPollsWithDetails();

        // Calculate summary statistics
        $totalPolls = count($polls);
        $activePolls = count(array_filter($polls, fn($poll) => $poll['status'] === 'active'));
        $totalVotes = array_sum(array_column($polls, 'total_votes'));
        $averageVotesPerPoll = $totalPolls > 0 ? round($totalVotes / $totalPolls, 1) : 0;

        // Poll status distribution
        $statusStats = [];
        foreach ($polls as $poll) {
            $status = $poll['status'];
            if (!isset($statusStats[$status])) {
                $statusStats[$status] = 0;
            }
            $statusStats[$status]++;
        }

        // Poll type distribution
        $typeStats = [];
        foreach ($polls as $poll) {
            $type = $poll['multiple_choice'] ? 'Multiple Choice' : 'Single Choice';
            if (!isset($typeStats[$type])) {
                $typeStats[$type] = 0;
            }
            $typeStats[$type]++;
        }

        // Top performing polls
        usort($polls, function ($a, $b) {
            return $b['total_votes'] <=> $a['total_votes'];
        });
        $topPolls = array_slice($polls, 0, 10);

        // Recent voting activity (last 30 days)
        $recentVotes = $this->pollVoteModel->select('DATE(voted_at) as vote_date, COUNT(*) as vote_count')
            ->where('voted_at >=', date('Y-m-d', strtotime('-30 days')))
            ->groupBy('DATE(voted_at)')
            ->orderBy('vote_date', 'ASC')
            ->findAll();

        // Poll engagement over time
        $engagementData = [];
        foreach ($polls as $poll) {
            $createdDate = date('Y-m', strtotime($poll['created_at']));
            if (!isset($engagementData[$createdDate])) {
                $engagementData[$createdDate] = ['polls' => 0, 'votes' => 0];
            }
            $engagementData[$createdDate]['polls']++;
            $engagementData[$createdDate]['votes'] += $poll['total_votes'];
        }

        // Detailed poll analytics
        $pollAnalytics = [];
        foreach ($topPolls as $poll) {
            $options = $this->pollOptionModel->getOptionsWithPercentages($poll['id']);
            $pollAnalytics[] = [
                'poll' => $poll,
                'options' => $options
            ];
        }

        $data = [
            'title' => 'Poll Analytics',
            'totalPolls' => $totalPolls,
            'activePolls' => $activePolls,
            'totalVotes' => $totalVotes,
            'averageVotesPerPoll' => $averageVotesPerPoll,
            'statusStats' => $statusStats,
            'typeStats' => $typeStats,
            'topPolls' => $topPolls,
            'recentVotes' => $recentVotes,
            'engagementData' => $engagementData,
            'pollAnalytics' => $pollAnalytics
        ];

        return view('admin/polls/analytics', $data);
    }

    public function results($id)
    {
        $poll = $this->pollModel->getPollWithOptions($id);

        if (!$poll) {
            return redirect()->to('/admin/polls')->with('error', 'Poll not found');
        }

        // Get detailed vote statistics
        $voteStats = $this->pollVoteModel->getPollVoteStats($id);

        // Get voting timeline
        $votingTimeline = $this->pollVoteModel->select('DATE(voted_at) as vote_date, COUNT(*) as vote_count')
            ->where('poll_id', $id)
            ->groupBy('DATE(voted_at)')
            ->orderBy('vote_date', 'ASC')
            ->findAll();

        // Get hourly voting pattern
        $hourlyPattern = $this->pollVoteModel->select('HOUR(voted_at) as vote_hour, COUNT(*) as vote_count')
            ->where('poll_id', $id)
            ->groupBy('HOUR(voted_at)')
            ->orderBy('vote_hour', 'ASC')
            ->findAll();

        // Calculate option percentages
        foreach ($poll['options'] as &$option) {
            $option['vote_count'] = $voteStats[$option['id']] ?? 0;
            $option['percentage'] = $poll['total_votes'] > 0 ?
                round(($option['vote_count'] / $poll['total_votes']) * 100, 1) : 0;
        }

        $data = [
            'title' => 'Poll Results - ' . $poll['title'],
            'poll' => $poll,
            'voteStats' => $voteStats,
            'votingTimeline' => $votingTimeline,
            'hourlyPattern' => $hourlyPattern
        ];

        return view('admin/polls/results', $data);
    }
}
