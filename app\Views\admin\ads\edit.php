<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">Edit Advertisement: <?= esc($ad['title']) ?></h4>
    <a href="<?= base_url('admin/ads') ?>" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to Ads
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <?php if (session()->getFlashdata('errors')): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <strong>Please fix the following errors:</strong>
                        <ul class="mb-0 mt-2">
                            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                <li><?= $error ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <form action="<?= base_url('admin/ads/update/' . $ad['id']) ?>" method="post" enctype="multipart/form-data" id="adForm">
                    <?= csrf_field() ?>

                    <div class="mb-3">
                        <label for="title" class="form-label">
                            <i class="fas fa-heading me-2"></i>Advertisement Title <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="title" name="title"
                            value="<?= old('title', $ad['title']) ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">
                            <i class="fas fa-align-left me-2"></i>Description
                        </label>
                        <textarea class="form-control" id="description" name="description" rows="3"><?= old('description', $ad['description']) ?></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="type" class="form-label">
                                <i class="fas fa-layer-group me-2"></i>Advertisement Type <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="type" name="type" required onchange="toggleImageField()">
                                <option value="banner" <?= old('type', $ad['type']) === 'banner' ? 'selected' : '' ?>>Banner (Image)</option>
                                <option value="text" <?= old('type', $ad['type']) === 'text' ? 'selected' : '' ?>>Text Only</option>
                                <option value="video" <?= old('type', $ad['type']) === 'video' ? 'selected' : '' ?>>Video</option>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="position" class="form-label">
                                <i class="fas fa-map-marker-alt me-2"></i>Position <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="position" name="position" required>
                                <option value="header" <?= old('position', $ad['position']) === 'header' ? 'selected' : '' ?>>Header</option>
                                <option value="sidebar" <?= old('position', $ad['position']) === 'sidebar' ? 'selected' : '' ?>>Sidebar</option>
                                <option value="footer" <?= old('position', $ad['position']) === 'footer' ? 'selected' : '' ?>>Footer</option>
                                <option value="content_top" <?= old('position', $ad['position']) === 'content_top' ? 'selected' : '' ?>>Content Top</option>
                                <option value="content_bottom" <?= old('position', $ad['position']) === 'content_bottom' ? 'selected' : '' ?>>Content Bottom</option>
                                <option value="between_news" <?= old('position', $ad['position']) === 'between_news' ? 'selected' : '' ?>>Between News</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3" id="imageField">
                        <label for="image" class="form-label">
                            <i class="fas fa-image me-2"></i>Advertisement Image
                        </label>
                        <?php if ($ad['type'] === 'banner' && $ad['image']): ?>
                            <div class="mb-2">
                                <img src="<?= base_url('uploads/ads/' . $ad['image']) ?>"
                                    alt="Current Image" class="img-thumbnail" style="max-width: 200px;">
                                <div class="form-text">Current image</div>
                            </div>
                        <?php endif; ?>
                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                        <div class="form-text">Upload new image to replace current one (Max: 2MB, JPG/PNG)</div>
                    </div>

                    <div class="mb-3">
                        <label for="link_url" class="form-label">
                            <i class="fas fa-link me-2"></i>Link URL
                        </label>
                        <input type="url" class="form-control" id="link_url" name="link_url"
                            value="<?= old('link_url', $ad['link_url']) ?>">
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="status" class="form-label">
                                <i class="fas fa-toggle-on me-2"></i>Status <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="active" <?= old('status', $ad['status']) === 'active' ? 'selected' : '' ?>>Active</option>
                                <option value="inactive" <?= old('status', $ad['status']) === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                            </select>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="start_date" class="form-label">
                                <i class="fas fa-calendar-alt me-2"></i>Start Date
                            </label>
                            <input type="datetime-local" class="form-control" id="start_date" name="start_date"
                                value="<?= old('start_date', $ad['start_date'] ? date('Y-m-d\TH:i', strtotime($ad['start_date'])) : '') ?>">
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="end_date" class="form-label">
                                <i class="fas fa-calendar-times me-2"></i>End Date
                            </label>
                            <input type="datetime-local" class="form-control" id="end_date" name="end_date"
                                value="<?= old('end_date', $ad['end_date'] ? date('Y-m-d\TH:i', strtotime($ad['end_date'])) : '') ?>">
                        </div>
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <a href="<?= base_url('admin/ads') ?>" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Advertisement
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Advertisement Information</h6>
            </div>
            <div class="card-body">
                <p><strong>Ad ID:</strong> <?= $ad['id'] ?></p>
                <p><strong>Type:</strong> <?= ucfirst($ad['type']) ?></p>
                <p><strong>Position:</strong> <?= ucfirst(str_replace('_', ' ', $ad['position'])) ?></p>
                <p><strong>Created:</strong> <?= date('M d, Y H:i', strtotime($ad['created_at'])) ?></p>
                <p><strong>Last Updated:</strong> <?= date('M d, Y H:i', strtotime($ad['updated_at'])) ?></p>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-line me-2"></i>Performance Stats</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <h5 class="mb-0"><?= number_format($ad['clicks']) ?></h5>
                        <small class="text-muted">Clicks</small>
                    </div>
                    <div class="col-4">
                        <h5 class="mb-0"><?= number_format($ad['impressions']) ?></h5>
                        <small class="text-muted">Views</small>
                    </div>
                    <div class="col-4">
                        <h5 class="mb-0">
                            <?= $ad['impressions'] > 0 ? round(($ad['clicks'] / $ad['impressions']) * 100, 2) : 0 ?>%
                        </h5>
                        <small class="text-muted">CTR</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    function toggleImageField() {
        const type = document.getElementById('type').value;
        const imageField = document.getElementById('imageField');

        if (type === 'banner') {
            imageField.style.display = 'block';
        } else {
            imageField.style.display = 'none';
        }
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        toggleImageField();
    });
</script>
<?= $this->endSection() ?>