<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-line me-2"></i>Poll Analytics</h2>
                <a href="<?= base_url('admin/polls') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Polls
                </a>
            </div>

            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= number_format($totalPolls) ?></h4>
                                    <p class="mb-0">Total Polls</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-poll fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= number_format($activePolls) ?></h4>
                                    <p class="mb-0">Active Polls</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-play-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= number_format($totalVotes) ?></h4>
                                    <p class="mb-0">Total Votes</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-vote-yea fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= $averageVotesPerPoll ?></h4>
                                    <p class="mb-0">Avg Votes/Poll</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-bar fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-pie me-2"></i>Poll Status Distribution
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="statusChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-doughnut me-2"></i>Poll Type Distribution
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="typeChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Voting Activity Chart -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-line me-2"></i>Recent Voting Activity (Last 30 Days)
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="activityChart" width="400" height="100"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Performing Polls -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-trophy me-2"></i>Top Performing Polls
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Rank</th>
                                            <th>Poll Title</th>
                                            <th>Status</th>
                                            <th>Type</th>
                                            <th>Total Votes</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($topPolls as $index => $poll): ?>
                                            <tr>
                                                <td>
                                                    <span class="badge bg-<?= $index < 3 ? 'warning' : 'secondary' ?>">
                                                        #<?= $index + 1 ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="fw-bold"><?= esc($poll['title']) ?></div>
                                                    <?php if ($poll['description']): ?>
                                                        <small class="text-muted">
                                                            <?= esc(substr($poll['description'], 0, 80)) ?>...
                                                        </small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $statusClass = match($poll['status']) {
                                                        'active' => 'bg-success',
                                                        'inactive' => 'bg-secondary',
                                                        'closed' => 'bg-danger',
                                                        default => 'bg-secondary'
                                                    };
                                                    ?>
                                                    <span class="badge <?= $statusClass ?>"><?= ucfirst($poll['status']) ?></span>
                                                </td>
                                                <td>
                                                    <span class="badge <?= $poll['multiple_choice'] ? 'bg-info' : 'bg-primary' ?>">
                                                        <?= $poll['multiple_choice'] ? 'Multiple' : 'Single' ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-warning text-dark fs-6">
                                                        <i class="fas fa-vote-yea me-1"></i><?= number_format($poll['total_votes']) ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <small><?= date('d M Y', strtotime($poll['created_at'])) ?></small>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="<?= base_url('admin/polls/results/' . $poll['id']) ?>" 
                                                           class="btn btn-sm btn-outline-info" title="View Results">
                                                            <i class="fas fa-chart-bar"></i>
                                                        </a>
                                                        <a href="<?= base_url('admin/polls/edit/' . $poll['id']) ?>" 
                                                           class="btn btn-sm btn-outline-primary" title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Status Distribution Chart
const statusCtx = document.getElementById('statusChart').getContext('2d');
const statusChart = new Chart(statusCtx, {
    type: 'pie',
    data: {
        labels: <?= json_encode(array_keys($statusStats)) ?>,
        datasets: [{
            data: <?= json_encode(array_values($statusStats)) ?>,
            backgroundColor: [
                'rgba(40, 167, 69, 0.8)',   // Active - Green
                'rgba(108, 117, 125, 0.8)', // Inactive - Gray
                'rgba(220, 53, 69, 0.8)',   // Closed - Red
            ],
            borderColor: [
                'rgba(40, 167, 69, 1)',
                'rgba(108, 117, 125, 1)',
                'rgba(220, 53, 69, 1)',
            ],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Type Distribution Chart
const typeCtx = document.getElementById('typeChart').getContext('2d');
const typeChart = new Chart(typeCtx, {
    type: 'doughnut',
    data: {
        labels: <?= json_encode(array_keys($typeStats)) ?>,
        datasets: [{
            data: <?= json_encode(array_values($typeStats)) ?>,
            backgroundColor: [
                'rgba(0, 123, 255, 0.8)',   // Single Choice - Blue
                'rgba(23, 162, 184, 0.8)',  // Multiple Choice - Cyan
            ],
            borderColor: [
                'rgba(0, 123, 255, 1)',
                'rgba(23, 162, 184, 1)',
            ],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Recent Activity Chart
const activityCtx = document.getElementById('activityChart').getContext('2d');
const activityChart = new Chart(activityCtx, {
    type: 'line',
    data: {
        labels: <?= json_encode(array_column($recentVotes, 'vote_date')) ?>,
        datasets: [{
            label: 'Daily Votes',
            data: <?= json_encode(array_column($recentVotes, 'vote_count')) ?>,
            borderColor: 'rgba(255, 193, 7, 1)',
            backgroundColor: 'rgba(255, 193, 7, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
</script>
<?= $this->endSection() ?>
