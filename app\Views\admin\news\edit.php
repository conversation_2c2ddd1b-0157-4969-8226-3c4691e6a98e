<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">Edit Article: <?= esc($news['title']) ?></h4>
    <a href="<?= base_url('admin/news') ?>" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to News
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <?php if (session()->getFlashdata('errors')): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <strong>Please fix the following errors:</strong>
                        <ul class="mb-0 mt-2">
                            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                <li><?= $error ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <form action="<?= base_url('admin/news/update/' . $news['id']) ?>" method="post" enctype="multipart/form-data">
                    <?= csrf_field() ?>

                    <div class="mb-3">
                        <label for="title" class="form-label">
                            <i class="fas fa-heading me-2"></i>Title (शीर्षक) <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="title" name="title"
                            value="<?= old('title', $news['title']) ?>" required
                            placeholder="समाचार का शीर्षक यहाँ लिखें...">
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">
                            <i class="fas fa-align-left me-2"></i>Short Description (संक्षिप्त विवरण) <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control" id="description" name="description" rows="3" required
                            placeholder="समाचार का संक्षिप्त विवरण..."><?= old('description', $news['description']) ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="content" class="form-label">
                            <i class="fas fa-file-alt me-2"></i>Full Content (पूरी सामग्री) <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control" id="content" name="content" rows="10" required
                            placeholder="समाचार की पूरी सामग्री यहाँ लिखें..."><?= old('content', $news['content']) ?></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="category_id" class="form-label">
                                <i class="fas fa-tags me-2"></i>Category (श्रेणी) <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="category_id" name="category_id" required>
                                <option value="">Select Category</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= $category['id'] ?>"
                                        <?= old('category_id', $news['category_id']) == $category['id'] ? 'selected' : '' ?>>
                                        <?= esc($category['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">
                                <i class="fas fa-toggle-on me-2"></i>Status <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="">Select Status</option>
                                <option value="draft" <?= old('status', $news['status']) === 'draft' ? 'selected' : '' ?>>Draft</option>
                                <option value="published" <?= old('status', $news['status']) === 'published' ? 'selected' : '' ?>>Published</option>
                                <option value="archived" <?= old('status', $news['status']) === 'archived' ? 'selected' : '' ?>>Archived</option>
                            </select>
                        </div>
                    </div>

                    <!-- Media Type Selection -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-photo-video me-2"></i>Media Type (मीडिया प्रकार) <span class="text-danger">*</span>
                        </label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="media_type" id="media_image" value="image"
                                        <?= old('media_type', $news['media_type'] ?? 'image') === 'image' ? 'checked' : '' ?> required>
                                    <label class="form-check-label" for="media_image">
                                        <i class="fas fa-image me-1"></i>Image (चित्र)
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="media_type" id="media_video" value="video"
                                        <?= old('media_type', $news['media_type'] ?? 'image') === 'video' ? 'checked' : '' ?> required>
                                    <label class="form-check-label" for="media_video">
                                        <i class="fas fa-video me-1"></i>Video (वीडियो)
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="form-text">Choose whether this article will feature an image or video</div>
                    </div>

                    <!-- Image Upload Section -->
                    <div class="mb-3" id="image-section">
                        <label for="image" class="form-label">
                            <i class="fas fa-image me-2"></i>Featured Image (मुख्य चित्र)
                        </label>
                        <?php if (($news['media_type'] ?? 'image') === 'image' && $news['image']): ?>
                            <div class="mb-2">
                                <img src="<?= base_url('uploads/news/' . $news['image']) ?>"
                                    alt="Current Image" class="img-thumbnail" style="max-width: 200px;">
                                <div class="form-text">Current image</div>
                            </div>
                        <?php endif; ?>
                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                        <div class="form-text">Upload a new image to replace the current one (Max: 2MB, JPG/PNG)</div>
                    </div>

                    <!-- YouTube Video Section -->
                    <div class="mb-3" id="video-section" style="display: none;">
                        <label for="youtube_url" class="form-label">
                            <i class="fab fa-youtube me-2"></i>YouTube Video URL (यूट्यूब वीडियो लिंक)
                        </label>
                        <?php if (($news['media_type'] ?? 'image') === 'video' && $news['youtube_url']): ?>
                            <div class="mb-2">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fab fa-youtube me-2"></i>Current Video</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="ratio ratio-16x9">
                                            <iframe src="https://www.youtube.com/embed/<?= esc($news['youtube_id']) ?>" frameborder="0" allowfullscreen></iframe>
                                        </div>
                                        <div class="form-text mt-2">Current video: <?= esc($news['youtube_url']) ?></div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                        <input type="url" class="form-control" id="youtube_url" name="youtube_url"
                            value="<?= old('youtube_url', $news['youtube_url'] ?? '') ?>"
                            placeholder="https://www.youtube.com/watch?v=VIDEO_ID or https://youtu.be/VIDEO_ID">
                        <div class="form-text">
                            <strong>Supported YouTube URL formats:</strong><br>
                            • https://www.youtube.com/watch?v=VIDEO_ID<br>
                            • https://youtu.be/VIDEO_ID<br>
                            • https://www.youtube.com/embed/VIDEO_ID
                        </div>
                        <div id="youtube-preview" class="mt-3" style="display: none;">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fab fa-youtube me-2"></i>Video Preview</h6>
                                </div>
                                <div class="card-body">
                                    <div class="ratio ratio-16x9">
                                        <iframe id="youtube-iframe" src="" frameborder="0" allowfullscreen></iframe>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="featured" name="featured" value="1"
                                <?= old('featured', $news['featured']) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="featured">
                                <i class="fas fa-star me-2"></i>Mark as Featured Article
                            </label>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <a href="<?= base_url('admin/news') ?>" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Article
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Article Information</h6>
            </div>
            <div class="card-body">
                <p><strong>Article ID:</strong> <?= $news['id'] ?></p>
                <p><strong>Slug:</strong> <?= esc($news['slug']) ?></p>
                <p><strong>Views:</strong> <?= number_format($news['views']) ?></p>
                <p><strong>Created:</strong> <?= date('M d, Y H:i', strtotime($news['created_at'])) ?></p>
                <p><strong>Last Updated:</strong> <?= date('M d, Y H:i', strtotime($news['updated_at'])) ?></p>
                <?php if ($news['published_at']): ?>
                    <p><strong>Published:</strong> <?= date('M d, Y H:i', strtotime($news['published_at'])) ?></p>
                <?php endif; ?>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-line me-2"></i>Article Stats</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h5 class="mb-0"><?= number_format($news['views']) ?></h5>
                            <small class="text-muted">Views</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h5 class="mb-0"><?= ucfirst($news['status']) ?></h5>
                        <small class="text-muted">Status</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-edit me-2"></i>Editing Tips</h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li><i class="fas fa-check text-success me-2"></i>Review content for accuracy</li>
                    <li><i class="fas fa-check text-success me-2"></i>Update images if needed</li>
                    <li><i class="fas fa-check text-success me-2"></i>Check category assignment</li>
                    <li><i class="fas fa-check text-success me-2"></i>Verify publication status</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Media type switching
    document.addEventListener('DOMContentLoaded', function() {
        const imageRadio = document.getElementById('media_image');
        const videoRadio = document.getElementById('media_video');
        const imageSection = document.getElementById('image-section');
        const videoSection = document.getElementById('video-section');
        const imageInput = document.getElementById('image');
        const youtubeInput = document.getElementById('youtube_url');

        function toggleMediaSections() {
            if (imageRadio.checked) {
                imageSection.style.display = 'block';
                videoSection.style.display = 'none';
                imageInput.required = false; // Not required for edit
                youtubeInput.required = false;
            } else if (videoRadio.checked) {
                imageSection.style.display = 'none';
                videoSection.style.display = 'block';
                imageInput.required = false;
                youtubeInput.required = false; // Not required for edit if already has video
            }
        }

        imageRadio.addEventListener('change', toggleMediaSections);
        videoRadio.addEventListener('change', toggleMediaSections);

        // Initialize on page load
        toggleMediaSections();
    });

    // YouTube URL validation and preview
    document.getElementById('youtube_url').addEventListener('input', function() {
        const url = this.value.trim();
        const preview = document.getElementById('youtube-preview');
        const iframe = document.getElementById('youtube-iframe');

        if (url) {
            const videoId = extractYouTubeId(url);
            if (videoId) {
                // Show preview
                iframe.src = `https://www.youtube.com/embed/${videoId}`;
                preview.style.display = 'block';
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                // Invalid URL
                hideYouTubePreview();
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        } else {
            hideYouTubePreview();
            this.classList.remove('is-valid', 'is-invalid');
        }
    });

    function extractYouTubeId(url) {
        const patterns = [
            /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})/,
            /youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})/,
            /youtu\.be\/([a-zA-Z0-9_-]{11})/,
            /youtube\.com\/embed\/([a-zA-Z0-9_-]{11})/
        ];

        for (let pattern of patterns) {
            const match = url.match(pattern);
            if (match) {
                return match[1];
            }
        }
        return null;
    }

    function hideYouTubePreview() {
        const preview = document.getElementById('youtube-preview');
        const iframe = document.getElementById('youtube-iframe');
        preview.style.display = 'none';
        iframe.src = '';
    }

    // Image preview for new uploads
    document.getElementById('image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                // Add preview logic here if needed
            };
            reader.readAsDataURL(file);
        }
    });
</script>
<?= $this->endSection() ?>