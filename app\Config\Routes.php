<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
// Frontend Routes
$routes->get('/', 'Home::index');
$routes->get('news/(:any)', 'Home::news/$1');
$routes->get('category/(:any)', 'Home::category/$1');

// Poll Routes
$routes->post('poll/vote', 'PollController::vote');
$routes->get('poll/results/(:num)', 'PollController::getResults/$1');

// Epaper Routes
$routes->get('epaper', 'EpaperController::index');
$routes->get('epaper/view/(:num)', 'EpaperController::view/$1');
$routes->get('epaper/download/(:num)', 'EpaperController::download/$1');
$routes->get('epaper/archive', 'EpaperController::archive');
$routes->get('epaper/archive/(:num)', 'EpaperController::archive/$1');
$routes->get('epaper/archive/(:num)/(:num)', 'EpaperController::archive/$1/$2');
$routes->get('epaper/search', 'EpaperController::search');

// API Routes for Ad Tracking
$routes->post('api/track-ad-click/(:num)', 'Api\AdTracking::trackClick/$1');
$routes->post('api/track-ad-impression/(:num)', 'Api\AdTracking::trackImpression/$1');

// Authentication Routes
$routes->group('admin', function ($routes) {
    $routes->get('login', 'Auth::login');
    $routes->post('authenticate', 'Auth::authenticate');
    $routes->get('logout', 'Auth::logout');
});

// Admin Panel Routes (Protected)
$routes->group('admin', ['filter' => 'auth'], function ($routes) {
    $routes->get('dashboard', 'Admin\Dashboard::index');

    // User Management (Admin only)
    $routes->group('users', ['filter' => 'auth:admin'], function ($routes) {
        $routes->get('/', 'Admin\Users::index');
        $routes->get('create', 'Admin\Users::create');
        $routes->post('store', 'Admin\Users::store');
        $routes->get('edit/(:num)', 'Admin\Users::edit/$1');
        $routes->post('update/(:num)', 'Admin\Users::update/$1');
        $routes->get('delete/(:num)', 'Admin\Users::delete/$1');
    });

    // News Management
    $routes->group('news', function ($routes) {
        $routes->get('/', 'Admin\News::index');
        $routes->get('create', 'Admin\News::create');
        $routes->post('store', 'Admin\News::store');
        $routes->get('edit/(:num)', 'Admin\News::edit/$1');
        $routes->post('update/(:num)', 'Admin\News::update/$1');
        $routes->get('delete/(:num)', 'Admin\News::delete/$1');
    });

    // Category Management
    $routes->group('categories', function ($routes) {
        $routes->get('/', 'Admin\Categories::index');
        $routes->get('create', 'Admin\Categories::create');
        $routes->post('store', 'Admin\Categories::store');
        $routes->get('edit/(:num)', 'Admin\Categories::edit/$1');
        $routes->post('update/(:num)', 'Admin\Categories::update/$1');
        $routes->get('delete/(:num)', 'Admin\Categories::delete/$1');
    });

    // Tag Management
    $routes->group('tags', function ($routes) {
        $routes->get('/', 'Admin\Tags::index');
        $routes->get('create', 'Admin\Tags::create');
        $routes->post('store', 'Admin\Tags::store');
        $routes->get('edit/(:num)', 'Admin\Tags::edit/$1');
        $routes->post('update/(:num)', 'Admin\Tags::update/$1');
        $routes->get('delete/(:num)', 'Admin\Tags::delete/$1');
    });

    // Ads Management
    $routes->group('ads', function ($routes) {
        $routes->get('/', 'Admin\Ads::index');
        $routes->get('analytics', 'Admin\Ads::analytics');
        $routes->get('create', 'Admin\Ads::create');
        $routes->post('store', 'Admin\Ads::store');
        $routes->get('edit/(:num)', 'Admin\Ads::edit/$1');
        $routes->post('update/(:num)', 'Admin\Ads::update/$1');
        $routes->get('delete/(:num)', 'Admin\Ads::delete/$1');
    });

    // Breaking News Management
    $routes->group('breaking-news', function ($routes) {
        $routes->get('/', 'Admin\BreakingNews::index');
        $routes->get('create', 'Admin\BreakingNews::create');
        $routes->post('store', 'Admin\BreakingNews::store');
        $routes->get('edit/(:num)', 'Admin\BreakingNews::edit/$1');
        $routes->post('update/(:num)', 'Admin\BreakingNews::update/$1');
        $routes->get('delete/(:num)', 'Admin\BreakingNews::delete/$1');
        $routes->post('toggle-status/(:num)', 'Admin\BreakingNews::toggleStatus/$1');
    });

    // Polls Management
    $routes->group('polls', function ($routes) {
        $routes->get('/', 'Admin\Polls::index');
        $routes->get('analytics', 'Admin\Polls::analytics');
        $routes->get('results/(:num)', 'Admin\Polls::results/$1');
        $routes->get('create', 'Admin\Polls::create');
        $routes->post('store', 'Admin\Polls::store');
        $routes->get('edit/(:num)', 'Admin\Polls::edit/$1');
        $routes->post('update/(:num)', 'Admin\Polls::update/$1');
        $routes->get('delete/(:num)', 'Admin\Polls::delete/$1');
    });

    // Epapers Management
    $routes->group('epapers', function ($routes) {
        $routes->get('/', 'Admin\Epapers::index');
        $routes->get('analytics', 'Admin\Epapers::analytics');
        $routes->get('create', 'Admin\Epapers::create');
        $routes->post('store', 'Admin\Epapers::store');
        $routes->get('edit/(:num)', 'Admin\Epapers::edit/$1');
        $routes->post('update/(:num)', 'Admin\Epapers::update/$1');
        $routes->get('delete/(:num)', 'Admin\Epapers::delete/$1');
    });

    // Settings Management
    $routes->group('settings', function ($routes) {
        $routes->get('/', 'Admin\Settings::index');

        // Profile Management
        $routes->get('profile', 'Admin\Settings::profile');
        $routes->post('profile/update', 'Admin\Settings::updateProfile');
        $routes->get('change-password', 'Admin\Settings::changePassword');
        $routes->post('change-password', 'Admin\Settings::updatePassword');

        // Site Settings
        $routes->get('site', 'Admin\Settings::siteSettings');
        $routes->post('site/update', 'Admin\Settings::updateSiteSettings');

        // Contact Settings
        $routes->get('contact', 'Admin\Settings::contactSettings');
        $routes->post('contact/update', 'Admin\Settings::updateContactSettings');

        // Social Media Settings
        $routes->get('social', 'Admin\Settings::socialSettings');
        $routes->post('social/update', 'Admin\Settings::updateSocialSettings');

        // Footer Settings
        $routes->get('footer', 'Admin\Settings::footerSettings');
        $routes->post('footer/update', 'Admin\Settings::updateFooterSettings');

        // Analytics Settings
        $routes->get('analytics', 'Admin\Settings::analyticsSettings');
        $routes->post('analytics/update', 'Admin\Settings::updateAnalyticsSettings');

        // SEO Settings
        $routes->get('seo', 'Admin\Settings::seoSettings');
        $routes->post('seo/update', 'Admin\Settings::updateSeoSettings');
    });
});
