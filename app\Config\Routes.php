<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
// Frontend Routes
$routes->get('/', 'Home::index');
$routes->get('news/(:segment)', 'Home::news/$1');
$routes->get('category/(:segment)', 'Home::category/$1');

// API Routes for Ad Tracking
$routes->post('api/track-ad-click/(:num)', 'Api\AdTracking::trackClick/$1');
$routes->post('api/track-ad-impression/(:num)', 'Api\AdTracking::trackImpression/$1');

// Authentication Routes
$routes->group('admin', function ($routes) {
    $routes->get('login', 'Auth::login');
    $routes->post('authenticate', 'Auth::authenticate');
    $routes->get('logout', 'Auth::logout');
});

// Admin Panel Routes (Protected)
$routes->group('admin', ['filter' => 'auth'], function ($routes) {
    $routes->get('dashboard', 'Admin\Dashboard::index');

    // User Management (Admin only)
    $routes->group('users', ['filter' => 'auth:admin'], function ($routes) {
        $routes->get('/', 'Admin\Users::index');
        $routes->get('create', 'Admin\Users::create');
        $routes->post('store', 'Admin\Users::store');
        $routes->get('edit/(:num)', 'Admin\Users::edit/$1');
        $routes->post('update/(:num)', 'Admin\Users::update/$1');
        $routes->get('delete/(:num)', 'Admin\Users::delete/$1');
    });

    // News Management
    $routes->group('news', function ($routes) {
        $routes->get('/', 'Admin\News::index');
        $routes->get('create', 'Admin\News::create');
        $routes->post('store', 'Admin\News::store');
        $routes->get('edit/(:num)', 'Admin\News::edit/$1');
        $routes->post('update/(:num)', 'Admin\News::update/$1');
        $routes->get('delete/(:num)', 'Admin\News::delete/$1');
    });

    // Category Management
    $routes->group('categories', function ($routes) {
        $routes->get('/', 'Admin\Categories::index');
        $routes->get('create', 'Admin\Categories::create');
        $routes->post('store', 'Admin\Categories::store');
        $routes->get('edit/(:num)', 'Admin\Categories::edit/$1');
        $routes->post('update/(:num)', 'Admin\Categories::update/$1');
        $routes->get('delete/(:num)', 'Admin\Categories::delete/$1');
    });

    // Tag Management
    $routes->group('tags', function ($routes) {
        $routes->get('/', 'Admin\Tags::index');
        $routes->get('create', 'Admin\Tags::create');
        $routes->post('store', 'Admin\Tags::store');
        $routes->get('edit/(:num)', 'Admin\Tags::edit/$1');
        $routes->post('update/(:num)', 'Admin\Tags::update/$1');
        $routes->get('delete/(:num)', 'Admin\Tags::delete/$1');
    });

    // Ads Management
    $routes->group('ads', function ($routes) {
        $routes->get('/', 'Admin\Ads::index');
        $routes->get('create', 'Admin\Ads::create');
        $routes->post('store', 'Admin\Ads::store');
        $routes->get('edit/(:num)', 'Admin\Ads::edit/$1');
        $routes->post('update/(:num)', 'Admin\Ads::update/$1');
        $routes->get('delete/(:num)', 'Admin\Ads::delete/$1');
    });
});
