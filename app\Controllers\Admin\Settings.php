<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\Setting;
use App\Models\User;

class Settings extends BaseController
{
    protected $settingModel;
    protected $userModel;

    public function __construct()
    {
        $this->settingModel = new Setting();
        $this->userModel = new User();
    }

    public function index()
    {
        $data = [
            'title' => 'Settings Management',
            'settingsGrouped' => $this->settingModel->getSettingsGrouped(),
            'categories' => $this->settingModel->getCategories()
        ];

        return view('admin/settings/index', $data);
    }

    public function profile()
    {
        $userId = session()->get('user_id');
        $user = $this->userModel->find($userId);

        if (!$user) {
            return redirect()->to('/admin/dashboard')->with('error', 'User not found');
        }

        $data = [
            'title' => 'Profile Management',
            'user' => $user
        ];

        return view('admin/settings/profile', $data);
    }

    public function updateProfile()
    {
        $userId = session()->get('user_id');
        $user = $this->userModel->find($userId);

        if (!$user) {
            return redirect()->to('/admin/dashboard')->with('error', 'User not found');
        }

        $rules = [
            'full_name' => 'required|min_length[3]|max_length[100]',
            'email'     => 'required|valid_email|max_length[100]',
            'phone'     => 'permit_empty|max_length[20]',
        ];

        // Check if email is being changed and if it's unique
        if ($this->request->getPost('email') !== $user['email']) {
            $rules['email'] .= '|is_unique[users.email]';
        }

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'full_name' => $this->request->getPost('full_name'),
            'email'     => $this->request->getPost('email'),
            'phone'     => $this->request->getPost('phone'),
        ];

        // Handle profile image upload
        $profileImage = $this->request->getFile('profile_image');
        if ($profileImage && $profileImage->isValid()) {
            $uploadPath = FCPATH . 'uploads/profiles/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            // Delete old profile image
            if ($user['profile_image'] && file_exists($uploadPath . $user['profile_image'])) {
                unlink($uploadPath . $user['profile_image']);
            }

            $fileName = 'profile_' . $userId . '_' . time() . '.' . $profileImage->getExtension();
            if ($profileImage->move($uploadPath, $fileName)) {
                $data['profile_image'] = $fileName;
            }
        }

        try {
            if ($this->userModel->update($userId, $data)) {
                // Update session data
                session()->set([
                    'full_name' => $data['full_name'],
                    'email' => $data['email']
                ]);

                return redirect()->to('/admin/settings/profile')->with('success', 'Profile updated successfully');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to update profile');
            }
        } catch (\Exception $e) {
            log_message('error', 'Profile update error: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while updating profile');
        }
    }

    public function changePassword()
    {
        $data = [
            'title' => 'Change Password'
        ];

        return view('admin/settings/change_password', $data);
    }

    public function updatePassword()
    {
        $rules = [
            'current_password' => 'required',
            'new_password'     => 'required|min_length[6]|max_length[255]',
            'confirm_password' => 'required|matches[new_password]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->with('errors', $this->validator->getErrors());
        }

        $userId = session()->get('user_id');
        $user = $this->userModel->find($userId);

        if (!$user) {
            return redirect()->to('/admin/dashboard')->with('error', 'User not found');
        }

        // Verify current password
        if (!password_verify($this->request->getPost('current_password'), $user['password'])) {
            return redirect()->back()->with('error', 'Current password is incorrect');
        }

        // Update password
        $newPassword = password_hash($this->request->getPost('new_password'), PASSWORD_DEFAULT);

        try {
            if ($this->userModel->update($userId, ['password' => $newPassword])) {
                return redirect()->to('/admin/settings/change-password')->with('success', 'Password changed successfully');
            } else {
                return redirect()->back()->with('error', 'Failed to change password');
            }
        } catch (\Exception $e) {
            log_message('error', 'Password change error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred while changing password');
        }
    }

    public function siteSettings()
    {
        $siteSettings = $this->settingModel->getSettingsByCategory('site');

        $data = [
            'title' => 'Site Settings',
            'settings' => $siteSettings
        ];

        return view('admin/settings/site', $data);
    }

    public function updateSiteSettings()
    {
        $rules = [
            'site_name'    => 'required|max_length[100]',
            'site_tagline' => 'permit_empty|max_length[200]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $settings = [
            'site_name'    => $this->request->getPost('site_name'),
            'site_tagline' => $this->request->getPost('site_tagline'),
        ];

        // Handle logo upload
        $logo = $this->request->getFile('site_logo');
        if ($logo && $logo->isValid()) {
            $uploadPath = FCPATH . 'uploads/site/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            $fileName = 'logo_' . time() . '.' . $logo->getExtension();
            if ($logo->move($uploadPath, $fileName)) {
                $settings['site_logo'] = $fileName;
            }
        }

        // Handle favicon upload
        $favicon = $this->request->getFile('site_favicon');
        if ($favicon && $favicon->isValid()) {
            $uploadPath = FCPATH . 'uploads/site/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            $fileName = 'favicon_' . time() . '.' . $favicon->getExtension();
            if ($favicon->move($uploadPath, $fileName)) {
                $settings['site_favicon'] = $fileName;
            }
        }

        try {
            if ($this->settingModel->updateSettings($settings)) {
                return redirect()->to('/admin/settings/site')->with('success', 'Site settings updated successfully');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to update site settings');
            }
        } catch (\Exception $e) {
            log_message('error', 'Site settings update error: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while updating site settings');
        }
    }

    public function contactSettings()
    {
        $contactSettings = $this->settingModel->getSettingsByCategory('contact');

        $data = [
            'title' => 'Contact Settings',
            'settings' => $contactSettings
        ];

        return view('admin/settings/contact', $data);
    }

    public function updateContactSettings()
    {
        $rules = [
            'contact_email'    => 'permit_empty|valid_email',
            'contact_phone'    => 'permit_empty|max_length[20]',
            'contact_whatsapp' => 'permit_empty|max_length[20]',
            'contact_address'  => 'permit_empty|max_length[500]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $settings = [
            'contact_email'    => $this->request->getPost('contact_email'),
            'contact_phone'    => $this->request->getPost('contact_phone'),
            'contact_whatsapp' => $this->request->getPost('contact_whatsapp'),
            'contact_address'  => $this->request->getPost('contact_address'),
        ];

        try {
            if ($this->settingModel->updateSettings($settings)) {
                return redirect()->to('/admin/settings/contact')->with('success', 'Contact settings updated successfully');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to update contact settings');
            }
        } catch (\Exception $e) {
            log_message('error', 'Contact settings update error: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while updating contact settings');
        }
    }

    public function socialSettings()
    {
        $socialSettings = $this->settingModel->getSettingsByCategory('social');

        $data = [
            'title' => 'Social Media Settings',
            'settings' => $socialSettings
        ];

        return view('admin/settings/social', $data);
    }

    public function updateSocialSettings()
    {
        $settings = [
            'facebook_url'  => $this->request->getPost('facebook_url'),
            'twitter_url'   => $this->request->getPost('twitter_url'),
            'instagram_url' => $this->request->getPost('instagram_url'),
            'youtube_url'   => $this->request->getPost('youtube_url'),
            'linkedin_url'  => $this->request->getPost('linkedin_url'),
            'telegram_url'  => $this->request->getPost('telegram_url'),
        ];

        try {
            if ($this->settingModel->updateSettings($settings)) {
                return redirect()->to('/admin/settings/social')->with('success', 'Social media settings updated successfully');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to update social media settings');
            }
        } catch (\Exception $e) {
            log_message('error', 'Social settings update error: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while updating social media settings');
        }
    }

    public function footerSettings()
    {
        $footerSettings = $this->settingModel->getSettingsByCategory('footer');

        $data = [
            'title' => 'Footer Settings',
            'settings' => $footerSettings
        ];

        return view('admin/settings/footer', $data);
    }

    public function updateFooterSettings()
    {
        $settings = [
            'footer_copyright'   => $this->request->getPost('footer_copyright'),
            'footer_description' => $this->request->getPost('footer_description'),
        ];

        try {
            if ($this->settingModel->updateSettings($settings)) {
                return redirect()->to('/admin/settings/footer')->with('success', 'Footer settings updated successfully');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to update footer settings');
            }
        } catch (\Exception $e) {
            log_message('error', 'Footer settings update error: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while updating footer settings');
        }
    }

    public function analyticsSettings()
    {
        $analyticsSettings = $this->settingModel->getSettingsByCategory('analytics');

        $data = [
            'title' => 'Analytics Settings',
            'settings' => $analyticsSettings
        ];

        return view('admin/settings/analytics', $data);
    }

    public function updateAnalyticsSettings()
    {
        $settings = [
            'google_analytics_id'   => $this->request->getPost('google_analytics_id'),
            'google_tag_manager_id' => $this->request->getPost('google_tag_manager_id'),
            'facebook_pixel_id'     => $this->request->getPost('facebook_pixel_id'),
        ];

        try {
            if ($this->settingModel->updateSettings($settings)) {
                return redirect()->to('/admin/settings/analytics')->with('success', 'Analytics settings updated successfully');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to update analytics settings');
            }
        } catch (\Exception $e) {
            log_message('error', 'Analytics settings update error: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while updating analytics settings');
        }
    }

    public function seoSettings()
    {
        $seoSettings = $this->settingModel->getSettingsByCategory('seo');

        $data = [
            'title' => 'SEO Settings',
            'settings' => $seoSettings
        ];

        return view('admin/settings/seo', $data);
    }

    public function updateSeoSettings()
    {
        $settings = [
            'meta_description' => $this->request->getPost('meta_description'),
            'meta_keywords'    => $this->request->getPost('meta_keywords'),
        ];

        try {
            if ($this->settingModel->updateSettings($settings)) {
                return redirect()->to('/admin/settings/seo')->with('success', 'SEO settings updated successfully');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to update SEO settings');
            }
        } catch (\Exception $e) {
            log_message('error', 'SEO settings update error: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while updating SEO settings');
        }
    }
}
