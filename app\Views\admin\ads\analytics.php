<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-line me-2"></i>Advertisement Analytics</h2>
                <a href="<?= base_url('admin/ads') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Ads
                </a>
            </div>

            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= number_format($totalAds) ?></h4>
                                    <p class="mb-0">Total Ads</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-ad fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= number_format($totalImpressions) ?></h4>
                                    <p class="mb-0">Total Impressions</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-eye fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= number_format($totalClicks) ?></h4>
                                    <p class="mb-0">Total Clicks</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-mouse-pointer fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?= $averageCTR ?>%</h4>
                                    <p class="mb-0">Average CTR</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-percentage fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance by Position -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-map-marker-alt me-2"></i>Performance by Position
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="positionChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-pie me-2"></i>Ad Type Distribution
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="typeChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Performing Ads -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-trophy me-2"></i>Top Performing Ads
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Ad Title</th>
                                    <th>Position</th>
                                    <th>Type</th>
                                    <th>Impressions</th>
                                    <th>Clicks</th>
                                    <th>CTR</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($topAds as $ad): ?>
                                    <?php 
                                    $ctr = $ad['impressions'] > 0 ? ($ad['clicks'] / $ad['impressions']) * 100 : 0;
                                    ?>
                                    <tr>
                                        <td>
                                            <strong><?= esc($ad['title']) ?></strong>
                                            <?php if ($ad['description']): ?>
                                                <br><small class="text-muted"><?= esc(substr($ad['description'], 0, 50)) ?>...</small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?= ucfirst(str_replace('_', ' ', $ad['position'])) ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $ad['type'] === 'banner' ? 'primary' : ($ad['type'] === 'text' ? 'success' : 'info') ?>">
                                                <?= ucfirst($ad['type']) ?>
                                            </span>
                                        </td>
                                        <td><?= number_format($ad['impressions']) ?></td>
                                        <td><?= number_format($ad['clicks']) ?></td>
                                        <td>
                                            <span class="badge bg-<?= $ctr > 2 ? 'success' : ($ctr > 1 ? 'warning' : 'danger') ?>">
                                                <?= number_format($ctr, 2) ?>%
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $ad['status'] === 'active' ? 'success' : 'secondary' ?>">
                                                <?= ucfirst($ad['status']) ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Position Performance Chart
const positionCtx = document.getElementById('positionChart').getContext('2d');
const positionChart = new Chart(positionCtx, {
    type: 'bar',
    data: {
        labels: <?= json_encode(array_keys($positionStats)) ?>,
        datasets: [{
            label: 'Impressions',
            data: <?= json_encode(array_column($positionStats, 'impressions')) ?>,
            backgroundColor: 'rgba(54, 162, 235, 0.8)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }, {
            label: 'Clicks',
            data: <?= json_encode(array_column($positionStats, 'clicks')) ?>,
            backgroundColor: 'rgba(255, 99, 132, 0.8)',
            borderColor: 'rgba(255, 99, 132, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Ad Type Chart
const typeCtx = document.getElementById('typeChart').getContext('2d');
const typeChart = new Chart(typeCtx, {
    type: 'doughnut',
    data: {
        labels: <?= json_encode(array_keys($typeStats)) ?>,
        datasets: [{
            data: <?= json_encode(array_values($typeStats)) ?>,
            backgroundColor: [
                'rgba(255, 99, 132, 0.8)',
                'rgba(54, 162, 235, 0.8)',
                'rgba(255, 205, 86, 0.8)',
                'rgba(75, 192, 192, 0.8)'
            ],
            borderColor: [
                'rgba(255, 99, 132, 1)',
                'rgba(54, 162, 235, 1)',
                'rgba(255, 205, 86, 1)',
                'rgba(75, 192, 192, 1)'
            ],
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
<?= $this->endSection() ?>
