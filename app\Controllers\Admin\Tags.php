<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\Tag;

class Tags extends BaseController
{
    protected $tagModel;

    public function __construct()
    {
        $this->tagModel = new Tag();
    }

    public function index()
    {
        $data = [
            'title' => 'Tag Management',
            'tags' => $this->tagModel->findAll()
        ];

        return view('admin/tags/index', $data);
    }

    public function create()
    {
        $data = [
            'title' => 'Add New Tag'
        ];

        return view('admin/tags/create', $data);
    }

    public function store()
    {
        $rules = [
            'name' => 'required|min_length[2]|max_length[255]',
            'slug' => 'required|min_length[2]|max_length[255]|is_unique[tags.slug]',
            'status' => 'required|in_list[active,inactive]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name'   => $this->request->getPost('name'),
            'slug'   => $this->request->getPost('slug'),
            'status' => $this->request->getPost('status'),
        ];

        if ($this->tagModel->save($data)) {
            return redirect()->to('/admin/tags')->with('success', 'Tag created successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create tag');
        }
    }

    public function edit($id)
    {
        $tag = $this->tagModel->find($id);

        if (!$tag) {
            return redirect()->to('/admin/tags')->with('error', 'Tag not found');
        }

        $data = [
            'title' => 'Edit Tag',
            'tag' => $tag
        ];

        return view('admin/tags/edit', $data);
    }

    public function update($id)
    {
        $tag = $this->tagModel->find($id);

        if (!$tag) {
            return redirect()->to('/admin/tags')->with('error', 'Tag not found');
        }

        $rules = [
            'name' => 'required|min_length[2]|max_length[255]',
            'slug' => "required|min_length[2]|max_length[255]|is_unique[tags.slug,id,$id]",
            'status' => 'required|in_list[active,inactive]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name'   => $this->request->getPost('name'),
            'slug'   => $this->request->getPost('slug'),
            'status' => $this->request->getPost('status'),
        ];

        if ($this->tagModel->update($id, $data)) {
            return redirect()->to('/admin/tags')->with('success', 'Tag updated successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update tag');
        }
    }

    public function delete($id)
    {
        $tag = $this->tagModel->find($id);

        if (!$tag) {
            return redirect()->to('/admin/tags')->with('error', 'Tag not found');
        }

        // Check if tag is used in news articles
        $db = \Config\Database::connect();
        $newsTagsCount = $db->table('news_tags')->where('tag_id', $id)->countAllResults();

        if ($newsTagsCount > 0) {
            return redirect()->to('/admin/tags')->with('error', 'Cannot delete tag. It is used in ' . $newsTagsCount . ' news articles.');
        }

        if ($this->tagModel->delete($id)) {
            return redirect()->to('/admin/tags')->with('success', 'Tag deleted successfully');
        } else {
            return redirect()->to('/admin/tags')->with('error', 'Failed to delete tag');
        }
    }
}
