<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-edit me-2"></i>Edit Epaper</h2>
                <a href="<?= base_url('admin/epapers') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Epapers
                </a>
            </div>

            <!-- Error Messages -->
            <?php if (session()->getFlashdata('errors')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <h6>Please fix the following errors:</h6>
                    <ul class="mb-0">
                        <?php foreach (session()->getFlashdata('errors') as $error): ?>
                            <li><?= esc($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Edit Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-newspaper me-2"></i>Epaper Details
                    </h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('admin/epapers/update/' . $epaper['id']) ?>" method="post" enctype="multipart/form-data">
                        <?= csrf_field() ?>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <!-- Title -->
                                <div class="mb-3">
                                    <label for="title" class="form-label">
                                        <i class="fas fa-heading me-2"></i>Epaper Title *
                                    </label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="<?= old('title', $epaper['title']) ?>" required maxlength="500"
                                           placeholder="Enter epaper title">
                                    <div class="form-text">Maximum 500 characters</div>
                                </div>

                                <!-- Description -->
                                <div class="mb-3">
                                    <label for="description" class="form-label">
                                        <i class="fas fa-align-left me-2"></i>Description
                                    </label>
                                    <textarea class="form-control" id="description" name="description" rows="3"
                                              placeholder="Optional description about this epaper edition"><?= old('description', $epaper['description']) ?></textarea>
                                    <div class="form-text">Optional field for additional information</div>
                                </div>

                                <!-- Current PDF File -->
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-file-pdf me-2"></i>Current PDF File
                                    </label>
                                    <div class="card bg-light">
                                        <div class="card-body p-3">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-file-pdf fa-2x text-danger me-3"></i>
                                                <div>
                                                    <strong><?= esc($epaper['pdf_file']) ?></strong>
                                                    <br><small class="text-muted">
                                                        Size: <?= formatBytes($epaper['file_size']) ?>
                                                        <?php if ($epaper['page_count']): ?>
                                                            | Pages: <?= $epaper['page_count'] ?>
                                                        <?php endif; ?>
                                                    </small>
                                                </div>
                                                <div class="ms-auto">
                                                    <a href="<?= base_url('uploads/epapers/' . $epaper['pdf_file']) ?>" 
                                                       class="btn btn-sm btn-outline-primary" target="_blank">
                                                        <i class="fas fa-eye me-1"></i>View
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- New PDF File Upload -->
                                <div class="mb-3">
                                    <label for="pdf_file" class="form-label">
                                        <i class="fas fa-upload me-2"></i>Replace PDF File
                                    </label>
                                    <input type="file" class="form-control" id="pdf_file" name="pdf_file" 
                                           accept=".pdf">
                                    <div class="form-text">
                                        <strong>Optional:</strong> Upload a new PDF to replace the current one. PDF format only, Maximum size: 50MB
                                    </div>
                                </div>

                                <!-- Current Thumbnail -->
                                <?php if ($epaper['thumbnail']): ?>
                                    <div class="mb-3">
                                        <label class="form-label">
                                            <i class="fas fa-image me-2"></i>Current Thumbnail
                                        </label>
                                        <div class="card bg-light">
                                            <div class="card-body p-3">
                                                <div class="d-flex align-items-center">
                                                    <img src="<?= base_url('uploads/epapers/thumbnails/' . $epaper['thumbnail']) ?>" 
                                                         alt="Thumbnail" class="img-thumbnail me-3" style="max-width: 100px; max-height: 100px;">
                                                    <div>
                                                        <strong><?= esc($epaper['thumbnail']) ?></strong>
                                                        <br><small class="text-muted">Current thumbnail image</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- New Thumbnail Upload -->
                                <div class="mb-3">
                                    <label for="thumbnail" class="form-label">
                                        <i class="fas fa-image me-2"></i><?= $epaper['thumbnail'] ? 'Replace Thumbnail' : 'Add Thumbnail' ?>
                                    </label>
                                    <input type="file" class="form-control" id="thumbnail" name="thumbnail" 
                                           accept="image/*">
                                    <div class="form-text">
                                        Optional: <?= $epaper['thumbnail'] ? 'Upload a new thumbnail to replace the current one' : 'Upload a thumbnail image for the epaper' ?> (JPG, PNG, GIF)
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- Publication Date -->
                                <div class="mb-3">
                                    <label for="publication_date" class="form-label">
                                        <i class="fas fa-calendar-alt me-2"></i>Publication Date *
                                    </label>
                                    <input type="date" class="form-control" id="publication_date" name="publication_date" 
                                           value="<?= old('publication_date', $epaper['publication_date']) ?>" required>
                                </div>

                                <!-- Edition -->
                                <div class="mb-3">
                                    <label for="edition" class="form-label">
                                        <i class="fas fa-bookmark me-2"></i>Edition *
                                    </label>
                                    <input type="text" class="form-control" id="edition" name="edition" 
                                           value="<?= old('edition', $epaper['edition']) ?>" required maxlength="100"
                                           placeholder="e.g., Main Edition, Evening Edition">
                                    <div class="form-text">Maximum 100 characters</div>
                                </div>

                                <!-- Language -->
                                <div class="mb-3">
                                    <label for="language" class="form-label">
                                        <i class="fas fa-language me-2"></i>Language *
                                    </label>
                                    <select class="form-select" id="language" name="language" required>
                                        <option value="">Select Language</option>
                                        <option value="Hindi" <?= old('language', $epaper['language']) == 'Hindi' ? 'selected' : '' ?>>Hindi</option>
                                        <option value="English" <?= old('language', $epaper['language']) == 'English' ? 'selected' : '' ?>>English</option>
                                        <option value="Bilingual" <?= old('language', $epaper['language']) == 'Bilingual' ? 'selected' : '' ?>>Bilingual</option>
                                    </select>
                                </div>

                                <!-- Page Count -->
                                <div class="mb-3">
                                    <label for="page_count" class="form-label">
                                        <i class="fas fa-file-alt me-2"></i>Page Count
                                    </label>
                                    <input type="number" class="form-control" id="page_count" name="page_count" 
                                           value="<?= old('page_count', $epaper['page_count']) ?>" min="1" max="999"
                                           placeholder="Number of pages">
                                    <div class="form-text">Optional: Total number of pages in the PDF</div>
                                </div>

                                <!-- Status -->
                                <div class="mb-3">
                                    <label for="status" class="form-label">
                                        <i class="fas fa-toggle-on me-2"></i>Status *
                                    </label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="">Select Status</option>
                                        <option value="active" <?= old('status', $epaper['status']) == 'active' ? 'selected' : '' ?>>Active</option>
                                        <option value="inactive" <?= old('status', $epaper['status']) == 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                        <option value="archived" <?= old('status', $epaper['status']) == 'archived' ? 'selected' : '' ?>>Archived</option>
                                    </select>
                                </div>

                                <!-- Featured -->
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="featured" name="featured" value="1"
                                               <?= old('featured', $epaper['featured']) ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="featured">
                                            <i class="fas fa-star me-2"></i>Mark as Featured
                                        </label>
                                    </div>
                                    <div class="form-text">Featured epapers will be highlighted on the website</div>
                                </div>

                                <!-- Statistics -->
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-chart-bar me-2"></i>Statistics
                                    </label>
                                    <div class="card bg-light">
                                        <div class="card-body p-3">
                                            <div class="row text-center">
                                                <div class="col-6">
                                                    <div class="h5 mb-0 text-primary"><?= number_format($epaper['view_count']) ?></div>
                                                    <small class="text-muted">Views</small>
                                                </div>
                                                <div class="col-6">
                                                    <div class="h5 mb-0 text-success"><?= number_format($epaper['download_count']) ?></div>
                                                    <small class="text-muted">Downloads</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Epaper Info -->
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-info-circle me-2"></i>Epaper Info
                                    </label>
                                    <div class="card bg-light">
                                        <div class="card-body p-2">
                                            <small>
                                                <strong>ID:</strong> <?= $epaper['id'] ?><br>
                                                <strong>Uploaded:</strong> <?= date('d M Y, H:i', strtotime($epaper['created_at'])) ?><br>
                                                <strong>Updated:</strong> <?= date('d M Y, H:i', strtotime($epaper['updated_at'])) ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <a href="<?= base_url('admin/epapers') ?>" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Update Epaper
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // File size validation
    const pdfInput = document.getElementById('pdf_file');
    const thumbnailInput = document.getElementById('thumbnail');
    
    pdfInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const maxSize = 50 * 1024 * 1024; // 50MB in bytes
            if (file.size > maxSize) {
                alert('PDF file size must be less than 50MB. Current size: ' + (file.size / (1024 * 1024)).toFixed(2) + 'MB');
                this.value = '';
                return;
            }
            
            if (file.type !== 'application/pdf') {
                alert('Please select a valid PDF file.');
                this.value = '';
                return;
            }
        }
    });
    
    thumbnailInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const maxSize = 5 * 1024 * 1024; // 5MB for images
            if (file.size > maxSize) {
                alert('Thumbnail image size must be less than 5MB. Current size: ' + (file.size / (1024 * 1024)).toFixed(2) + 'MB');
                this.value = '';
                return;
            }
            
            if (!file.type.startsWith('image/')) {
                alert('Please select a valid image file.');
                this.value = '';
                return;
            }
        }
    });
});
</script>
<?= $this->endSection() ?>
