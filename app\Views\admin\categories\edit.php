<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">Edit Category: <?= esc($category['name']) ?></h4>
    <a href="<?= base_url('admin/categories') ?>" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to Categories
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <?php if (session()->getFlashdata('errors')): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <strong>Please fix the following errors:</strong>
                        <ul class="mb-0 mt-2">
                            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                <li><?= $error ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <form action="<?= base_url('admin/categories/update/' . $category['id']) ?>" method="post">
                    <?= csrf_field() ?>
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">
                            <i class="fas fa-tag me-2"></i>Category Name (श्रेणी नाम) <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="name" name="name" 
                               value="<?= old('name', $category['name']) ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="slug" class="form-label">
                            <i class="fas fa-link me-2"></i>Slug <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="slug" name="slug" 
                               value="<?= old('slug', $category['slug']) ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">
                            <i class="fas fa-align-left me-2"></i>Description
                        </label>
                        <textarea class="form-control" id="description" name="description" rows="4"><?= old('description', $category['description']) ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="status" class="form-label">
                            <i class="fas fa-toggle-on me-2"></i>Status <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="active" <?= old('status', $category['status']) === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="inactive" <?= old('status', $category['status']) === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                        </select>
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <a href="<?= base_url('admin/categories') ?>" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Category
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Category Information</h6>
            </div>
            <div class="card-body">
                <p><strong>Category ID:</strong> <?= $category['id'] ?></p>
                <p><strong>Created:</strong> <?= date('M d, Y H:i', strtotime($category['created_at'])) ?></p>
                <p><strong>Last Updated:</strong> <?= date('M d, Y H:i', strtotime($category['updated_at'])) ?></p>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
