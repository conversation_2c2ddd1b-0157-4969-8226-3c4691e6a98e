{"url": "http://localhost/bbc_news/public/index.php/admin/news/store", "method": "POST", "isAJAX": false, "startTime": **********.912636, "totalTime": 110.9, "totalMemory": "7.659", "segmentDuration": 20, "segmentCount": 6, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.915906, "duration": 0.*****************}, {"name": "Required Before Filters", "component": "Timer", "start": **********.935507, "duration": 0.0025339126586914062}, {"name": "Routing", "component": "Timer", "start": **********.938046, "duration": 0.0006949901580810547}, {"name": "Before Filters", "component": "Timer", "start": **********.938939, "duration": 0.*****************}, {"name": "Controller", "component": "Timer", "start": **********.951964, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.951966, "duration": 0.*****************}, {"name": "After Filters", "component": "Timer", "start": **********.0226, "duration": 8.106231689453125e-06}, {"name": "Required After Filters", "component": "Timer", "start": **********.022619, "duration": 0.***************}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(4 total Queries, 4 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.17 ms", "sql": "<strong>SELECT</strong> 1\n<strong>FROM</strong> `news`\n<strong>WHERE</strong> `slug` = &#039;पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे&#039;\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Validation\\Rules.php:175", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\Validation\\StrictRules\\Rules.php:187", "function": "        CodeIgniter\\Validation\\Rules->is_unique()", "index": "  3    "}, {"file": "SYSTEMPATH\\Validation\\Validation.php:336", "function": "        CodeIgniter\\Validation\\StrictRules\\Rules->is_unique()", "index": "  4    "}, {"file": "SYSTEMPATH\\Validation\\Validation.php:201", "function": "        CodeIgniter\\Validation\\Validation->processRules()", "index": "  5    "}, {"file": "SYSTEMPATH\\BaseModel.php:1580", "function": "        CodeIgniter\\Validation\\Validation->run()", "index": "  6    "}, {"file": "SYSTEMPATH\\BaseModel.php:812", "function": "        CodeIgniter\\BaseModel->validate()", "index": "  7    "}, {"file": "SYSTEMPATH\\Model.php:800", "function": "        CodeIgniter\\BaseModel->insert()", "index": "  8    "}, {"file": "SYSTEMPATH\\BaseModel.php:754", "function": "        CodeIgniter\\Model->insert()", "index": "  9    "}, {"file": "APPPATH\\Controllers\\Admin\\News.php:77", "function": "        CodeIgniter\\BaseModel->save()", "index": " 10    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Admin\\News->store()", "index": " 11    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": " 12    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 13    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 14    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 15    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 16    "}], "trace-file": "APPPATH\\Controllers\\Admin\\News.php:77", "qid": "9c1aed9229ec2ae208a626b3083e0c55"}, {"hover": "", "class": "", "duration": "0.1 ms", "sql": "<strong>SELECT</strong> 1\n<strong>FROM</strong> `categories`\n<strong>WHERE</strong> `id` = &#039;1&#039;\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Validation\\Rules.php:132", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\Validation\\StrictRules\\Rules.php:148", "function": "        CodeIgniter\\Validation\\Rules->is_not_unique()", "index": "  3    "}, {"file": "SYSTEMPATH\\Validation\\Validation.php:336", "function": "        CodeIgniter\\Validation\\StrictRules\\Rules->is_not_unique()", "index": "  4    "}, {"file": "SYSTEMPATH\\Validation\\Validation.php:201", "function": "        CodeIgniter\\Validation\\Validation->processRules()", "index": "  5    "}, {"file": "SYSTEMPATH\\BaseModel.php:1580", "function": "        CodeIgniter\\Validation\\Validation->run()", "index": "  6    "}, {"file": "SYSTEMPATH\\BaseModel.php:812", "function": "        CodeIgniter\\BaseModel->validate()", "index": "  7    "}, {"file": "SYSTEMPATH\\Model.php:800", "function": "        CodeIgniter\\BaseModel->insert()", "index": "  8    "}, {"file": "SYSTEMPATH\\BaseModel.php:754", "function": "        CodeIgniter\\Model->insert()", "index": "  9    "}, {"file": "APPPATH\\Controllers\\Admin\\News.php:77", "function": "        CodeIgniter\\BaseModel->save()", "index": " 10    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Admin\\News->store()", "index": " 11    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": " 12    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 13    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 14    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 15    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 16    "}], "trace-file": "APPPATH\\Controllers\\Admin\\News.php:77", "qid": "2e45545dc18e32284dbe8e85879d1cc4"}, {"hover": "", "class": "", "duration": "0.08 ms", "sql": "<strong>SELECT</strong> 1\n<strong>FROM</strong> `users`\n<strong>WHERE</strong> `id` = &#039;1&#039;\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Validation\\Rules.php:132", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\Validation\\StrictRules\\Rules.php:148", "function": "        CodeIgniter\\Validation\\Rules->is_not_unique()", "index": "  3    "}, {"file": "SYSTEMPATH\\Validation\\Validation.php:336", "function": "        CodeIgniter\\Validation\\StrictRules\\Rules->is_not_unique()", "index": "  4    "}, {"file": "SYSTEMPATH\\Validation\\Validation.php:201", "function": "        CodeIgniter\\Validation\\Validation->processRules()", "index": "  5    "}, {"file": "SYSTEMPATH\\BaseModel.php:1580", "function": "        CodeIgniter\\Validation\\Validation->run()", "index": "  6    "}, {"file": "SYSTEMPATH\\BaseModel.php:812", "function": "        CodeIgniter\\BaseModel->validate()", "index": "  7    "}, {"file": "SYSTEMPATH\\Model.php:800", "function": "        CodeIgniter\\BaseModel->insert()", "index": "  8    "}, {"file": "SYSTEMPATH\\BaseModel.php:754", "function": "        CodeIgniter\\Model->insert()", "index": "  9    "}, {"file": "APPPATH\\Controllers\\Admin\\News.php:77", "function": "        CodeIgniter\\BaseModel->save()", "index": " 10    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Admin\\News->store()", "index": " 11    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": " 12    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 13    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 14    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 15    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 16    "}], "trace-file": "APPPATH\\Controllers\\Admin\\News.php:77", "qid": "a6278ec2d048e5b790321fabadb7198b"}, {"hover": "", "class": "", "duration": "7.19 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> `news` (`title`, `slug`, `description`, `content`, `image`, `category_id`, `author_id`, `status`, `featured`, `published_at`, `created_at`, `updated_at`) <strong>VALUES</strong> (&#039;पीएम मोदी 12 जून को टीडीपी प्रमुख चंद्रबाबू नायडू के शपथ ग्रहण समारोह में शामिल होंगे&#039;, &#039;पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे&#039;, &#039;मनोनीत प्रधानमंत्री नरेंद्र मोदी अपने गठबंधन के साथी और टीडीपी प्रमुख के शपथ ग्रहण समारोह में शामिल हो सकते हैं। टीडीपी प्रमुख चंद्रबाबू नायडू आंध्र प्रदेश के मुख्यमंत्री के रूप में 12 जून को शपथ लेंगे। बता दें, मनोनीत प्रधानमंत्री मोदी आज राष्ट्रपति भवन में तीसरी बार प्रधानमंत्री के रूप में शपथ ग्रहण करेंगे।&#039;, &#039;मनोनीत प्रधानमंत्री नरेंद्र मोदी अपने गठबंधन के साथी और टीडीपी प्रमुख के शपथ ग्रहण समारोह में शामिल हो सकते हैं। टीडीपी प्रमुख चंद्रबाबू नायडू आंध्र प्रदेश के मुख्यमंत्री के रूप में 12 जून को शपथ लेंगे। बता दें, मनोनीत प्रधानमंत्री मोदी आज राष्ट्रपति भवन में तीसरी बार प्रधानमंत्री के रूप में शपथ ग्रहण करेंगे।\\r\\n\\r\\n \\r\\n\\r\\n\\r\\nमुख्य सचिव नीरभ कुमार प्रसाद ने शनिवार को बताया कि शपथ ग्रहण समारोह में पीएम मोदी, राज्यपाल एस अब्दुल नजीर और अन्य गणमान्य शामिल हो सकते हैं। नायडू बुधवार को सुबह 11:27 बजे गन्नवरम हवाई अड्डे के पास केसरपल्ली आईटी पार्क में शपथ लेंगे। मुख्य सचिव ने समारोह के लिए की जा रही तैयारियों की समीक्षा की और अधिकारियों को पुख्ता तैयारियां करने का निर्देश दिया। गन्नवरम हवाई अड्डे पर कई वीवीआईपी के पहुंचने की उम्मीद है। शपथ ग्रहण समारोह के लिए वरिष्ठ आईएएस अधिकारी पीएस पद्युम्न को राज्य समन्वयक नियुक्त किया गया है।\\r\\n\\r\\nकेंद्र में भी मजबूत स्तंभ बनकर उभरे\\r\\n\\r\\n \\r\\n\\r\\nगौरतलब है कि नायडू परिवार और चंद्रबाबू नायडू की पत्नी की संपत्ति में एक तरफ ये इजाफा हो रहा है। वहीं दूसरी ओर 12 जून को चंद्रबाबू नायडू आंध्र प्रदेश के नए सीएम बनेंगे। आंध्र प्रदेश विधानसभा चुनाव परिणाम में टीडीपी ने सबसे अधिक सीटें जीती हैं। 135 सीटें जीत कर टीडीपी ने बहुमत के आंकड़े को पार कर लिया।\\r\\n\\r\\nकिंग मेकर की भूमिका में हैं चंद्रबाबू नायडू\\r\\n\\r\\nचंद्रबाबू नायडू एनडीए गठबंधन के लिए एक किंगमेकर्स में से एक बनकर सामने आए हैं। एनडीए ने लगातार तीसरी बार बहुमत पाने का रिकॉर्ड बनया है। चंद्रबाबू नायडू और नीतीश कुमार की पार्टी का गठबंधन की सरकार बनाने में अहम योगदान रहने वाला है। भाजपा इस बार लोकसभा चुनाव में अकेले दम पर बहुमत लाने में सफल नहीं हो सकी है। वहीं कांग्रेस के नेतृत्व वाले इंडिया गठबंधन ने इस बार चुनाव में बेहतरीन प्रदर्शन किया है।&#039;, &#039;**********_61bb0e8fbeab52801378.jpeg&#039;, &#039;1&#039;, &#039;1&#039;, &#039;published&#039;, 0, &#039;2025-06-24 08:25:23&#039;, &#039;2025-06-24 08:25:23&#039;, &#039;2025-06-24 08:25:23&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2345", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:394", "function": "        CodeIgniter\\Database\\BaseBuilder->insert()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:843", "function": "        CodeIgniter\\Model->doInsert()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:800", "function": "        CodeIgniter\\BaseModel->insert()", "index": "  4    "}, {"file": "SYSTEMPATH\\BaseModel.php:754", "function": "        CodeIgniter\\Model->insert()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Admin\\News.php:77", "function": "        CodeIgniter\\BaseModel->save()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Admin\\News->store()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Controllers\\Admin\\News.php:77", "qid": "ed05815f72bc0fe430197fb09f0f3845"}]}, "badgeValue": 4, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.011564, "duration": "0.000741"}, {"name": "Query", "component": "Database", "start": **********.012818, "duration": "0.000170", "query": "<strong>SELECT</strong> 1\n<strong>FROM</strong> `news`\n<strong>WHERE</strong> `slug` = &#039;पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे&#039;\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.013918, "duration": "0.000103", "query": "<strong>SELECT</strong> 1\n<strong>FROM</strong> `categories`\n<strong>WHERE</strong> `id` = &#039;1&#039;\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.014122, "duration": "0.000080", "query": "<strong>SELECT</strong> 1\n<strong>FROM</strong> `users`\n<strong>WHERE</strong> `id` = &#039;1&#039;\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.014366, "duration": "0.007195", "query": "<strong>INSERT</strong> <strong>INTO</strong> `news` (`title`, `slug`, `description`, `content`, `image`, `category_id`, `author_id`, `status`, `featured`, `published_at`, `created_at`, `updated_at`) <strong>VALUES</strong> (&#039;पीएम मोदी 12 जून को टीडीपी प्रमुख चंद्रबाबू नायडू के शपथ ग्रहण समारोह में शामिल होंगे&#039;, &#039;पीएम-मोदी-12-जून-को-टीडीपी-प्रमुख-चंद्रबाबू-नायडू-के-शपथ-ग्रहण-समारोह-में-शामिल-होंगे&#039;, &#039;मनोनीत प्रधानमंत्री नरेंद्र मोदी अपने गठबंधन के साथी और टीडीपी प्रमुख के शपथ ग्रहण समारोह में शामिल हो सकते हैं। टीडीपी प्रमुख चंद्रबाबू नायडू आंध्र प्रदेश के मुख्यमंत्री के रूप में 12 जून को शपथ लेंगे। बता दें, मनोनीत प्रधानमंत्री मोदी आज राष्ट्रपति भवन में तीसरी बार प्रधानमंत्री के रूप में शपथ ग्रहण करेंगे।&#039;, &#039;मनोनीत प्रधानमंत्री नरेंद्र मोदी अपने गठबंधन के साथी और टीडीपी प्रमुख के शपथ ग्रहण समारोह में शामिल हो सकते हैं। टीडीपी प्रमुख चंद्रबाबू नायडू आंध्र प्रदेश के मुख्यमंत्री के रूप में 12 जून को शपथ लेंगे। बता दें, मनोनीत प्रधानमंत्री मोदी आज राष्ट्रपति भवन में तीसरी बार प्रधानमंत्री के रूप में शपथ ग्रहण करेंगे।\\r\\n\\r\\n \\r\\n\\r\\n\\r\\nमुख्य सचिव नीरभ कुमार प्रसाद ने शनिवार को बताया कि शपथ ग्रहण समारोह में पीएम मोदी, राज्यपाल एस अब्दुल नजीर और अन्य गणमान्य शामिल हो सकते हैं। नायडू बुधवार को सुबह 11:27 बजे गन्नवरम हवाई अड्डे के पास केसरपल्ली आईटी पार्क में शपथ लेंगे। मुख्य सचिव ने समारोह के लिए की जा रही तैयारियों की समीक्षा की और अधिकारियों को पुख्ता तैयारियां करने का निर्देश दिया। गन्नवरम हवाई अड्डे पर कई वीवीआईपी के पहुंचने की उम्मीद है। शपथ ग्रहण समारोह के लिए वरिष्ठ आईएएस अधिकारी पीएस पद्युम्न को राज्य समन्वयक नियुक्त किया गया है।\\r\\n\\r\\nकेंद्र में भी मजबूत स्तंभ बनकर उभरे\\r\\n\\r\\n \\r\\n\\r\\nगौरतलब है कि नायडू परिवार और चंद्रबाबू नायडू की पत्नी की संपत्ति में एक तरफ ये इजाफा हो रहा है। वहीं दूसरी ओर 12 जून को चंद्रबाबू नायडू आंध्र प्रदेश के नए सीएम बनेंगे। आंध्र प्रदेश विधानसभा चुनाव परिणाम में टीडीपी ने सबसे अधिक सीटें जीती हैं। 135 सीटें जीत कर टीडीपी ने बहुमत के आंकड़े को पार कर लिया।\\r\\n\\r\\nकिंग मेकर की भूमिका में हैं चंद्रबाबू नायडू\\r\\n\\r\\nचंद्रबाबू नायडू एनडीए गठबंधन के लिए एक किंगमेकर्स में से एक बनकर सामने आए हैं। एनडीए ने लगातार तीसरी बार बहुमत पाने का रिकॉर्ड बनया है। चंद्रबाबू नायडू और नीतीश कुमार की पार्टी का गठबंधन की सरकार बनाने में अहम योगदान रहने वाला है। भाजपा इस बार लोकसभा चुनाव में अकेले दम पर बहुमत लाने में सफल नहीं हो सकी है। वहीं कांग्रेस के नेतृत्व वाले इंडिया गठबंधन ने इस बार चुनाव में बेहतरीन प्रदर्शन किया है।&#039;, &#039;**********_61bb0e8fbeab52801378.jpeg&#039;, &#039;1&#039;, &#039;1&#039;, &#039;published&#039;, 0, &#039;2025-06-24 08:25:23&#039;, &#039;2025-06-24 08:25:23&#039;, &#039;2025-06-24 08:25:23&#039;)"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "debug", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 0, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": []}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 179 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH\\Database\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH\\Database\\Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Files\\File.php", "name": "File.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Files\\FileCollection.php", "name": "FileCollection.php"}, {"path": "SYSTEMPATH\\HTTP\\Files\\UploadedFile.php", "name": "UploadedFile.php"}, {"path": "SYSTEMPATH\\HTTP\\Files\\UploadedFileInterface.php", "name": "UploadedFileInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RedirectResponse.php", "name": "RedirectResponse.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\Array\\ArrayHelper.php", "name": "ArrayHelper.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\I18n\\Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Session\\Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH\\Session\\SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH\\Validation\\CreditCardRules.php", "name": "CreditCardRules.php"}, {"path": "SYSTEMPATH\\Validation\\DotArrayFilter.php", "name": "DotArrayFilter.php"}, {"path": "SYSTEMPATH\\Validation\\FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH\\Validation\\Rules.php", "name": "Rules.php"}, {"path": "SYSTEMPATH\\Validation\\StrictRules\\CreditCardRules.php", "name": "CreditCardRules.php"}, {"path": "SYSTEMPATH\\Validation\\StrictRules\\FileRules.php", "name": "FileRules.php"}, {"path": "SYSTEMPATH\\Validation\\StrictRules\\FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH\\Validation\\StrictRules\\Rules.php", "name": "Rules.php"}, {"path": "SYSTEMPATH\\Validation\\Validation.php", "name": "Validation.php"}, {"path": "SYSTEMPATH\\Validation\\ValidationInterface.php", "name": "ValidationInterface.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Mimes.php", "name": "Mimes.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Session.php", "name": "Session.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\Validation.php", "name": "Validation.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\Admin\\News.php", "name": "News.php"}, {"path": "APPPATH\\Controllers\\BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH\\Filters\\AuthFilter.php", "name": "AuthFilter.php"}, {"path": "APPPATH\\Helpers\\hindi_helper.php", "name": "hindi_helper.php"}, {"path": "APPPATH\\Models\\Category.php", "name": "Category.php"}, {"path": "APPPATH\\Models\\News.php", "name": "News.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}, {"path": "VENDORPATH\\autoload.php", "name": "autoload.php"}, {"path": "VENDORPATH\\composer\\ClassLoader.php", "name": "ClassLoader.php"}, {"path": "VENDORPATH\\composer\\InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "VENDORPATH\\composer\\autoload_real.php", "name": "autoload_real.php"}, {"path": "VENDORPATH\\composer\\autoload_static.php", "name": "autoload_static.php"}, {"path": "VENDORPATH\\composer\\installed.php", "name": "installed.php"}, {"path": "VENDORPATH\\composer\\platform_check.php", "name": "platform_check.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\Escaper.php", "name": "Escaper.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "VENDORPATH\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php", "name": "deep_copy.php"}, {"path": "VENDORPATH\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php", "name": "Functions.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LogLevel.php", "name": "LogLevel.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "VENDORPATH\\symfony\\deprecation-contracts\\function.php", "name": "function.php"}]}, "badgeValue": 179, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Admin\\News", "method": "store", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "news/([^/]+)", "handler": "\\App\\Controllers\\Home::news/$1"}, {"method": "GET", "route": "category/([^/]+)", "handler": "\\App\\Controllers\\Home::category/$1"}, {"method": "GET", "route": "admin/login", "handler": "\\App\\Controllers\\Auth::login"}, {"method": "GET", "route": "admin/logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "admin/dashboard", "handler": "\\App\\Controllers\\Admin\\Dashboard::index"}, {"method": "GET", "route": "admin/users", "handler": "\\App\\Controllers\\Admin\\Users::index"}, {"method": "GET", "route": "admin/users/create", "handler": "\\App\\Controllers\\Admin\\Users::create"}, {"method": "GET", "route": "admin/users/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Users::edit/$1"}, {"method": "GET", "route": "admin/users/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Users::delete/$1"}, {"method": "GET", "route": "admin/news", "handler": "\\App\\Controllers\\Admin\\News::index"}, {"method": "GET", "route": "admin/news/create", "handler": "\\App\\Controllers\\Admin\\News::create"}, {"method": "GET", "route": "admin/news/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\News::edit/$1"}, {"method": "GET", "route": "admin/news/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\News::delete/$1"}, {"method": "GET", "route": "admin/categories", "handler": "\\App\\Controllers\\Admin\\Categories::index"}, {"method": "GET", "route": "admin/categories/create", "handler": "\\App\\Controllers\\Admin\\Categories::create"}, {"method": "GET", "route": "admin/categories/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Categories::edit/$1"}, {"method": "GET", "route": "admin/categories/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Categories::delete/$1"}, {"method": "GET", "route": "admin/tags", "handler": "\\App\\Controllers\\Admin\\Tags::index"}, {"method": "GET", "route": "admin/tags/create", "handler": "\\App\\Controllers\\Admin\\Tags::create"}, {"method": "GET", "route": "admin/tags/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Tags::edit/$1"}, {"method": "GET", "route": "admin/tags/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Tags::delete/$1"}, {"method": "GET", "route": "admin/ads", "handler": "\\App\\Controllers\\Admin\\Ads::index"}, {"method": "GET", "route": "admin/ads/create", "handler": "\\App\\Controllers\\Admin\\Ads::create"}, {"method": "GET", "route": "admin/ads/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Ads::edit/$1"}, {"method": "GET", "route": "admin/ads/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Ads::delete/$1"}, {"method": "POST", "route": "api/track-ad-click/([0-9]+)", "handler": "\\App\\Controllers\\Api\\AdTracking::trackClick/$1"}, {"method": "POST", "route": "api/track-ad-impression/([0-9]+)", "handler": "\\App\\Controllers\\Api\\AdTracking::trackImpression/$1"}, {"method": "POST", "route": "admin/authenticate", "handler": "\\App\\Controllers\\Auth::authenticate"}, {"method": "POST", "route": "admin/users/store", "handler": "\\App\\Controllers\\Admin\\Users::store"}, {"method": "POST", "route": "admin/users/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Users::update/$1"}, {"method": "POST", "route": "admin/news/store", "handler": "\\App\\Controllers\\Admin\\News::store"}, {"method": "POST", "route": "admin/news/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\News::update/$1"}, {"method": "POST", "route": "admin/categories/store", "handler": "\\App\\Controllers\\Admin\\Categories::store"}, {"method": "POST", "route": "admin/categories/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Categories::update/$1"}, {"method": "POST", "route": "admin/tags/store", "handler": "\\App\\Controllers\\Admin\\Tags::store"}, {"method": "POST", "route": "admin/tags/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Tags::update/$1"}, {"method": "POST", "route": "admin/ads/store", "handler": "\\App\\Controllers\\Admin\\Ads::store"}, {"method": "POST", "route": "admin/ads/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Ads::update/$1"}]}, "badgeValue": 13, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "7.69", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "0.07", "count": 4}}}, "badgeValue": 5, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.927805, "duration": 0.007692098617553711}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.012993, "duration": 2.193450927734375e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.014024, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.014204, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.021565, "duration": 2.3126602172851562e-05}]}], "vars": {"varData": {"View Data": []}, "session": {"__ci_last_regenerate": "<pre>1750753317</pre>", "_ci_previous_url": "http://localhost/bbc_news/public/index.php/admin/news/create", "user_id": "1", "username": "admin", "email": "<EMAIL>", "full_name": "Administrator", "role": "admin", "isLoggedIn": "<pre>1</pre>", "success": "News article created successfully", "__ci_vars": "<pre>Array\n(\n    [success] =&gt; new\n)\n</pre>"}, "post": {"csrf_test_name": "8c60c7c238567f457ab076f4385ea33e", "title": "पीएम मोदी 12 जून को टीडीपी प्रमुख चंद्रबाबू नायडू के शपथ ग्रहण समारोह में शामिल होंगे", "description": "मनोनीत प्रधानमंत्री नरेंद्र मोदी अपने गठबंधन के साथी और टीडीपी प्रमुख के शपथ ग्रहण समारोह में शामिल हो सकते हैं। टीडीपी प्रमुख चंद्रबाबू नायडू आंध्र प्रदेश के मुख्यमंत्री के रूप में 12 जून को शपथ लेंगे। बता दें, मनोनीत प्रधानमंत्री मोदी आज राष्ट्रपति भवन में तीसरी बार प्रधानमंत्री के रूप में शपथ ग्रहण करेंगे।", "content": "मनोनीत प्रधानमंत्री नरेंद्र मोदी अपने गठबंधन के साथी और टीडीपी प्रमुख के शपथ ग्रहण समारोह में शामिल हो सकते हैं। टीडीपी प्रमुख चंद्रबाबू नायडू आंध्र प्रदेश के मुख्यमंत्री के रूप में 12 जून को शपथ लेंगे। बता दें, मनोनीत प्रधानमंत्री मोदी आज राष्ट्रपति भवन में तीसरी बार प्रधानमंत्री के रूप में शपथ ग्रहण करेंगे।\r\n\r\n \r\n\r\n\r\nमुख्य सचिव नीरभ कुमार प्रसाद ने शनिवार को बताया कि शपथ ग्रहण समारोह में पीएम मोदी, राज्यपाल एस अब्दुल नजीर और अन्य गणमान्य शामिल हो सकते हैं। नायडू बुधवार को सुबह 11:27 बजे गन्नवरम हवाई अड्डे के पास केसरपल्ली आईटी पार्क में शपथ लेंगे। मुख्य सचिव ने समारोह के लिए की जा रही तैयारियों की समीक्षा की और अधिकारियों को पुख्ता तैयारियां करने का निर्देश दिया। गन्नवरम हवाई अड्डे पर कई वीवीआईपी के पहुंचने की उम्मीद है। शपथ ग्रहण समारोह के लिए वरिष्ठ आईएएस अधिकारी पीएस पद्युम्न को राज्य समन्वयक नियुक्त किया गया है।\r\n\r\nकेंद्र में भी मजबूत स्तंभ बनकर उभरे\r\n\r\n \r\n\r\nगौरतलब है कि नायडू परिवार और चंद्रबाबू नायडू की पत्नी की संपत्ति में एक तरफ ये इजाफा हो रहा है। वहीं दूसरी ओर 12 जून को चंद्रबाबू नायडू आंध्र प्रदेश के नए सीएम बनेंगे। आंध्र प्रदेश विधानसभा चुनाव परिणाम में टीडीपी ने सबसे अधिक सीटें जीती हैं। 135 सीटें जीत कर टीडीपी ने बहुमत के आंकड़े को पार कर लिया।\r\n\r\nकिंग मेकर की भूमिका में हैं चंद्रबाबू नायडू\r\n\r\nचंद्रबाबू नायडू एनडीए गठबंधन के लिए एक किंगमेकर्स में से एक बनकर सामने आए हैं। एनडीए ने लगातार तीसरी बार बहुमत पाने का रिकॉर्ड बनया है। चंद्रबाबू नायडू और नीतीश कुमार की पार्टी का गठबंधन की सरकार बनाने में अहम योगदान रहने वाला है। भाजपा इस बार लोकसभा चुनाव में अकेले दम पर बहुमत लाने में सफल नहीं हो सकी है। वहीं कांग्रेस के नेतृत्व वाले इंडिया गठबंधन ने इस बार चुनाव में बेहतरीन प्रदर्शन किया है।", "category_id": "1", "status": "published"}, "headers": {"Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryOenE8E3UV2FLdEPo", "Host": "localhost", "Connection": "keep-alive", "Cache-Control": "max-age=0", "Sec-Ch-Ua": "&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Origin": "http://localhost", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Referer": "http://localhost/bbc_news/public/admin/news/create", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "en-US,en;q=0.9,eu;q=0.8,hi;q=0.7,bho;q=0.6,ne;q=0.5,nl;q=0.4,eo;q=0.3,haw;q=0.2", "Cookie": "csrf_cookie_name=8c60c7c238567f457ab076f4385ea33e; ci_session=37863bfbfd8ba717cfff576e2471f2b0"}, "cookies": {"csrf_cookie_name": "8c60c7c238567f457ab076f4385ea33e", "ci_session": "37863bfbfd8ba717cfff576e2471f2b0"}, "request": "HTTP/1.1", "response": {"statusCode": 303, "reason": "See Other", "contentType": "text/html; charset=UTF-8", "headers": {"Cache-Control": "no-store, max-age=0, no-cache", "Content-Type": "text/html; charset=UTF-8", "Location": "http://localhost/bbc_news/public/index.php/admin/news"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.12", "phpSAPI": "apache2handler", "environment": "development", "baseURL": "http://localhost/bbc_news/public/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}