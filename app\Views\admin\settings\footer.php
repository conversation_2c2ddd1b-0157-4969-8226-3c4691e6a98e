<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-copyright me-2"></i>Footer Settings</h2>
                <a href="<?= base_url('admin/settings') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Settings
                </a>
            </div>

            <!-- Success/Error Messages -->
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-edit me-2"></i>Footer Content Management
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="<?= base_url('admin/settings/footer/update') ?>" method="post">
                                <?= csrf_field() ?>
                                
                                <!-- Footer Copyright -->
                                <div class="mb-3">
                                    <label for="footer_copyright" class="form-label">
                                        <i class="fas fa-copyright me-2"></i>Copyright Text
                                    </label>
                                    <input type="text" class="form-control" id="footer_copyright" name="footer_copyright" 
                                           value="<?= old('footer_copyright', getSetting('footer_copyright', '© 2025 BBC News Portal. All rights reserved.')) ?>" 
                                           placeholder="© 2025 Your Website Name. All rights reserved.">
                                    <div class="form-text">Copyright notice that appears in the footer</div>
                                </div>

                                <!-- Footer Description -->
                                <div class="mb-3">
                                    <label for="footer_description" class="form-label">
                                        <i class="fas fa-align-left me-2"></i>Footer Description
                                    </label>
                                    <textarea class="form-control" id="footer_description" name="footer_description" rows="4" 
                                              placeholder="Brief description about your website or organization"><?= old('footer_description', getSetting('footer_description', 'Your trusted source for latest Hindi news and updates.')) ?></textarea>
                                    <div class="form-text">Brief description about your website (optional)</div>
                                </div>

                                <!-- Submit Button -->
                                <div class="d-flex justify-content-between">
                                    <a href="<?= base_url('admin/settings') ?>" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Update Footer Settings
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Footer Preview -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-eye me-2"></i>Footer Preview
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="footer-preview bg-dark text-white p-3 rounded">
                                <div class="text-center">
                                    <div class="mb-2">
                                        <small id="preview-description"><?= getSetting('footer_description', 'Your trusted source for latest Hindi news and updates.') ?></small>
                                    </div>
                                    <hr class="my-2" style="border-color: #6c757d;">
                                    <div>
                                        <small id="preview-copyright"><?= getSetting('footer_copyright', '© 2025 BBC News Portal. All rights reserved.') ?></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Footer Guidelines -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>Guidelines
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled small mb-0">
                                <li><i class="fas fa-check text-success me-2"></i>Include current year in copyright</li>
                                <li><i class="fas fa-check text-success me-2"></i>Use your actual website/company name</li>
                                <li><i class="fas fa-check text-success me-2"></i>Keep description brief and informative</li>
                                <li><i class="fas fa-check text-success me-2"></i>Update copyright year annually</li>
                                <li><i class="fas fa-check text-success me-2"></i>Ensure legal compliance</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Copyright Examples -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-lightbulb me-2"></i>Copyright Examples
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="small">
                                <div class="mb-2">
                                    <strong>Standard:</strong><br>
                                    <code>© 2025 BBC News Portal. All rights reserved.</code>
                                </div>
                                <div class="mb-2">
                                    <strong>With Company:</strong><br>
                                    <code>© 2025 BBC News Portal Pvt. Ltd. All rights reserved.</code>
                                </div>
                                <div class="mb-2">
                                    <strong>Hindi Version:</strong><br>
                                    <code>© 2025 बीबीसी न्यूज़ पोर्टल। सभी अधिकार सुरक्षित।</code>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>Quick Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="updateYear()">
                                    <i class="fas fa-calendar me-2"></i>Update to Current Year
                                </button>
                                <a href="<?= base_url('admin/settings/contact') ?>" class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-phone me-2"></i>Contact Settings
                                </a>
                                <a href="<?= base_url('admin/settings/social') ?>" class="btn btn-outline-warning btn-sm">
                                    <i class="fab fa-facebook me-2"></i>Social Media
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live preview updates
    const copyrightInput = document.getElementById('footer_copyright');
    const descriptionInput = document.getElementById('footer_description');
    const previewCopyright = document.getElementById('preview-copyright');
    const previewDescription = document.getElementById('preview-description');
    
    copyrightInput.addEventListener('input', function() {
        previewCopyright.textContent = this.value || '© 2025 BBC News Portal. All rights reserved.';
    });
    
    descriptionInput.addEventListener('input', function() {
        previewDescription.textContent = this.value || 'Your trusted source for latest Hindi news and updates.';
    });
});

function updateYear() {
    const currentYear = new Date().getFullYear();
    const copyrightInput = document.getElementById('footer_copyright');
    const currentText = copyrightInput.value;
    
    // Replace any 4-digit year with current year
    const updatedText = currentText.replace(/\b\d{4}\b/, currentYear);
    
    copyrightInput.value = updatedText;
    
    // Trigger input event to update preview
    copyrightInput.dispatchEvent(new Event('input'));
    
    // Show feedback
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-check me-2"></i>Updated!';
    btn.classList.remove('btn-outline-primary');
    btn.classList.add('btn-success');
    
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.classList.remove('btn-success');
        btn.classList.add('btn-outline-primary');
    }, 2000);
}
</script>
<?= $this->endSection() ?>
