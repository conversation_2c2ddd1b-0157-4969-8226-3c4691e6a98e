<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'App\\Controllers\\BaseController' => $baseDir . '/app/Controllers/BaseController.php',
    'App\\Controllers\\Home' => $baseDir . '/app/Controllers/Home.php',
    'CodeIgniter\\API\\ResponseTrait' => $vendorDir . '/codeigniter4/framework/system/API/ResponseTrait.php',
    'CodeIgniter\\Autoloader\\Autoloader' => $vendorDir . '/codeigniter4/framework/system/Autoloader/Autoloader.php',
    'CodeIgniter\\Autoloader\\FileLocator' => $vendorDir . '/codeigniter4/framework/system/Autoloader/FileLocator.php',
    'CodeIgniter\\Autoloader\\FileLocatorCached' => $vendorDir . '/codeigniter4/framework/system/Autoloader/FileLocatorCached.php',
    'CodeIgniter\\Autoloader\\FileLocatorInterface' => $vendorDir . '/codeigniter4/framework/system/Autoloader/FileLocatorInterface.php',
    'CodeIgniter\\BaseModel' => $vendorDir . '/codeigniter4/framework/system/BaseModel.php',
    'CodeIgniter\\Boot' => $vendorDir . '/codeigniter4/framework/system/Boot.php',
    'CodeIgniter\\CLI\\BaseCommand' => $vendorDir . '/codeigniter4/framework/system/CLI/BaseCommand.php',
    'CodeIgniter\\CLI\\CLI' => $vendorDir . '/codeigniter4/framework/system/CLI/CLI.php',
    'CodeIgniter\\CLI\\Commands' => $vendorDir . '/codeigniter4/framework/system/CLI/Commands.php',
    'CodeIgniter\\CLI\\Console' => $vendorDir . '/codeigniter4/framework/system/CLI/Console.php',
    'CodeIgniter\\CLI\\Exceptions\\CLIException' => $vendorDir . '/codeigniter4/framework/system/CLI/Exceptions/CLIException.php',
    'CodeIgniter\\CLI\\GeneratorTrait' => $vendorDir . '/codeigniter4/framework/system/CLI/GeneratorTrait.php',
    'CodeIgniter\\CLI\\InputOutput' => $vendorDir . '/codeigniter4/framework/system/CLI/InputOutput.php',
    'CodeIgniter\\Cache\\CacheFactory' => $vendorDir . '/codeigniter4/framework/system/Cache/CacheFactory.php',
    'CodeIgniter\\Cache\\CacheInterface' => $vendorDir . '/codeigniter4/framework/system/Cache/CacheInterface.php',
    'CodeIgniter\\Cache\\Exceptions\\CacheException' => $vendorDir . '/codeigniter4/framework/system/Cache/Exceptions/CacheException.php',
    'CodeIgniter\\Cache\\FactoriesCache' => $vendorDir . '/codeigniter4/framework/system/Cache/FactoriesCache.php',
    'CodeIgniter\\Cache\\FactoriesCache\\FileVarExportHandler' => $vendorDir . '/codeigniter4/framework/system/Cache/FactoriesCache/FileVarExportHandler.php',
    'CodeIgniter\\Cache\\Handlers\\BaseHandler' => $vendorDir . '/codeigniter4/framework/system/Cache/Handlers/BaseHandler.php',
    'CodeIgniter\\Cache\\Handlers\\DummyHandler' => $vendorDir . '/codeigniter4/framework/system/Cache/Handlers/DummyHandler.php',
    'CodeIgniter\\Cache\\Handlers\\FileHandler' => $vendorDir . '/codeigniter4/framework/system/Cache/Handlers/FileHandler.php',
    'CodeIgniter\\Cache\\Handlers\\MemcachedHandler' => $vendorDir . '/codeigniter4/framework/system/Cache/Handlers/MemcachedHandler.php',
    'CodeIgniter\\Cache\\Handlers\\PredisHandler' => $vendorDir . '/codeigniter4/framework/system/Cache/Handlers/PredisHandler.php',
    'CodeIgniter\\Cache\\Handlers\\RedisHandler' => $vendorDir . '/codeigniter4/framework/system/Cache/Handlers/RedisHandler.php',
    'CodeIgniter\\Cache\\Handlers\\WincacheHandler' => $vendorDir . '/codeigniter4/framework/system/Cache/Handlers/WincacheHandler.php',
    'CodeIgniter\\Cache\\ResponseCache' => $vendorDir . '/codeigniter4/framework/system/Cache/ResponseCache.php',
    'CodeIgniter\\CodeIgniter' => $vendorDir . '/codeigniter4/framework/system/CodeIgniter.php',
    'CodeIgniter\\Commands\\Cache\\ClearCache' => $vendorDir . '/codeigniter4/framework/system/Commands/Cache/ClearCache.php',
    'CodeIgniter\\Commands\\Cache\\InfoCache' => $vendorDir . '/codeigniter4/framework/system/Commands/Cache/InfoCache.php',
    'CodeIgniter\\Commands\\Database\\CreateDatabase' => $vendorDir . '/codeigniter4/framework/system/Commands/Database/CreateDatabase.php',
    'CodeIgniter\\Commands\\Database\\Migrate' => $vendorDir . '/codeigniter4/framework/system/Commands/Database/Migrate.php',
    'CodeIgniter\\Commands\\Database\\MigrateRefresh' => $vendorDir . '/codeigniter4/framework/system/Commands/Database/MigrateRefresh.php',
    'CodeIgniter\\Commands\\Database\\MigrateRollback' => $vendorDir . '/codeigniter4/framework/system/Commands/Database/MigrateRollback.php',
    'CodeIgniter\\Commands\\Database\\MigrateStatus' => $vendorDir . '/codeigniter4/framework/system/Commands/Database/MigrateStatus.php',
    'CodeIgniter\\Commands\\Database\\Seed' => $vendorDir . '/codeigniter4/framework/system/Commands/Database/Seed.php',
    'CodeIgniter\\Commands\\Database\\ShowTableInfo' => $vendorDir . '/codeigniter4/framework/system/Commands/Database/ShowTableInfo.php',
    'CodeIgniter\\Commands\\Encryption\\GenerateKey' => $vendorDir . '/codeigniter4/framework/system/Commands/Encryption/GenerateKey.php',
    'CodeIgniter\\Commands\\Generators\\CellGenerator' => $vendorDir . '/codeigniter4/framework/system/Commands/Generators/CellGenerator.php',
    'CodeIgniter\\Commands\\Generators\\CommandGenerator' => $vendorDir . '/codeigniter4/framework/system/Commands/Generators/CommandGenerator.php',
    'CodeIgniter\\Commands\\Generators\\ConfigGenerator' => $vendorDir . '/codeigniter4/framework/system/Commands/Generators/ConfigGenerator.php',
    'CodeIgniter\\Commands\\Generators\\ControllerGenerator' => $vendorDir . '/codeigniter4/framework/system/Commands/Generators/ControllerGenerator.php',
    'CodeIgniter\\Commands\\Generators\\EntityGenerator' => $vendorDir . '/codeigniter4/framework/system/Commands/Generators/EntityGenerator.php',
    'CodeIgniter\\Commands\\Generators\\FilterGenerator' => $vendorDir . '/codeigniter4/framework/system/Commands/Generators/FilterGenerator.php',
    'CodeIgniter\\Commands\\Generators\\MigrationGenerator' => $vendorDir . '/codeigniter4/framework/system/Commands/Generators/MigrationGenerator.php',
    'CodeIgniter\\Commands\\Generators\\ModelGenerator' => $vendorDir . '/codeigniter4/framework/system/Commands/Generators/ModelGenerator.php',
    'CodeIgniter\\Commands\\Generators\\ScaffoldGenerator' => $vendorDir . '/codeigniter4/framework/system/Commands/Generators/ScaffoldGenerator.php',
    'CodeIgniter\\Commands\\Generators\\SeederGenerator' => $vendorDir . '/codeigniter4/framework/system/Commands/Generators/SeederGenerator.php',
    'CodeIgniter\\Commands\\Generators\\TestGenerator' => $vendorDir . '/codeigniter4/framework/system/Commands/Generators/TestGenerator.php',
    'CodeIgniter\\Commands\\Generators\\ValidationGenerator' => $vendorDir . '/codeigniter4/framework/system/Commands/Generators/ValidationGenerator.php',
    'CodeIgniter\\Commands\\Help' => $vendorDir . '/codeigniter4/framework/system/Commands/Help.php',
    'CodeIgniter\\Commands\\Housekeeping\\ClearDebugbar' => $vendorDir . '/codeigniter4/framework/system/Commands/Housekeeping/ClearDebugbar.php',
    'CodeIgniter\\Commands\\Housekeeping\\ClearLogs' => $vendorDir . '/codeigniter4/framework/system/Commands/Housekeeping/ClearLogs.php',
    'CodeIgniter\\Commands\\ListCommands' => $vendorDir . '/codeigniter4/framework/system/Commands/ListCommands.php',
    'CodeIgniter\\Commands\\Server\\Serve' => $vendorDir . '/codeigniter4/framework/system/Commands/Server/Serve.php',
    'CodeIgniter\\Commands\\Translation\\LocalizationFinder' => $vendorDir . '/codeigniter4/framework/system/Commands/Translation/LocalizationFinder.php',
    'CodeIgniter\\Commands\\Translation\\LocalizationSync' => $vendorDir . '/codeigniter4/framework/system/Commands/Translation/LocalizationSync.php',
    'CodeIgniter\\Commands\\Utilities\\ConfigCheck' => $vendorDir . '/codeigniter4/framework/system/Commands/Utilities/ConfigCheck.php',
    'CodeIgniter\\Commands\\Utilities\\Environment' => $vendorDir . '/codeigniter4/framework/system/Commands/Utilities/Environment.php',
    'CodeIgniter\\Commands\\Utilities\\FilterCheck' => $vendorDir . '/codeigniter4/framework/system/Commands/Utilities/FilterCheck.php',
    'CodeIgniter\\Commands\\Utilities\\Namespaces' => $vendorDir . '/codeigniter4/framework/system/Commands/Utilities/Namespaces.php',
    'CodeIgniter\\Commands\\Utilities\\Optimize' => $vendorDir . '/codeigniter4/framework/system/Commands/Utilities/Optimize.php',
    'CodeIgniter\\Commands\\Utilities\\PhpIniCheck' => $vendorDir . '/codeigniter4/framework/system/Commands/Utilities/PhpIniCheck.php',
    'CodeIgniter\\Commands\\Utilities\\Publish' => $vendorDir . '/codeigniter4/framework/system/Commands/Utilities/Publish.php',
    'CodeIgniter\\Commands\\Utilities\\Routes' => $vendorDir . '/codeigniter4/framework/system/Commands/Utilities/Routes.php',
    'CodeIgniter\\Commands\\Utilities\\Routes\\AutoRouteCollector' => $vendorDir . '/codeigniter4/framework/system/Commands/Utilities/Routes/AutoRouteCollector.php',
    'CodeIgniter\\Commands\\Utilities\\Routes\\AutoRouterImproved\\AutoRouteCollector' => $vendorDir . '/codeigniter4/framework/system/Commands/Utilities/Routes/AutoRouterImproved/AutoRouteCollector.php',
    'CodeIgniter\\Commands\\Utilities\\Routes\\AutoRouterImproved\\ControllerMethodReader' => $vendorDir . '/codeigniter4/framework/system/Commands/Utilities/Routes/AutoRouterImproved/ControllerMethodReader.php',
    'CodeIgniter\\Commands\\Utilities\\Routes\\ControllerFinder' => $vendorDir . '/codeigniter4/framework/system/Commands/Utilities/Routes/ControllerFinder.php',
    'CodeIgniter\\Commands\\Utilities\\Routes\\ControllerMethodReader' => $vendorDir . '/codeigniter4/framework/system/Commands/Utilities/Routes/ControllerMethodReader.php',
    'CodeIgniter\\Commands\\Utilities\\Routes\\FilterCollector' => $vendorDir . '/codeigniter4/framework/system/Commands/Utilities/Routes/FilterCollector.php',
    'CodeIgniter\\Commands\\Utilities\\Routes\\FilterFinder' => $vendorDir . '/codeigniter4/framework/system/Commands/Utilities/Routes/FilterFinder.php',
    'CodeIgniter\\Commands\\Utilities\\Routes\\SampleURIGenerator' => $vendorDir . '/codeigniter4/framework/system/Commands/Utilities/Routes/SampleURIGenerator.php',
    'CodeIgniter\\ComposerScripts' => $vendorDir . '/codeigniter4/framework/system/ComposerScripts.php',
    'CodeIgniter\\Config\\AutoloadConfig' => $vendorDir . '/codeigniter4/framework/system/Config/AutoloadConfig.php',
    'CodeIgniter\\Config\\BaseConfig' => $vendorDir . '/codeigniter4/framework/system/Config/BaseConfig.php',
    'CodeIgniter\\Config\\BaseService' => $vendorDir . '/codeigniter4/framework/system/Config/BaseService.php',
    'CodeIgniter\\Config\\DotEnv' => $vendorDir . '/codeigniter4/framework/system/Config/DotEnv.php',
    'CodeIgniter\\Config\\Factories' => $vendorDir . '/codeigniter4/framework/system/Config/Factories.php',
    'CodeIgniter\\Config\\Factory' => $vendorDir . '/codeigniter4/framework/system/Config/Factory.php',
    'CodeIgniter\\Config\\Filters' => $vendorDir . '/codeigniter4/framework/system/Config/Filters.php',
    'CodeIgniter\\Config\\ForeignCharacters' => $vendorDir . '/codeigniter4/framework/system/Config/ForeignCharacters.php',
    'CodeIgniter\\Config\\Publisher' => $vendorDir . '/codeigniter4/framework/system/Config/Publisher.php',
    'CodeIgniter\\Config\\Routing' => $vendorDir . '/codeigniter4/framework/system/Config/Routing.php',
    'CodeIgniter\\Config\\Services' => $vendorDir . '/codeigniter4/framework/system/Config/Services.php',
    'CodeIgniter\\Config\\View' => $vendorDir . '/codeigniter4/framework/system/Config/View.php',
    'CodeIgniter\\Controller' => $vendorDir . '/codeigniter4/framework/system/Controller.php',
    'CodeIgniter\\Cookie\\CloneableCookieInterface' => $vendorDir . '/codeigniter4/framework/system/Cookie/CloneableCookieInterface.php',
    'CodeIgniter\\Cookie\\Cookie' => $vendorDir . '/codeigniter4/framework/system/Cookie/Cookie.php',
    'CodeIgniter\\Cookie\\CookieInterface' => $vendorDir . '/codeigniter4/framework/system/Cookie/CookieInterface.php',
    'CodeIgniter\\Cookie\\CookieStore' => $vendorDir . '/codeigniter4/framework/system/Cookie/CookieStore.php',
    'CodeIgniter\\Cookie\\Exceptions\\CookieException' => $vendorDir . '/codeigniter4/framework/system/Cookie/Exceptions/CookieException.php',
    'CodeIgniter\\DataCaster\\Cast\\ArrayCast' => $vendorDir . '/codeigniter4/framework/system/DataCaster/Cast/ArrayCast.php',
    'CodeIgniter\\DataCaster\\Cast\\BaseCast' => $vendorDir . '/codeigniter4/framework/system/DataCaster/Cast/BaseCast.php',
    'CodeIgniter\\DataCaster\\Cast\\BooleanCast' => $vendorDir . '/codeigniter4/framework/system/DataCaster/Cast/BooleanCast.php',
    'CodeIgniter\\DataCaster\\Cast\\CSVCast' => $vendorDir . '/codeigniter4/framework/system/DataCaster/Cast/CSVCast.php',
    'CodeIgniter\\DataCaster\\Cast\\CastInterface' => $vendorDir . '/codeigniter4/framework/system/DataCaster/Cast/CastInterface.php',
    'CodeIgniter\\DataCaster\\Cast\\DatetimeCast' => $vendorDir . '/codeigniter4/framework/system/DataCaster/Cast/DatetimeCast.php',
    'CodeIgniter\\DataCaster\\Cast\\FloatCast' => $vendorDir . '/codeigniter4/framework/system/DataCaster/Cast/FloatCast.php',
    'CodeIgniter\\DataCaster\\Cast\\IntBoolCast' => $vendorDir . '/codeigniter4/framework/system/DataCaster/Cast/IntBoolCast.php',
    'CodeIgniter\\DataCaster\\Cast\\IntegerCast' => $vendorDir . '/codeigniter4/framework/system/DataCaster/Cast/IntegerCast.php',
    'CodeIgniter\\DataCaster\\Cast\\JsonCast' => $vendorDir . '/codeigniter4/framework/system/DataCaster/Cast/JsonCast.php',
    'CodeIgniter\\DataCaster\\Cast\\TimestampCast' => $vendorDir . '/codeigniter4/framework/system/DataCaster/Cast/TimestampCast.php',
    'CodeIgniter\\DataCaster\\Cast\\URICast' => $vendorDir . '/codeigniter4/framework/system/DataCaster/Cast/URICast.php',
    'CodeIgniter\\DataCaster\\DataCaster' => $vendorDir . '/codeigniter4/framework/system/DataCaster/DataCaster.php',
    'CodeIgniter\\DataCaster\\Exceptions\\CastException' => $vendorDir . '/codeigniter4/framework/system/DataCaster/Exceptions/CastException.php',
    'CodeIgniter\\DataConverter\\DataConverter' => $vendorDir . '/codeigniter4/framework/system/DataConverter/DataConverter.php',
    'CodeIgniter\\Database\\BaseBuilder' => $vendorDir . '/codeigniter4/framework/system/Database/BaseBuilder.php',
    'CodeIgniter\\Database\\BaseConnection' => $vendorDir . '/codeigniter4/framework/system/Database/BaseConnection.php',
    'CodeIgniter\\Database\\BasePreparedQuery' => $vendorDir . '/codeigniter4/framework/system/Database/BasePreparedQuery.php',
    'CodeIgniter\\Database\\BaseResult' => $vendorDir . '/codeigniter4/framework/system/Database/BaseResult.php',
    'CodeIgniter\\Database\\BaseUtils' => $vendorDir . '/codeigniter4/framework/system/Database/BaseUtils.php',
    'CodeIgniter\\Database\\Config' => $vendorDir . '/codeigniter4/framework/system/Database/Config.php',
    'CodeIgniter\\Database\\ConnectionInterface' => $vendorDir . '/codeigniter4/framework/system/Database/ConnectionInterface.php',
    'CodeIgniter\\Database\\Database' => $vendorDir . '/codeigniter4/framework/system/Database/Database.php',
    'CodeIgniter\\Database\\Exceptions\\DataException' => $vendorDir . '/codeigniter4/framework/system/Database/Exceptions/DataException.php',
    'CodeIgniter\\Database\\Exceptions\\DatabaseException' => $vendorDir . '/codeigniter4/framework/system/Database/Exceptions/DatabaseException.php',
    'CodeIgniter\\Database\\Exceptions\\ExceptionInterface' => $vendorDir . '/codeigniter4/framework/system/Database/Exceptions/ExceptionInterface.php',
    'CodeIgniter\\Database\\Forge' => $vendorDir . '/codeigniter4/framework/system/Database/Forge.php',
    'CodeIgniter\\Database\\Migration' => $vendorDir . '/codeigniter4/framework/system/Database/Migration.php',
    'CodeIgniter\\Database\\MigrationRunner' => $vendorDir . '/codeigniter4/framework/system/Database/MigrationRunner.php',
    'CodeIgniter\\Database\\MySQLi\\Builder' => $vendorDir . '/codeigniter4/framework/system/Database/MySQLi/Builder.php',
    'CodeIgniter\\Database\\MySQLi\\Connection' => $vendorDir . '/codeigniter4/framework/system/Database/MySQLi/Connection.php',
    'CodeIgniter\\Database\\MySQLi\\Forge' => $vendorDir . '/codeigniter4/framework/system/Database/MySQLi/Forge.php',
    'CodeIgniter\\Database\\MySQLi\\PreparedQuery' => $vendorDir . '/codeigniter4/framework/system/Database/MySQLi/PreparedQuery.php',
    'CodeIgniter\\Database\\MySQLi\\Result' => $vendorDir . '/codeigniter4/framework/system/Database/MySQLi/Result.php',
    'CodeIgniter\\Database\\MySQLi\\Utils' => $vendorDir . '/codeigniter4/framework/system/Database/MySQLi/Utils.php',
    'CodeIgniter\\Database\\OCI8\\Builder' => $vendorDir . '/codeigniter4/framework/system/Database/OCI8/Builder.php',
    'CodeIgniter\\Database\\OCI8\\Connection' => $vendorDir . '/codeigniter4/framework/system/Database/OCI8/Connection.php',
    'CodeIgniter\\Database\\OCI8\\Forge' => $vendorDir . '/codeigniter4/framework/system/Database/OCI8/Forge.php',
    'CodeIgniter\\Database\\OCI8\\PreparedQuery' => $vendorDir . '/codeigniter4/framework/system/Database/OCI8/PreparedQuery.php',
    'CodeIgniter\\Database\\OCI8\\Result' => $vendorDir . '/codeigniter4/framework/system/Database/OCI8/Result.php',
    'CodeIgniter\\Database\\OCI8\\Utils' => $vendorDir . '/codeigniter4/framework/system/Database/OCI8/Utils.php',
    'CodeIgniter\\Database\\Postgre\\Builder' => $vendorDir . '/codeigniter4/framework/system/Database/Postgre/Builder.php',
    'CodeIgniter\\Database\\Postgre\\Connection' => $vendorDir . '/codeigniter4/framework/system/Database/Postgre/Connection.php',
    'CodeIgniter\\Database\\Postgre\\Forge' => $vendorDir . '/codeigniter4/framework/system/Database/Postgre/Forge.php',
    'CodeIgniter\\Database\\Postgre\\PreparedQuery' => $vendorDir . '/codeigniter4/framework/system/Database/Postgre/PreparedQuery.php',
    'CodeIgniter\\Database\\Postgre\\Result' => $vendorDir . '/codeigniter4/framework/system/Database/Postgre/Result.php',
    'CodeIgniter\\Database\\Postgre\\Utils' => $vendorDir . '/codeigniter4/framework/system/Database/Postgre/Utils.php',
    'CodeIgniter\\Database\\PreparedQueryInterface' => $vendorDir . '/codeigniter4/framework/system/Database/PreparedQueryInterface.php',
    'CodeIgniter\\Database\\Query' => $vendorDir . '/codeigniter4/framework/system/Database/Query.php',
    'CodeIgniter\\Database\\QueryInterface' => $vendorDir . '/codeigniter4/framework/system/Database/QueryInterface.php',
    'CodeIgniter\\Database\\RawSql' => $vendorDir . '/codeigniter4/framework/system/Database/RawSql.php',
    'CodeIgniter\\Database\\ResultInterface' => $vendorDir . '/codeigniter4/framework/system/Database/ResultInterface.php',
    'CodeIgniter\\Database\\SQLSRV\\Builder' => $vendorDir . '/codeigniter4/framework/system/Database/SQLSRV/Builder.php',
    'CodeIgniter\\Database\\SQLSRV\\Connection' => $vendorDir . '/codeigniter4/framework/system/Database/SQLSRV/Connection.php',
    'CodeIgniter\\Database\\SQLSRV\\Forge' => $vendorDir . '/codeigniter4/framework/system/Database/SQLSRV/Forge.php',
    'CodeIgniter\\Database\\SQLSRV\\PreparedQuery' => $vendorDir . '/codeigniter4/framework/system/Database/SQLSRV/PreparedQuery.php',
    'CodeIgniter\\Database\\SQLSRV\\Result' => $vendorDir . '/codeigniter4/framework/system/Database/SQLSRV/Result.php',
    'CodeIgniter\\Database\\SQLSRV\\Utils' => $vendorDir . '/codeigniter4/framework/system/Database/SQLSRV/Utils.php',
    'CodeIgniter\\Database\\SQLite3\\Builder' => $vendorDir . '/codeigniter4/framework/system/Database/SQLite3/Builder.php',
    'CodeIgniter\\Database\\SQLite3\\Connection' => $vendorDir . '/codeigniter4/framework/system/Database/SQLite3/Connection.php',
    'CodeIgniter\\Database\\SQLite3\\Forge' => $vendorDir . '/codeigniter4/framework/system/Database/SQLite3/Forge.php',
    'CodeIgniter\\Database\\SQLite3\\PreparedQuery' => $vendorDir . '/codeigniter4/framework/system/Database/SQLite3/PreparedQuery.php',
    'CodeIgniter\\Database\\SQLite3\\Result' => $vendorDir . '/codeigniter4/framework/system/Database/SQLite3/Result.php',
    'CodeIgniter\\Database\\SQLite3\\Table' => $vendorDir . '/codeigniter4/framework/system/Database/SQLite3/Table.php',
    'CodeIgniter\\Database\\SQLite3\\Utils' => $vendorDir . '/codeigniter4/framework/system/Database/SQLite3/Utils.php',
    'CodeIgniter\\Database\\Seeder' => $vendorDir . '/codeigniter4/framework/system/Database/Seeder.php',
    'CodeIgniter\\Database\\TableName' => $vendorDir . '/codeigniter4/framework/system/Database/TableName.php',
    'CodeIgniter\\Debug\\BaseExceptionHandler' => $vendorDir . '/codeigniter4/framework/system/Debug/BaseExceptionHandler.php',
    'CodeIgniter\\Debug\\ExceptionHandler' => $vendorDir . '/codeigniter4/framework/system/Debug/ExceptionHandler.php',
    'CodeIgniter\\Debug\\ExceptionHandlerInterface' => $vendorDir . '/codeigniter4/framework/system/Debug/ExceptionHandlerInterface.php',
    'CodeIgniter\\Debug\\Exceptions' => $vendorDir . '/codeigniter4/framework/system/Debug/Exceptions.php',
    'CodeIgniter\\Debug\\Iterator' => $vendorDir . '/codeigniter4/framework/system/Debug/Iterator.php',
    'CodeIgniter\\Debug\\Timer' => $vendorDir . '/codeigniter4/framework/system/Debug/Timer.php',
    'CodeIgniter\\Debug\\Toolbar' => $vendorDir . '/codeigniter4/framework/system/Debug/Toolbar.php',
    'CodeIgniter\\Debug\\Toolbar\\Collectors\\BaseCollector' => $vendorDir . '/codeigniter4/framework/system/Debug/Toolbar/Collectors/BaseCollector.php',
    'CodeIgniter\\Debug\\Toolbar\\Collectors\\Config' => $vendorDir . '/codeigniter4/framework/system/Debug/Toolbar/Collectors/Config.php',
    'CodeIgniter\\Debug\\Toolbar\\Collectors\\Database' => $vendorDir . '/codeigniter4/framework/system/Debug/Toolbar/Collectors/Database.php',
    'CodeIgniter\\Debug\\Toolbar\\Collectors\\Events' => $vendorDir . '/codeigniter4/framework/system/Debug/Toolbar/Collectors/Events.php',
    'CodeIgniter\\Debug\\Toolbar\\Collectors\\Files' => $vendorDir . '/codeigniter4/framework/system/Debug/Toolbar/Collectors/Files.php',
    'CodeIgniter\\Debug\\Toolbar\\Collectors\\History' => $vendorDir . '/codeigniter4/framework/system/Debug/Toolbar/Collectors/History.php',
    'CodeIgniter\\Debug\\Toolbar\\Collectors\\Logs' => $vendorDir . '/codeigniter4/framework/system/Debug/Toolbar/Collectors/Logs.php',
    'CodeIgniter\\Debug\\Toolbar\\Collectors\\Routes' => $vendorDir . '/codeigniter4/framework/system/Debug/Toolbar/Collectors/Routes.php',
    'CodeIgniter\\Debug\\Toolbar\\Collectors\\Timers' => $vendorDir . '/codeigniter4/framework/system/Debug/Toolbar/Collectors/Timers.php',
    'CodeIgniter\\Debug\\Toolbar\\Collectors\\Views' => $vendorDir . '/codeigniter4/framework/system/Debug/Toolbar/Collectors/Views.php',
    'CodeIgniter\\Email\\Email' => $vendorDir . '/codeigniter4/framework/system/Email/Email.php',
    'CodeIgniter\\Encryption\\EncrypterInterface' => $vendorDir . '/codeigniter4/framework/system/Encryption/EncrypterInterface.php',
    'CodeIgniter\\Encryption\\Encryption' => $vendorDir . '/codeigniter4/framework/system/Encryption/Encryption.php',
    'CodeIgniter\\Encryption\\Exceptions\\EncryptionException' => $vendorDir . '/codeigniter4/framework/system/Encryption/Exceptions/EncryptionException.php',
    'CodeIgniter\\Encryption\\Handlers\\BaseHandler' => $vendorDir . '/codeigniter4/framework/system/Encryption/Handlers/BaseHandler.php',
    'CodeIgniter\\Encryption\\Handlers\\OpenSSLHandler' => $vendorDir . '/codeigniter4/framework/system/Encryption/Handlers/OpenSSLHandler.php',
    'CodeIgniter\\Encryption\\Handlers\\SodiumHandler' => $vendorDir . '/codeigniter4/framework/system/Encryption/Handlers/SodiumHandler.php',
    'CodeIgniter\\Entity\\Cast\\ArrayCast' => $vendorDir . '/codeigniter4/framework/system/Entity/Cast/ArrayCast.php',
    'CodeIgniter\\Entity\\Cast\\BaseCast' => $vendorDir . '/codeigniter4/framework/system/Entity/Cast/BaseCast.php',
    'CodeIgniter\\Entity\\Cast\\BooleanCast' => $vendorDir . '/codeigniter4/framework/system/Entity/Cast/BooleanCast.php',
    'CodeIgniter\\Entity\\Cast\\CSVCast' => $vendorDir . '/codeigniter4/framework/system/Entity/Cast/CSVCast.php',
    'CodeIgniter\\Entity\\Cast\\CastInterface' => $vendorDir . '/codeigniter4/framework/system/Entity/Cast/CastInterface.php',
    'CodeIgniter\\Entity\\Cast\\DatetimeCast' => $vendorDir . '/codeigniter4/framework/system/Entity/Cast/DatetimeCast.php',
    'CodeIgniter\\Entity\\Cast\\FloatCast' => $vendorDir . '/codeigniter4/framework/system/Entity/Cast/FloatCast.php',
    'CodeIgniter\\Entity\\Cast\\IntBoolCast' => $vendorDir . '/codeigniter4/framework/system/Entity/Cast/IntBoolCast.php',
    'CodeIgniter\\Entity\\Cast\\IntegerCast' => $vendorDir . '/codeigniter4/framework/system/Entity/Cast/IntegerCast.php',
    'CodeIgniter\\Entity\\Cast\\JsonCast' => $vendorDir . '/codeigniter4/framework/system/Entity/Cast/JsonCast.php',
    'CodeIgniter\\Entity\\Cast\\ObjectCast' => $vendorDir . '/codeigniter4/framework/system/Entity/Cast/ObjectCast.php',
    'CodeIgniter\\Entity\\Cast\\StringCast' => $vendorDir . '/codeigniter4/framework/system/Entity/Cast/StringCast.php',
    'CodeIgniter\\Entity\\Cast\\TimestampCast' => $vendorDir . '/codeigniter4/framework/system/Entity/Cast/TimestampCast.php',
    'CodeIgniter\\Entity\\Cast\\URICast' => $vendorDir . '/codeigniter4/framework/system/Entity/Cast/URICast.php',
    'CodeIgniter\\Entity\\Entity' => $vendorDir . '/codeigniter4/framework/system/Entity/Entity.php',
    'CodeIgniter\\Entity\\Exceptions\\CastException' => $vendorDir . '/codeigniter4/framework/system/Entity/Exceptions/CastException.php',
    'CodeIgniter\\Events\\Events' => $vendorDir . '/codeigniter4/framework/system/Events/Events.php',
    'CodeIgniter\\Exceptions\\BadFunctionCallException' => $vendorDir . '/codeigniter4/framework/system/Exceptions/BadFunctionCallException.php',
    'CodeIgniter\\Exceptions\\BadMethodCallException' => $vendorDir . '/codeigniter4/framework/system/Exceptions/BadMethodCallException.php',
    'CodeIgniter\\Exceptions\\ConfigException' => $vendorDir . '/codeigniter4/framework/system/Exceptions/ConfigException.php',
    'CodeIgniter\\Exceptions\\CriticalError' => $vendorDir . '/codeigniter4/framework/system/Exceptions/CriticalError.php',
    'CodeIgniter\\Exceptions\\DebugTraceableTrait' => $vendorDir . '/codeigniter4/framework/system/Exceptions/DebugTraceableTrait.php',
    'CodeIgniter\\Exceptions\\DownloadException' => $vendorDir . '/codeigniter4/framework/system/Exceptions/DownloadException.php',
    'CodeIgniter\\Exceptions\\ExceptionInterface' => $vendorDir . '/codeigniter4/framework/system/Exceptions/ExceptionInterface.php',
    'CodeIgniter\\Exceptions\\FrameworkException' => $vendorDir . '/codeigniter4/framework/system/Exceptions/FrameworkException.php',
    'CodeIgniter\\Exceptions\\HTTPExceptionInterface' => $vendorDir . '/codeigniter4/framework/system/Exceptions/HTTPExceptionInterface.php',
    'CodeIgniter\\Exceptions\\HasExitCodeInterface' => $vendorDir . '/codeigniter4/framework/system/Exceptions/HasExitCodeInterface.php',
    'CodeIgniter\\Exceptions\\InvalidArgumentException' => $vendorDir . '/codeigniter4/framework/system/Exceptions/InvalidArgumentException.php',
    'CodeIgniter\\Exceptions\\LogicException' => $vendorDir . '/codeigniter4/framework/system/Exceptions/LogicException.php',
    'CodeIgniter\\Exceptions\\ModelException' => $vendorDir . '/codeigniter4/framework/system/Exceptions/ModelException.php',
    'CodeIgniter\\Exceptions\\PageNotFoundException' => $vendorDir . '/codeigniter4/framework/system/Exceptions/PageNotFoundException.php',
    'CodeIgniter\\Exceptions\\RuntimeException' => $vendorDir . '/codeigniter4/framework/system/Exceptions/RuntimeException.php',
    'CodeIgniter\\Exceptions\\TestException' => $vendorDir . '/codeigniter4/framework/system/Exceptions/TestException.php',
    'CodeIgniter\\Files\\Exceptions\\ExceptionInterface' => $vendorDir . '/codeigniter4/framework/system/Files/Exceptions/ExceptionInterface.php',
    'CodeIgniter\\Files\\Exceptions\\FileException' => $vendorDir . '/codeigniter4/framework/system/Files/Exceptions/FileException.php',
    'CodeIgniter\\Files\\Exceptions\\FileNotFoundException' => $vendorDir . '/codeigniter4/framework/system/Files/Exceptions/FileNotFoundException.php',
    'CodeIgniter\\Files\\File' => $vendorDir . '/codeigniter4/framework/system/Files/File.php',
    'CodeIgniter\\Files\\FileCollection' => $vendorDir . '/codeigniter4/framework/system/Files/FileCollection.php',
    'CodeIgniter\\Files\\FileSizeUnit' => $vendorDir . '/codeigniter4/framework/system/Files/FileSizeUnit.php',
    'CodeIgniter\\Filters\\CSRF' => $vendorDir . '/codeigniter4/framework/system/Filters/CSRF.php',
    'CodeIgniter\\Filters\\Cors' => $vendorDir . '/codeigniter4/framework/system/Filters/Cors.php',
    'CodeIgniter\\Filters\\DebugToolbar' => $vendorDir . '/codeigniter4/framework/system/Filters/DebugToolbar.php',
    'CodeIgniter\\Filters\\Exceptions\\FilterException' => $vendorDir . '/codeigniter4/framework/system/Filters/Exceptions/FilterException.php',
    'CodeIgniter\\Filters\\FilterInterface' => $vendorDir . '/codeigniter4/framework/system/Filters/FilterInterface.php',
    'CodeIgniter\\Filters\\Filters' => $vendorDir . '/codeigniter4/framework/system/Filters/Filters.php',
    'CodeIgniter\\Filters\\ForceHTTPS' => $vendorDir . '/codeigniter4/framework/system/Filters/ForceHTTPS.php',
    'CodeIgniter\\Filters\\Honeypot' => $vendorDir . '/codeigniter4/framework/system/Filters/Honeypot.php',
    'CodeIgniter\\Filters\\InvalidChars' => $vendorDir . '/codeigniter4/framework/system/Filters/InvalidChars.php',
    'CodeIgniter\\Filters\\PageCache' => $vendorDir . '/codeigniter4/framework/system/Filters/PageCache.php',
    'CodeIgniter\\Filters\\PerformanceMetrics' => $vendorDir . '/codeigniter4/framework/system/Filters/PerformanceMetrics.php',
    'CodeIgniter\\Filters\\SecureHeaders' => $vendorDir . '/codeigniter4/framework/system/Filters/SecureHeaders.php',
    'CodeIgniter\\Format\\Exceptions\\FormatException' => $vendorDir . '/codeigniter4/framework/system/Format/Exceptions/FormatException.php',
    'CodeIgniter\\Format\\Format' => $vendorDir . '/codeigniter4/framework/system/Format/Format.php',
    'CodeIgniter\\Format\\FormatterInterface' => $vendorDir . '/codeigniter4/framework/system/Format/FormatterInterface.php',
    'CodeIgniter\\Format\\JSONFormatter' => $vendorDir . '/codeigniter4/framework/system/Format/JSONFormatter.php',
    'CodeIgniter\\Format\\XMLFormatter' => $vendorDir . '/codeigniter4/framework/system/Format/XMLFormatter.php',
    'CodeIgniter\\HTTP\\CLIRequest' => $vendorDir . '/codeigniter4/framework/system/HTTP/CLIRequest.php',
    'CodeIgniter\\HTTP\\CURLRequest' => $vendorDir . '/codeigniter4/framework/system/HTTP/CURLRequest.php',
    'CodeIgniter\\HTTP\\ContentSecurityPolicy' => $vendorDir . '/codeigniter4/framework/system/HTTP/ContentSecurityPolicy.php',
    'CodeIgniter\\HTTP\\Cors' => $vendorDir . '/codeigniter4/framework/system/HTTP/Cors.php',
    'CodeIgniter\\HTTP\\DownloadResponse' => $vendorDir . '/codeigniter4/framework/system/HTTP/DownloadResponse.php',
    'CodeIgniter\\HTTP\\Exceptions\\BadRequestException' => $vendorDir . '/codeigniter4/framework/system/HTTP/Exceptions/BadRequestException.php',
    'CodeIgniter\\HTTP\\Exceptions\\ExceptionInterface' => $vendorDir . '/codeigniter4/framework/system/HTTP/Exceptions/ExceptionInterface.php',
    'CodeIgniter\\HTTP\\Exceptions\\HTTPException' => $vendorDir . '/codeigniter4/framework/system/HTTP/Exceptions/HTTPException.php',
    'CodeIgniter\\HTTP\\Exceptions\\RedirectException' => $vendorDir . '/codeigniter4/framework/system/HTTP/Exceptions/RedirectException.php',
    'CodeIgniter\\HTTP\\Files\\FileCollection' => $vendorDir . '/codeigniter4/framework/system/HTTP/Files/FileCollection.php',
    'CodeIgniter\\HTTP\\Files\\UploadedFile' => $vendorDir . '/codeigniter4/framework/system/HTTP/Files/UploadedFile.php',
    'CodeIgniter\\HTTP\\Files\\UploadedFileInterface' => $vendorDir . '/codeigniter4/framework/system/HTTP/Files/UploadedFileInterface.php',
    'CodeIgniter\\HTTP\\Header' => $vendorDir . '/codeigniter4/framework/system/HTTP/Header.php',
    'CodeIgniter\\HTTP\\IncomingRequest' => $vendorDir . '/codeigniter4/framework/system/HTTP/IncomingRequest.php',
    'CodeIgniter\\HTTP\\Message' => $vendorDir . '/codeigniter4/framework/system/HTTP/Message.php',
    'CodeIgniter\\HTTP\\MessageInterface' => $vendorDir . '/codeigniter4/framework/system/HTTP/MessageInterface.php',
    'CodeIgniter\\HTTP\\MessageTrait' => $vendorDir . '/codeigniter4/framework/system/HTTP/MessageTrait.php',
    'CodeIgniter\\HTTP\\Method' => $vendorDir . '/codeigniter4/framework/system/HTTP/Method.php',
    'CodeIgniter\\HTTP\\Negotiate' => $vendorDir . '/codeigniter4/framework/system/HTTP/Negotiate.php',
    'CodeIgniter\\HTTP\\OutgoingRequest' => $vendorDir . '/codeigniter4/framework/system/HTTP/OutgoingRequest.php',
    'CodeIgniter\\HTTP\\OutgoingRequestInterface' => $vendorDir . '/codeigniter4/framework/system/HTTP/OutgoingRequestInterface.php',
    'CodeIgniter\\HTTP\\RedirectResponse' => $vendorDir . '/codeigniter4/framework/system/HTTP/RedirectResponse.php',
    'CodeIgniter\\HTTP\\Request' => $vendorDir . '/codeigniter4/framework/system/HTTP/Request.php',
    'CodeIgniter\\HTTP\\RequestInterface' => $vendorDir . '/codeigniter4/framework/system/HTTP/RequestInterface.php',
    'CodeIgniter\\HTTP\\RequestTrait' => $vendorDir . '/codeigniter4/framework/system/HTTP/RequestTrait.php',
    'CodeIgniter\\HTTP\\ResponsableInterface' => $vendorDir . '/codeigniter4/framework/system/HTTP/ResponsableInterface.php',
    'CodeIgniter\\HTTP\\Response' => $vendorDir . '/codeigniter4/framework/system/HTTP/Response.php',
    'CodeIgniter\\HTTP\\ResponseInterface' => $vendorDir . '/codeigniter4/framework/system/HTTP/ResponseInterface.php',
    'CodeIgniter\\HTTP\\ResponseTrait' => $vendorDir . '/codeigniter4/framework/system/HTTP/ResponseTrait.php',
    'CodeIgniter\\HTTP\\SiteURI' => $vendorDir . '/codeigniter4/framework/system/HTTP/SiteURI.php',
    'CodeIgniter\\HTTP\\SiteURIFactory' => $vendorDir . '/codeigniter4/framework/system/HTTP/SiteURIFactory.php',
    'CodeIgniter\\HTTP\\URI' => $vendorDir . '/codeigniter4/framework/system/HTTP/URI.php',
    'CodeIgniter\\HTTP\\UserAgent' => $vendorDir . '/codeigniter4/framework/system/HTTP/UserAgent.php',
    'CodeIgniter\\Helpers\\Array\\ArrayHelper' => $vendorDir . '/codeigniter4/framework/system/Helpers/Array/ArrayHelper.php',
    'CodeIgniter\\Honeypot\\Exceptions\\HoneypotException' => $vendorDir . '/codeigniter4/framework/system/Honeypot/Exceptions/HoneypotException.php',
    'CodeIgniter\\Honeypot\\Honeypot' => $vendorDir . '/codeigniter4/framework/system/Honeypot/Honeypot.php',
    'CodeIgniter\\HotReloader\\DirectoryHasher' => $vendorDir . '/codeigniter4/framework/system/HotReloader/DirectoryHasher.php',
    'CodeIgniter\\HotReloader\\HotReloader' => $vendorDir . '/codeigniter4/framework/system/HotReloader/HotReloader.php',
    'CodeIgniter\\HotReloader\\IteratorFilter' => $vendorDir . '/codeigniter4/framework/system/HotReloader/IteratorFilter.php',
    'CodeIgniter\\I18n\\Exceptions\\I18nException' => $vendorDir . '/codeigniter4/framework/system/I18n/Exceptions/I18nException.php',
    'CodeIgniter\\I18n\\Time' => $vendorDir . '/codeigniter4/framework/system/I18n/Time.php',
    'CodeIgniter\\I18n\\TimeDifference' => $vendorDir . '/codeigniter4/framework/system/I18n/TimeDifference.php',
    'CodeIgniter\\I18n\\TimeLegacy' => $vendorDir . '/codeigniter4/framework/system/I18n/TimeLegacy.php',
    'CodeIgniter\\I18n\\TimeTrait' => $vendorDir . '/codeigniter4/framework/system/I18n/TimeTrait.php',
    'CodeIgniter\\Images\\Exceptions\\ImageException' => $vendorDir . '/codeigniter4/framework/system/Images/Exceptions/ImageException.php',
    'CodeIgniter\\Images\\Handlers\\BaseHandler' => $vendorDir . '/codeigniter4/framework/system/Images/Handlers/BaseHandler.php',
    'CodeIgniter\\Images\\Handlers\\GDHandler' => $vendorDir . '/codeigniter4/framework/system/Images/Handlers/GDHandler.php',
    'CodeIgniter\\Images\\Handlers\\ImageMagickHandler' => $vendorDir . '/codeigniter4/framework/system/Images/Handlers/ImageMagickHandler.php',
    'CodeIgniter\\Images\\Image' => $vendorDir . '/codeigniter4/framework/system/Images/Image.php',
    'CodeIgniter\\Images\\ImageHandlerInterface' => $vendorDir . '/codeigniter4/framework/system/Images/ImageHandlerInterface.php',
    'CodeIgniter\\Language\\Language' => $vendorDir . '/codeigniter4/framework/system/Language/Language.php',
    'CodeIgniter\\Log\\Exceptions\\LogException' => $vendorDir . '/codeigniter4/framework/system/Log/Exceptions/LogException.php',
    'CodeIgniter\\Log\\Handlers\\BaseHandler' => $vendorDir . '/codeigniter4/framework/system/Log/Handlers/BaseHandler.php',
    'CodeIgniter\\Log\\Handlers\\ChromeLoggerHandler' => $vendorDir . '/codeigniter4/framework/system/Log/Handlers/ChromeLoggerHandler.php',
    'CodeIgniter\\Log\\Handlers\\ErrorlogHandler' => $vendorDir . '/codeigniter4/framework/system/Log/Handlers/ErrorlogHandler.php',
    'CodeIgniter\\Log\\Handlers\\FileHandler' => $vendorDir . '/codeigniter4/framework/system/Log/Handlers/FileHandler.php',
    'CodeIgniter\\Log\\Handlers\\HandlerInterface' => $vendorDir . '/codeigniter4/framework/system/Log/Handlers/HandlerInterface.php',
    'CodeIgniter\\Log\\Logger' => $vendorDir . '/codeigniter4/framework/system/Log/Logger.php',
    'CodeIgniter\\Model' => $vendorDir . '/codeigniter4/framework/system/Model.php',
    'CodeIgniter\\Modules\\Modules' => $vendorDir . '/codeigniter4/framework/system/Modules/Modules.php',
    'CodeIgniter\\Pager\\Exceptions\\PagerException' => $vendorDir . '/codeigniter4/framework/system/Pager/Exceptions/PagerException.php',
    'CodeIgniter\\Pager\\Pager' => $vendorDir . '/codeigniter4/framework/system/Pager/Pager.php',
    'CodeIgniter\\Pager\\PagerInterface' => $vendorDir . '/codeigniter4/framework/system/Pager/PagerInterface.php',
    'CodeIgniter\\Pager\\PagerRenderer' => $vendorDir . '/codeigniter4/framework/system/Pager/PagerRenderer.php',
    'CodeIgniter\\Publisher\\ContentReplacer' => $vendorDir . '/codeigniter4/framework/system/Publisher/ContentReplacer.php',
    'CodeIgniter\\Publisher\\Exceptions\\PublisherException' => $vendorDir . '/codeigniter4/framework/system/Publisher/Exceptions/PublisherException.php',
    'CodeIgniter\\Publisher\\Publisher' => $vendorDir . '/codeigniter4/framework/system/Publisher/Publisher.php',
    'CodeIgniter\\RESTful\\BaseResource' => $vendorDir . '/codeigniter4/framework/system/RESTful/BaseResource.php',
    'CodeIgniter\\RESTful\\ResourceController' => $vendorDir . '/codeigniter4/framework/system/RESTful/ResourceController.php',
    'CodeIgniter\\RESTful\\ResourcePresenter' => $vendorDir . '/codeigniter4/framework/system/RESTful/ResourcePresenter.php',
    'CodeIgniter\\Router\\AutoRouter' => $vendorDir . '/codeigniter4/framework/system/Router/AutoRouter.php',
    'CodeIgniter\\Router\\AutoRouterImproved' => $vendorDir . '/codeigniter4/framework/system/Router/AutoRouterImproved.php',
    'CodeIgniter\\Router\\AutoRouterInterface' => $vendorDir . '/codeigniter4/framework/system/Router/AutoRouterInterface.php',
    'CodeIgniter\\Router\\DefinedRouteCollector' => $vendorDir . '/codeigniter4/framework/system/Router/DefinedRouteCollector.php',
    'CodeIgniter\\Router\\Exceptions\\ExceptionInterface' => $vendorDir . '/codeigniter4/framework/system/Router/Exceptions/ExceptionInterface.php',
    'CodeIgniter\\Router\\Exceptions\\MethodNotFoundException' => $vendorDir . '/codeigniter4/framework/system/Router/Exceptions/MethodNotFoundException.php',
    'CodeIgniter\\Router\\Exceptions\\RouterException' => $vendorDir . '/codeigniter4/framework/system/Router/Exceptions/RouterException.php',
    'CodeIgniter\\Router\\RouteCollection' => $vendorDir . '/codeigniter4/framework/system/Router/RouteCollection.php',
    'CodeIgniter\\Router\\RouteCollectionInterface' => $vendorDir . '/codeigniter4/framework/system/Router/RouteCollectionInterface.php',
    'CodeIgniter\\Router\\Router' => $vendorDir . '/codeigniter4/framework/system/Router/Router.php',
    'CodeIgniter\\Router\\RouterInterface' => $vendorDir . '/codeigniter4/framework/system/Router/RouterInterface.php',
    'CodeIgniter\\Security\\CheckPhpIni' => $vendorDir . '/codeigniter4/framework/system/Security/CheckPhpIni.php',
    'CodeIgniter\\Security\\Exceptions\\SecurityException' => $vendorDir . '/codeigniter4/framework/system/Security/Exceptions/SecurityException.php',
    'CodeIgniter\\Security\\Security' => $vendorDir . '/codeigniter4/framework/system/Security/Security.php',
    'CodeIgniter\\Security\\SecurityInterface' => $vendorDir . '/codeigniter4/framework/system/Security/SecurityInterface.php',
    'CodeIgniter\\Session\\Exceptions\\SessionException' => $vendorDir . '/codeigniter4/framework/system/Session/Exceptions/SessionException.php',
    'CodeIgniter\\Session\\Handlers\\ArrayHandler' => $vendorDir . '/codeigniter4/framework/system/Session/Handlers/ArrayHandler.php',
    'CodeIgniter\\Session\\Handlers\\BaseHandler' => $vendorDir . '/codeigniter4/framework/system/Session/Handlers/BaseHandler.php',
    'CodeIgniter\\Session\\Handlers\\DatabaseHandler' => $vendorDir . '/codeigniter4/framework/system/Session/Handlers/DatabaseHandler.php',
    'CodeIgniter\\Session\\Handlers\\Database\\MySQLiHandler' => $vendorDir . '/codeigniter4/framework/system/Session/Handlers/Database/MySQLiHandler.php',
    'CodeIgniter\\Session\\Handlers\\Database\\PostgreHandler' => $vendorDir . '/codeigniter4/framework/system/Session/Handlers/Database/PostgreHandler.php',
    'CodeIgniter\\Session\\Handlers\\FileHandler' => $vendorDir . '/codeigniter4/framework/system/Session/Handlers/FileHandler.php',
    'CodeIgniter\\Session\\Handlers\\MemcachedHandler' => $vendorDir . '/codeigniter4/framework/system/Session/Handlers/MemcachedHandler.php',
    'CodeIgniter\\Session\\Handlers\\RedisHandler' => $vendorDir . '/codeigniter4/framework/system/Session/Handlers/RedisHandler.php',
    'CodeIgniter\\Session\\Session' => $vendorDir . '/codeigniter4/framework/system/Session/Session.php',
    'CodeIgniter\\Session\\SessionInterface' => $vendorDir . '/codeigniter4/framework/system/Session/SessionInterface.php',
    'CodeIgniter\\Superglobals' => $vendorDir . '/codeigniter4/framework/system/Superglobals.php',
    'CodeIgniter\\Test\\CIUnitTestCase' => $vendorDir . '/codeigniter4/framework/system/Test/CIUnitTestCase.php',
    'CodeIgniter\\Test\\ConfigFromArrayTrait' => $vendorDir . '/codeigniter4/framework/system/Test/ConfigFromArrayTrait.php',
    'CodeIgniter\\Test\\Constraints\\SeeInDatabase' => $vendorDir . '/codeigniter4/framework/system/Test/Constraints/SeeInDatabase.php',
    'CodeIgniter\\Test\\ControllerTestTrait' => $vendorDir . '/codeigniter4/framework/system/Test/ControllerTestTrait.php',
    'CodeIgniter\\Test\\DOMParser' => $vendorDir . '/codeigniter4/framework/system/Test/DOMParser.php',
    'CodeIgniter\\Test\\DatabaseTestTrait' => $vendorDir . '/codeigniter4/framework/system/Test/DatabaseTestTrait.php',
    'CodeIgniter\\Test\\Fabricator' => $vendorDir . '/codeigniter4/framework/system/Test/Fabricator.php',
    'CodeIgniter\\Test\\FeatureTestTrait' => $vendorDir . '/codeigniter4/framework/system/Test/FeatureTestTrait.php',
    'CodeIgniter\\Test\\FilterTestTrait' => $vendorDir . '/codeigniter4/framework/system/Test/FilterTestTrait.php',
    'CodeIgniter\\Test\\Filters\\CITestStreamFilter' => $vendorDir . '/codeigniter4/framework/system/Test/Filters/CITestStreamFilter.php',
    'CodeIgniter\\Test\\IniTestTrait' => $vendorDir . '/codeigniter4/framework/system/Test/IniTestTrait.php',
    'CodeIgniter\\Test\\Interfaces\\FabricatorModel' => $vendorDir . '/codeigniter4/framework/system/Test/Interfaces/FabricatorModel.php',
    'CodeIgniter\\Test\\Mock\\MockAppConfig' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockAppConfig.php',
    'CodeIgniter\\Test\\Mock\\MockAutoload' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockAutoload.php',
    'CodeIgniter\\Test\\Mock\\MockBuilder' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockBuilder.php',
    'CodeIgniter\\Test\\Mock\\MockCLIConfig' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockCLIConfig.php',
    'CodeIgniter\\Test\\Mock\\MockCURLRequest' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockCURLRequest.php',
    'CodeIgniter\\Test\\Mock\\MockCache' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockCache.php',
    'CodeIgniter\\Test\\Mock\\MockCodeIgniter' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockCodeIgniter.php',
    'CodeIgniter\\Test\\Mock\\MockConnection' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockConnection.php',
    'CodeIgniter\\Test\\Mock\\MockEmail' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockEmail.php',
    'CodeIgniter\\Test\\Mock\\MockEvents' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockEvents.php',
    'CodeIgniter\\Test\\Mock\\MockFileLogger' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockFileLogger.php',
    'CodeIgniter\\Test\\Mock\\MockIncomingRequest' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockIncomingRequest.php',
    'CodeIgniter\\Test\\Mock\\MockInputOutput' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockInputOutput.php',
    'CodeIgniter\\Test\\Mock\\MockLanguage' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockLanguage.php',
    'CodeIgniter\\Test\\Mock\\MockLogger' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockLogger.php',
    'CodeIgniter\\Test\\Mock\\MockQuery' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockQuery.php',
    'CodeIgniter\\Test\\Mock\\MockResourceController' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockResourceController.php',
    'CodeIgniter\\Test\\Mock\\MockResourcePresenter' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockResourcePresenter.php',
    'CodeIgniter\\Test\\Mock\\MockResponse' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockResponse.php',
    'CodeIgniter\\Test\\Mock\\MockResult' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockResult.php',
    'CodeIgniter\\Test\\Mock\\MockSecurity' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockSecurity.php',
    'CodeIgniter\\Test\\Mock\\MockServices' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockServices.php',
    'CodeIgniter\\Test\\Mock\\MockSession' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockSession.php',
    'CodeIgniter\\Test\\Mock\\MockTable' => $vendorDir . '/codeigniter4/framework/system/Test/Mock/MockTable.php',
    'CodeIgniter\\Test\\PhpStreamWrapper' => $vendorDir . '/codeigniter4/framework/system/Test/PhpStreamWrapper.php',
    'CodeIgniter\\Test\\ReflectionHelper' => $vendorDir . '/codeigniter4/framework/system/Test/ReflectionHelper.php',
    'CodeIgniter\\Test\\StreamFilterTrait' => $vendorDir . '/codeigniter4/framework/system/Test/StreamFilterTrait.php',
    'CodeIgniter\\Test\\TestLogger' => $vendorDir . '/codeigniter4/framework/system/Test/TestLogger.php',
    'CodeIgniter\\Test\\TestResponse' => $vendorDir . '/codeigniter4/framework/system/Test/TestResponse.php',
    'CodeIgniter\\Throttle\\Throttler' => $vendorDir . '/codeigniter4/framework/system/Throttle/Throttler.php',
    'CodeIgniter\\Throttle\\ThrottlerInterface' => $vendorDir . '/codeigniter4/framework/system/Throttle/ThrottlerInterface.php',
    'CodeIgniter\\Traits\\ConditionalTrait' => $vendorDir . '/codeigniter4/framework/system/Traits/ConditionalTrait.php',
    'CodeIgniter\\Traits\\PropertiesTrait' => $vendorDir . '/codeigniter4/framework/system/Traits/PropertiesTrait.php',
    'CodeIgniter\\Typography\\Typography' => $vendorDir . '/codeigniter4/framework/system/Typography/Typography.php',
    'CodeIgniter\\Validation\\CreditCardRules' => $vendorDir . '/codeigniter4/framework/system/Validation/CreditCardRules.php',
    'CodeIgniter\\Validation\\DotArrayFilter' => $vendorDir . '/codeigniter4/framework/system/Validation/DotArrayFilter.php',
    'CodeIgniter\\Validation\\Exceptions\\ValidationException' => $vendorDir . '/codeigniter4/framework/system/Validation/Exceptions/ValidationException.php',
    'CodeIgniter\\Validation\\FileRules' => $vendorDir . '/codeigniter4/framework/system/Validation/FileRules.php',
    'CodeIgniter\\Validation\\FormatRules' => $vendorDir . '/codeigniter4/framework/system/Validation/FormatRules.php',
    'CodeIgniter\\Validation\\Rules' => $vendorDir . '/codeigniter4/framework/system/Validation/Rules.php',
    'CodeIgniter\\Validation\\StrictRules\\CreditCardRules' => $vendorDir . '/codeigniter4/framework/system/Validation/StrictRules/CreditCardRules.php',
    'CodeIgniter\\Validation\\StrictRules\\FileRules' => $vendorDir . '/codeigniter4/framework/system/Validation/StrictRules/FileRules.php',
    'CodeIgniter\\Validation\\StrictRules\\FormatRules' => $vendorDir . '/codeigniter4/framework/system/Validation/StrictRules/FormatRules.php',
    'CodeIgniter\\Validation\\StrictRules\\Rules' => $vendorDir . '/codeigniter4/framework/system/Validation/StrictRules/Rules.php',
    'CodeIgniter\\Validation\\Validation' => $vendorDir . '/codeigniter4/framework/system/Validation/Validation.php',
    'CodeIgniter\\Validation\\ValidationInterface' => $vendorDir . '/codeigniter4/framework/system/Validation/ValidationInterface.php',
    'CodeIgniter\\View\\Cell' => $vendorDir . '/codeigniter4/framework/system/View/Cell.php',
    'CodeIgniter\\View\\Cells\\Cell' => $vendorDir . '/codeigniter4/framework/system/View/Cells/Cell.php',
    'CodeIgniter\\View\\Exceptions\\ViewException' => $vendorDir . '/codeigniter4/framework/system/View/Exceptions/ViewException.php',
    'CodeIgniter\\View\\Filters' => $vendorDir . '/codeigniter4/framework/system/View/Filters.php',
    'CodeIgniter\\View\\Parser' => $vendorDir . '/codeigniter4/framework/system/View/Parser.php',
    'CodeIgniter\\View\\Plugins' => $vendorDir . '/codeigniter4/framework/system/View/Plugins.php',
    'CodeIgniter\\View\\RendererInterface' => $vendorDir . '/codeigniter4/framework/system/View/RendererInterface.php',
    'CodeIgniter\\View\\Table' => $vendorDir . '/codeigniter4/framework/system/View/Table.php',
    'CodeIgniter\\View\\View' => $vendorDir . '/codeigniter4/framework/system/View/View.php',
    'CodeIgniter\\View\\ViewDecoratorInterface' => $vendorDir . '/codeigniter4/framework/system/View/ViewDecoratorInterface.php',
    'CodeIgniter\\View\\ViewDecoratorTrait' => $vendorDir . '/codeigniter4/framework/system/View/ViewDecoratorTrait.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Config\\App' => $baseDir . '/app/Config/App.php',
    'Config\\Autoload' => $baseDir . '/app/Config/Autoload.php',
    'Config\\CURLRequest' => $baseDir . '/app/Config/CURLRequest.php',
    'Config\\Cache' => $baseDir . '/app/Config/Cache.php',
    'Config\\ContentSecurityPolicy' => $baseDir . '/app/Config/ContentSecurityPolicy.php',
    'Config\\Cookie' => $baseDir . '/app/Config/Cookie.php',
    'Config\\Cors' => $baseDir . '/app/Config/Cors.php',
    'Config\\Database' => $baseDir . '/app/Config/Database.php',
    'Config\\DocTypes' => $baseDir . '/app/Config/DocTypes.php',
    'Config\\Email' => $baseDir . '/app/Config/Email.php',
    'Config\\Encryption' => $baseDir . '/app/Config/Encryption.php',
    'Config\\Exceptions' => $baseDir . '/app/Config/Exceptions.php',
    'Config\\Feature' => $baseDir . '/app/Config/Feature.php',
    'Config\\Filters' => $baseDir . '/app/Config/Filters.php',
    'Config\\ForeignCharacters' => $baseDir . '/app/Config/ForeignCharacters.php',
    'Config\\Format' => $baseDir . '/app/Config/Format.php',
    'Config\\Generators' => $baseDir . '/app/Config/Generators.php',
    'Config\\Honeypot' => $baseDir . '/app/Config/Honeypot.php',
    'Config\\Images' => $baseDir . '/app/Config/Images.php',
    'Config\\Kint' => $baseDir . '/app/Config/Kint.php',
    'Config\\Logger' => $baseDir . '/app/Config/Logger.php',
    'Config\\Migrations' => $baseDir . '/app/Config/Migrations.php',
    'Config\\Mimes' => $baseDir . '/app/Config/Mimes.php',
    'Config\\Modules' => $baseDir . '/app/Config/Modules.php',
    'Config\\Optimize' => $baseDir . '/app/Config/Optimize.php',
    'Config\\Pager' => $baseDir . '/app/Config/Pager.php',
    'Config\\Paths' => $baseDir . '/app/Config/Paths.php',
    'Config\\Publisher' => $baseDir . '/app/Config/Publisher.php',
    'Config\\Routing' => $baseDir . '/app/Config/Routing.php',
    'Config\\Security' => $baseDir . '/app/Config/Security.php',
    'Config\\Services' => $baseDir . '/app/Config/Services.php',
    'Config\\Session' => $baseDir . '/app/Config/Session.php',
    'Config\\Toolbar' => $baseDir . '/app/Config/Toolbar.php',
    'Config\\UserAgents' => $baseDir . '/app/Config/UserAgents.php',
    'Config\\Validation' => $baseDir . '/app/Config/Validation.php',
    'Config\\View' => $baseDir . '/app/Config/View.php',
    'DeepCopy\\DeepCopy' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/DeepCopy.php',
    'DeepCopy\\Exception\\CloneException' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Exception/CloneException.php',
    'DeepCopy\\Exception\\PropertyException' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Exception/PropertyException.php',
    'DeepCopy\\Filter\\ChainableFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/ChainableFilter.php',
    'DeepCopy\\Filter\\Doctrine\\DoctrineCollectionFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/Doctrine/DoctrineCollectionFilter.php',
    'DeepCopy\\Filter\\Doctrine\\DoctrineEmptyCollectionFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/Doctrine/DoctrineEmptyCollectionFilter.php',
    'DeepCopy\\Filter\\Doctrine\\DoctrineProxyFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/Doctrine/DoctrineProxyFilter.php',
    'DeepCopy\\Filter\\Filter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/Filter.php',
    'DeepCopy\\Filter\\KeepFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/KeepFilter.php',
    'DeepCopy\\Filter\\ReplaceFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/ReplaceFilter.php',
    'DeepCopy\\Filter\\SetNullFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/SetNullFilter.php',
    'DeepCopy\\Matcher\\Doctrine\\DoctrineProxyMatcher' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Matcher/Doctrine/DoctrineProxyMatcher.php',
    'DeepCopy\\Matcher\\Matcher' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Matcher/Matcher.php',
    'DeepCopy\\Matcher\\PropertyMatcher' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Matcher/PropertyMatcher.php',
    'DeepCopy\\Matcher\\PropertyNameMatcher' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Matcher/PropertyNameMatcher.php',
    'DeepCopy\\Matcher\\PropertyTypeMatcher' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Matcher/PropertyTypeMatcher.php',
    'DeepCopy\\Reflection\\ReflectionHelper' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Reflection/ReflectionHelper.php',
    'DeepCopy\\TypeFilter\\Date\\DateIntervalFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/Date/DateIntervalFilter.php',
    'DeepCopy\\TypeFilter\\Date\\DatePeriodFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/Date/DatePeriodFilter.php',
    'DeepCopy\\TypeFilter\\ReplaceFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/ReplaceFilter.php',
    'DeepCopy\\TypeFilter\\ShallowCopyFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/ShallowCopyFilter.php',
    'DeepCopy\\TypeFilter\\Spl\\ArrayObjectFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/Spl/ArrayObjectFilter.php',
    'DeepCopy\\TypeFilter\\Spl\\SplDoublyLinkedList' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/Spl/SplDoublyLinkedList.php',
    'DeepCopy\\TypeFilter\\Spl\\SplDoublyLinkedListFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/Spl/SplDoublyLinkedListFilter.php',
    'DeepCopy\\TypeFilter\\TypeFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/TypeFilter.php',
    'DeepCopy\\TypeMatcher\\TypeMatcher' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeMatcher/TypeMatcher.php',
    'Faker\\Calculator\\Ean' => $vendorDir . '/fakerphp/faker/src/Faker/Calculator/Ean.php',
    'Faker\\Calculator\\Iban' => $vendorDir . '/fakerphp/faker/src/Faker/Calculator/Iban.php',
    'Faker\\Calculator\\Inn' => $vendorDir . '/fakerphp/faker/src/Faker/Calculator/Inn.php',
    'Faker\\Calculator\\Isbn' => $vendorDir . '/fakerphp/faker/src/Faker/Calculator/Isbn.php',
    'Faker\\Calculator\\Luhn' => $vendorDir . '/fakerphp/faker/src/Faker/Calculator/Luhn.php',
    'Faker\\Calculator\\TCNo' => $vendorDir . '/fakerphp/faker/src/Faker/Calculator/TCNo.php',
    'Faker\\ChanceGenerator' => $vendorDir . '/fakerphp/faker/src/Faker/ChanceGenerator.php',
    'Faker\\Container\\Container' => $vendorDir . '/fakerphp/faker/src/Faker/Container/Container.php',
    'Faker\\Container\\ContainerBuilder' => $vendorDir . '/fakerphp/faker/src/Faker/Container/ContainerBuilder.php',
    'Faker\\Container\\ContainerException' => $vendorDir . '/fakerphp/faker/src/Faker/Container/ContainerException.php',
    'Faker\\Container\\ContainerInterface' => $vendorDir . '/fakerphp/faker/src/Faker/Container/ContainerInterface.php',
    'Faker\\Container\\NotInContainerException' => $vendorDir . '/fakerphp/faker/src/Faker/Container/NotInContainerException.php',
    'Faker\\Core\\Barcode' => $vendorDir . '/fakerphp/faker/src/Faker/Core/Barcode.php',
    'Faker\\Core\\Blood' => $vendorDir . '/fakerphp/faker/src/Faker/Core/Blood.php',
    'Faker\\Core\\Color' => $vendorDir . '/fakerphp/faker/src/Faker/Core/Color.php',
    'Faker\\Core\\Coordinates' => $vendorDir . '/fakerphp/faker/src/Faker/Core/Coordinates.php',
    'Faker\\Core\\DateTime' => $vendorDir . '/fakerphp/faker/src/Faker/Core/DateTime.php',
    'Faker\\Core\\File' => $vendorDir . '/fakerphp/faker/src/Faker/Core/File.php',
    'Faker\\Core\\Number' => $vendorDir . '/fakerphp/faker/src/Faker/Core/Number.php',
    'Faker\\Core\\Uuid' => $vendorDir . '/fakerphp/faker/src/Faker/Core/Uuid.php',
    'Faker\\Core\\Version' => $vendorDir . '/fakerphp/faker/src/Faker/Core/Version.php',
    'Faker\\DefaultGenerator' => $vendorDir . '/fakerphp/faker/src/Faker/DefaultGenerator.php',
    'Faker\\Documentor' => $vendorDir . '/fakerphp/faker/src/Faker/Documentor.php',
    'Faker\\Extension\\AddressExtension' => $vendorDir . '/fakerphp/faker/src/Faker/Extension/AddressExtension.php',
    'Faker\\Extension\\BarcodeExtension' => $vendorDir . '/fakerphp/faker/src/Faker/Extension/BarcodeExtension.php',
    'Faker\\Extension\\BloodExtension' => $vendorDir . '/fakerphp/faker/src/Faker/Extension/BloodExtension.php',
    'Faker\\Extension\\ColorExtension' => $vendorDir . '/fakerphp/faker/src/Faker/Extension/ColorExtension.php',
    'Faker\\Extension\\CompanyExtension' => $vendorDir . '/fakerphp/faker/src/Faker/Extension/CompanyExtension.php',
    'Faker\\Extension\\CountryExtension' => $vendorDir . '/fakerphp/faker/src/Faker/Extension/CountryExtension.php',
    'Faker\\Extension\\DateTimeExtension' => $vendorDir . '/fakerphp/faker/src/Faker/Extension/DateTimeExtension.php',
    'Faker\\Extension\\Extension' => $vendorDir . '/fakerphp/faker/src/Faker/Extension/Extension.php',
    'Faker\\Extension\\ExtensionNotFound' => $vendorDir . '/fakerphp/faker/src/Faker/Extension/ExtensionNotFound.php',
    'Faker\\Extension\\FileExtension' => $vendorDir . '/fakerphp/faker/src/Faker/Extension/FileExtension.php',
    'Faker\\Extension\\GeneratorAwareExtension' => $vendorDir . '/fakerphp/faker/src/Faker/Extension/GeneratorAwareExtension.php',
    'Faker\\Extension\\GeneratorAwareExtensionTrait' => $vendorDir . '/fakerphp/faker/src/Faker/Extension/GeneratorAwareExtensionTrait.php',
    'Faker\\Extension\\Helper' => $vendorDir . '/fakerphp/faker/src/Faker/Extension/Helper.php',
    'Faker\\Extension\\NumberExtension' => $vendorDir . '/fakerphp/faker/src/Faker/Extension/NumberExtension.php',
    'Faker\\Extension\\PersonExtension' => $vendorDir . '/fakerphp/faker/src/Faker/Extension/PersonExtension.php',
    'Faker\\Extension\\PhoneNumberExtension' => $vendorDir . '/fakerphp/faker/src/Faker/Extension/PhoneNumberExtension.php',
    'Faker\\Extension\\UuidExtension' => $vendorDir . '/fakerphp/faker/src/Faker/Extension/UuidExtension.php',
    'Faker\\Extension\\VersionExtension' => $vendorDir . '/fakerphp/faker/src/Faker/Extension/VersionExtension.php',
    'Faker\\Factory' => $vendorDir . '/fakerphp/faker/src/Faker/Factory.php',
    'Faker\\Generator' => $vendorDir . '/fakerphp/faker/src/Faker/Generator.php',
    'Faker\\Guesser\\Name' => $vendorDir . '/fakerphp/faker/src/Faker/Guesser/Name.php',
    'Faker\\ORM\\CakePHP\\ColumnTypeGuesser' => $vendorDir . '/fakerphp/faker/src/Faker/ORM/CakePHP/ColumnTypeGuesser.php',
    'Faker\\ORM\\CakePHP\\EntityPopulator' => $vendorDir . '/fakerphp/faker/src/Faker/ORM/CakePHP/EntityPopulator.php',
    'Faker\\ORM\\CakePHP\\Populator' => $vendorDir . '/fakerphp/faker/src/Faker/ORM/CakePHP/Populator.php',
    'Faker\\ORM\\Doctrine\\ColumnTypeGuesser' => $vendorDir . '/fakerphp/faker/src/Faker/ORM/Doctrine/ColumnTypeGuesser.php',
    'Faker\\ORM\\Doctrine\\EntityPopulator' => $vendorDir . '/fakerphp/faker/src/Faker/ORM/Doctrine/EntityPopulator.php',
    'Faker\\ORM\\Doctrine\\Populator' => $vendorDir . '/fakerphp/faker/src/Faker/ORM/Doctrine/Populator.php',
    'Faker\\ORM\\Mandango\\ColumnTypeGuesser' => $vendorDir . '/fakerphp/faker/src/Faker/ORM/Mandango/ColumnTypeGuesser.php',
    'Faker\\ORM\\Mandango\\EntityPopulator' => $vendorDir . '/fakerphp/faker/src/Faker/ORM/Mandango/EntityPopulator.php',
    'Faker\\ORM\\Mandango\\Populator' => $vendorDir . '/fakerphp/faker/src/Faker/ORM/Mandango/Populator.php',
    'Faker\\ORM\\Propel2\\ColumnTypeGuesser' => $vendorDir . '/fakerphp/faker/src/Faker/ORM/Propel2/ColumnTypeGuesser.php',
    'Faker\\ORM\\Propel2\\EntityPopulator' => $vendorDir . '/fakerphp/faker/src/Faker/ORM/Propel2/EntityPopulator.php',
    'Faker\\ORM\\Propel2\\Populator' => $vendorDir . '/fakerphp/faker/src/Faker/ORM/Propel2/Populator.php',
    'Faker\\ORM\\Propel\\ColumnTypeGuesser' => $vendorDir . '/fakerphp/faker/src/Faker/ORM/Propel/ColumnTypeGuesser.php',
    'Faker\\ORM\\Propel\\EntityPopulator' => $vendorDir . '/fakerphp/faker/src/Faker/ORM/Propel/EntityPopulator.php',
    'Faker\\ORM\\Propel\\Populator' => $vendorDir . '/fakerphp/faker/src/Faker/ORM/Propel/Populator.php',
    'Faker\\ORM\\Spot\\ColumnTypeGuesser' => $vendorDir . '/fakerphp/faker/src/Faker/ORM/Spot/ColumnTypeGuesser.php',
    'Faker\\ORM\\Spot\\EntityPopulator' => $vendorDir . '/fakerphp/faker/src/Faker/ORM/Spot/EntityPopulator.php',
    'Faker\\ORM\\Spot\\Populator' => $vendorDir . '/fakerphp/faker/src/Faker/ORM/Spot/Populator.php',
    'Faker\\Provider\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/Address.php',
    'Faker\\Provider\\Barcode' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/Barcode.php',
    'Faker\\Provider\\Base' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/Base.php',
    'Faker\\Provider\\Biased' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/Biased.php',
    'Faker\\Provider\\Color' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/Color.php',
    'Faker\\Provider\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/Company.php',
    'Faker\\Provider\\DateTime' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/DateTime.php',
    'Faker\\Provider\\File' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/File.php',
    'Faker\\Provider\\HtmlLorem' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/HtmlLorem.php',
    'Faker\\Provider\\Image' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/Image.php',
    'Faker\\Provider\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/Internet.php',
    'Faker\\Provider\\Lorem' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/Lorem.php',
    'Faker\\Provider\\Medical' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/Medical.php',
    'Faker\\Provider\\Miscellaneous' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/Miscellaneous.php',
    'Faker\\Provider\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/Payment.php',
    'Faker\\Provider\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/Person.php',
    'Faker\\Provider\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/PhoneNumber.php',
    'Faker\\Provider\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/Text.php',
    'Faker\\Provider\\UserAgent' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/UserAgent.php',
    'Faker\\Provider\\Uuid' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/Uuid.php',
    'Faker\\Provider\\ar_EG\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ar_EG/Address.php',
    'Faker\\Provider\\ar_EG\\Color' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ar_EG/Color.php',
    'Faker\\Provider\\ar_EG\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ar_EG/Company.php',
    'Faker\\Provider\\ar_EG\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ar_EG/Internet.php',
    'Faker\\Provider\\ar_EG\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ar_EG/Payment.php',
    'Faker\\Provider\\ar_EG\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ar_EG/Person.php',
    'Faker\\Provider\\ar_EG\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ar_EG/Text.php',
    'Faker\\Provider\\ar_JO\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ar_JO/Address.php',
    'Faker\\Provider\\ar_JO\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ar_JO/Company.php',
    'Faker\\Provider\\ar_JO\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ar_JO/Internet.php',
    'Faker\\Provider\\ar_JO\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ar_JO/Person.php',
    'Faker\\Provider\\ar_JO\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ar_JO/Text.php',
    'Faker\\Provider\\ar_SA\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ar_SA/Address.php',
    'Faker\\Provider\\ar_SA\\Color' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ar_SA/Color.php',
    'Faker\\Provider\\ar_SA\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ar_SA/Company.php',
    'Faker\\Provider\\ar_SA\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ar_SA/Internet.php',
    'Faker\\Provider\\ar_SA\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ar_SA/Payment.php',
    'Faker\\Provider\\ar_SA\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ar_SA/Person.php',
    'Faker\\Provider\\ar_SA\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ar_SA/Text.php',
    'Faker\\Provider\\at_AT\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/at_AT/Payment.php',
    'Faker\\Provider\\bg_BG\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/bg_BG/Internet.php',
    'Faker\\Provider\\bg_BG\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/bg_BG/Payment.php',
    'Faker\\Provider\\bg_BG\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/bg_BG/Person.php',
    'Faker\\Provider\\bg_BG\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/bg_BG/PhoneNumber.php',
    'Faker\\Provider\\bn_BD\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/bn_BD/Address.php',
    'Faker\\Provider\\bn_BD\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/bn_BD/Company.php',
    'Faker\\Provider\\bn_BD\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/bn_BD/Person.php',
    'Faker\\Provider\\bn_BD\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/bn_BD/PhoneNumber.php',
    'Faker\\Provider\\bn_BD\\Utils' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/bn_BD/Utils.php',
    'Faker\\Provider\\cs_CZ\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/cs_CZ/Address.php',
    'Faker\\Provider\\cs_CZ\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/cs_CZ/Company.php',
    'Faker\\Provider\\cs_CZ\\DateTime' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/cs_CZ/DateTime.php',
    'Faker\\Provider\\cs_CZ\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/cs_CZ/Internet.php',
    'Faker\\Provider\\cs_CZ\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/cs_CZ/Payment.php',
    'Faker\\Provider\\cs_CZ\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/cs_CZ/Person.php',
    'Faker\\Provider\\cs_CZ\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/cs_CZ/PhoneNumber.php',
    'Faker\\Provider\\cs_CZ\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/cs_CZ/Text.php',
    'Faker\\Provider\\da_DK\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/da_DK/Address.php',
    'Faker\\Provider\\da_DK\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/da_DK/Company.php',
    'Faker\\Provider\\da_DK\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/da_DK/Internet.php',
    'Faker\\Provider\\da_DK\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/da_DK/Payment.php',
    'Faker\\Provider\\da_DK\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/da_DK/Person.php',
    'Faker\\Provider\\da_DK\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/da_DK/PhoneNumber.php',
    'Faker\\Provider\\de_AT\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/de_AT/Address.php',
    'Faker\\Provider\\de_AT\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/de_AT/Company.php',
    'Faker\\Provider\\de_AT\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/de_AT/Internet.php',
    'Faker\\Provider\\de_AT\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/de_AT/Payment.php',
    'Faker\\Provider\\de_AT\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/de_AT/Person.php',
    'Faker\\Provider\\de_AT\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/de_AT/PhoneNumber.php',
    'Faker\\Provider\\de_AT\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/de_AT/Text.php',
    'Faker\\Provider\\de_CH\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/de_CH/Address.php',
    'Faker\\Provider\\de_CH\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/de_CH/Company.php',
    'Faker\\Provider\\de_CH\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/de_CH/Internet.php',
    'Faker\\Provider\\de_CH\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/de_CH/Payment.php',
    'Faker\\Provider\\de_CH\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/de_CH/Person.php',
    'Faker\\Provider\\de_CH\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/de_CH/PhoneNumber.php',
    'Faker\\Provider\\de_CH\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/de_CH/Text.php',
    'Faker\\Provider\\de_DE\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/de_DE/Address.php',
    'Faker\\Provider\\de_DE\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/de_DE/Company.php',
    'Faker\\Provider\\de_DE\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/de_DE/Internet.php',
    'Faker\\Provider\\de_DE\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/de_DE/Payment.php',
    'Faker\\Provider\\de_DE\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/de_DE/Person.php',
    'Faker\\Provider\\de_DE\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/de_DE/PhoneNumber.php',
    'Faker\\Provider\\de_DE\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/de_DE/Text.php',
    'Faker\\Provider\\el_CY\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/el_CY/Address.php',
    'Faker\\Provider\\el_CY\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/el_CY/Company.php',
    'Faker\\Provider\\el_CY\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/el_CY/Internet.php',
    'Faker\\Provider\\el_CY\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/el_CY/Payment.php',
    'Faker\\Provider\\el_CY\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/el_CY/Person.php',
    'Faker\\Provider\\el_CY\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/el_CY/PhoneNumber.php',
    'Faker\\Provider\\el_GR\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/el_GR/Address.php',
    'Faker\\Provider\\el_GR\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/el_GR/Company.php',
    'Faker\\Provider\\el_GR\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/el_GR/Payment.php',
    'Faker\\Provider\\el_GR\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/el_GR/Person.php',
    'Faker\\Provider\\el_GR\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/el_GR/PhoneNumber.php',
    'Faker\\Provider\\el_GR\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/el_GR/Text.php',
    'Faker\\Provider\\en_AU\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_AU/Address.php',
    'Faker\\Provider\\en_AU\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_AU/Internet.php',
    'Faker\\Provider\\en_AU\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_AU/PhoneNumber.php',
    'Faker\\Provider\\en_CA\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_CA/Address.php',
    'Faker\\Provider\\en_CA\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_CA/PhoneNumber.php',
    'Faker\\Provider\\en_GB\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_GB/Address.php',
    'Faker\\Provider\\en_GB\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_GB/Company.php',
    'Faker\\Provider\\en_GB\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_GB/Internet.php',
    'Faker\\Provider\\en_GB\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_GB/Payment.php',
    'Faker\\Provider\\en_GB\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_GB/Person.php',
    'Faker\\Provider\\en_GB\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_GB/PhoneNumber.php',
    'Faker\\Provider\\en_HK\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_HK/Address.php',
    'Faker\\Provider\\en_HK\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_HK/Internet.php',
    'Faker\\Provider\\en_HK\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_HK/PhoneNumber.php',
    'Faker\\Provider\\en_IN\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_IN/Address.php',
    'Faker\\Provider\\en_IN\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_IN/Internet.php',
    'Faker\\Provider\\en_IN\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_IN/Person.php',
    'Faker\\Provider\\en_IN\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_IN/PhoneNumber.php',
    'Faker\\Provider\\en_NG\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_NG/Address.php',
    'Faker\\Provider\\en_NG\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_NG/Internet.php',
    'Faker\\Provider\\en_NG\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_NG/Person.php',
    'Faker\\Provider\\en_NG\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_NG/PhoneNumber.php',
    'Faker\\Provider\\en_NZ\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_NZ/Address.php',
    'Faker\\Provider\\en_NZ\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_NZ/Internet.php',
    'Faker\\Provider\\en_NZ\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_NZ/PhoneNumber.php',
    'Faker\\Provider\\en_PH\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_PH/Address.php',
    'Faker\\Provider\\en_PH\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_PH/PhoneNumber.php',
    'Faker\\Provider\\en_SG\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_SG/Address.php',
    'Faker\\Provider\\en_SG\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_SG/Person.php',
    'Faker\\Provider\\en_SG\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_SG/PhoneNumber.php',
    'Faker\\Provider\\en_UG\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_UG/Address.php',
    'Faker\\Provider\\en_UG\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_UG/Internet.php',
    'Faker\\Provider\\en_UG\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_UG/Person.php',
    'Faker\\Provider\\en_UG\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_UG/PhoneNumber.php',
    'Faker\\Provider\\en_US\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_US/Address.php',
    'Faker\\Provider\\en_US\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_US/Company.php',
    'Faker\\Provider\\en_US\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_US/Payment.php',
    'Faker\\Provider\\en_US\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_US/Person.php',
    'Faker\\Provider\\en_US\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_US/PhoneNumber.php',
    'Faker\\Provider\\en_US\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_US/Text.php',
    'Faker\\Provider\\en_ZA\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_ZA/Address.php',
    'Faker\\Provider\\en_ZA\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_ZA/Company.php',
    'Faker\\Provider\\en_ZA\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_ZA/Internet.php',
    'Faker\\Provider\\en_ZA\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_ZA/Person.php',
    'Faker\\Provider\\en_ZA\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/en_ZA/PhoneNumber.php',
    'Faker\\Provider\\es_AR\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/es_AR/Address.php',
    'Faker\\Provider\\es_AR\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/es_AR/Company.php',
    'Faker\\Provider\\es_AR\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/es_AR/Person.php',
    'Faker\\Provider\\es_AR\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/es_AR/PhoneNumber.php',
    'Faker\\Provider\\es_ES\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/es_ES/Address.php',
    'Faker\\Provider\\es_ES\\Color' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/es_ES/Color.php',
    'Faker\\Provider\\es_ES\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/es_ES/Company.php',
    'Faker\\Provider\\es_ES\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/es_ES/Internet.php',
    'Faker\\Provider\\es_ES\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/es_ES/Payment.php',
    'Faker\\Provider\\es_ES\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/es_ES/Person.php',
    'Faker\\Provider\\es_ES\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/es_ES/PhoneNumber.php',
    'Faker\\Provider\\es_ES\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/es_ES/Text.php',
    'Faker\\Provider\\es_PE\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/es_PE/Address.php',
    'Faker\\Provider\\es_PE\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/es_PE/Company.php',
    'Faker\\Provider\\es_PE\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/es_PE/Person.php',
    'Faker\\Provider\\es_PE\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/es_PE/PhoneNumber.php',
    'Faker\\Provider\\es_VE\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/es_VE/Address.php',
    'Faker\\Provider\\es_VE\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/es_VE/Company.php',
    'Faker\\Provider\\es_VE\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/es_VE/Internet.php',
    'Faker\\Provider\\es_VE\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/es_VE/Person.php',
    'Faker\\Provider\\es_VE\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/es_VE/PhoneNumber.php',
    'Faker\\Provider\\et_EE\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/et_EE/Person.php',
    'Faker\\Provider\\fa_IR\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fa_IR/Address.php',
    'Faker\\Provider\\fa_IR\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fa_IR/Company.php',
    'Faker\\Provider\\fa_IR\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fa_IR/Internet.php',
    'Faker\\Provider\\fa_IR\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fa_IR/Person.php',
    'Faker\\Provider\\fa_IR\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fa_IR/PhoneNumber.php',
    'Faker\\Provider\\fa_IR\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fa_IR/Text.php',
    'Faker\\Provider\\fi_FI\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fi_FI/Address.php',
    'Faker\\Provider\\fi_FI\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fi_FI/Company.php',
    'Faker\\Provider\\fi_FI\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fi_FI/Internet.php',
    'Faker\\Provider\\fi_FI\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fi_FI/Payment.php',
    'Faker\\Provider\\fi_FI\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fi_FI/Person.php',
    'Faker\\Provider\\fi_FI\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fi_FI/PhoneNumber.php',
    'Faker\\Provider\\fr_BE\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_BE/Address.php',
    'Faker\\Provider\\fr_BE\\Color' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_BE/Color.php',
    'Faker\\Provider\\fr_BE\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_BE/Company.php',
    'Faker\\Provider\\fr_BE\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_BE/Internet.php',
    'Faker\\Provider\\fr_BE\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_BE/Payment.php',
    'Faker\\Provider\\fr_BE\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_BE/Person.php',
    'Faker\\Provider\\fr_BE\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_BE/PhoneNumber.php',
    'Faker\\Provider\\fr_CA\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_CA/Address.php',
    'Faker\\Provider\\fr_CA\\Color' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_CA/Color.php',
    'Faker\\Provider\\fr_CA\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_CA/Company.php',
    'Faker\\Provider\\fr_CA\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_CA/Person.php',
    'Faker\\Provider\\fr_CA\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_CA/Text.php',
    'Faker\\Provider\\fr_CH\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_CH/Address.php',
    'Faker\\Provider\\fr_CH\\Color' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_CH/Color.php',
    'Faker\\Provider\\fr_CH\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_CH/Company.php',
    'Faker\\Provider\\fr_CH\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_CH/Internet.php',
    'Faker\\Provider\\fr_CH\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_CH/Payment.php',
    'Faker\\Provider\\fr_CH\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_CH/Person.php',
    'Faker\\Provider\\fr_CH\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_CH/PhoneNumber.php',
    'Faker\\Provider\\fr_CH\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_CH/Text.php',
    'Faker\\Provider\\fr_FR\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_FR/Address.php',
    'Faker\\Provider\\fr_FR\\Color' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_FR/Color.php',
    'Faker\\Provider\\fr_FR\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_FR/Company.php',
    'Faker\\Provider\\fr_FR\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_FR/Internet.php',
    'Faker\\Provider\\fr_FR\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_FR/Payment.php',
    'Faker\\Provider\\fr_FR\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_FR/Person.php',
    'Faker\\Provider\\fr_FR\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_FR/PhoneNumber.php',
    'Faker\\Provider\\fr_FR\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/fr_FR/Text.php',
    'Faker\\Provider\\he_IL\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/he_IL/Address.php',
    'Faker\\Provider\\he_IL\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/he_IL/Company.php',
    'Faker\\Provider\\he_IL\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/he_IL/Payment.php',
    'Faker\\Provider\\he_IL\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/he_IL/Person.php',
    'Faker\\Provider\\he_IL\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/he_IL/PhoneNumber.php',
    'Faker\\Provider\\hr_HR\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/hr_HR/Address.php',
    'Faker\\Provider\\hr_HR\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/hr_HR/Company.php',
    'Faker\\Provider\\hr_HR\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/hr_HR/Payment.php',
    'Faker\\Provider\\hr_HR\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/hr_HR/Person.php',
    'Faker\\Provider\\hr_HR\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/hr_HR/PhoneNumber.php',
    'Faker\\Provider\\hu_HU\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/hu_HU/Address.php',
    'Faker\\Provider\\hu_HU\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/hu_HU/Company.php',
    'Faker\\Provider\\hu_HU\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/hu_HU/Payment.php',
    'Faker\\Provider\\hu_HU\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/hu_HU/Person.php',
    'Faker\\Provider\\hu_HU\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/hu_HU/PhoneNumber.php',
    'Faker\\Provider\\hu_HU\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/hu_HU/Text.php',
    'Faker\\Provider\\hy_AM\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/hy_AM/Address.php',
    'Faker\\Provider\\hy_AM\\Color' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/hy_AM/Color.php',
    'Faker\\Provider\\hy_AM\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/hy_AM/Company.php',
    'Faker\\Provider\\hy_AM\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/hy_AM/Internet.php',
    'Faker\\Provider\\hy_AM\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/hy_AM/Person.php',
    'Faker\\Provider\\hy_AM\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/hy_AM/PhoneNumber.php',
    'Faker\\Provider\\id_ID\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/id_ID/Address.php',
    'Faker\\Provider\\id_ID\\Color' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/id_ID/Color.php',
    'Faker\\Provider\\id_ID\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/id_ID/Company.php',
    'Faker\\Provider\\id_ID\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/id_ID/Internet.php',
    'Faker\\Provider\\id_ID\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/id_ID/Person.php',
    'Faker\\Provider\\id_ID\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/id_ID/PhoneNumber.php',
    'Faker\\Provider\\is_IS\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/is_IS/Address.php',
    'Faker\\Provider\\is_IS\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/is_IS/Company.php',
    'Faker\\Provider\\is_IS\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/is_IS/Internet.php',
    'Faker\\Provider\\is_IS\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/is_IS/Payment.php',
    'Faker\\Provider\\is_IS\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/is_IS/Person.php',
    'Faker\\Provider\\is_IS\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/is_IS/PhoneNumber.php',
    'Faker\\Provider\\it_CH\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/it_CH/Address.php',
    'Faker\\Provider\\it_CH\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/it_CH/Company.php',
    'Faker\\Provider\\it_CH\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/it_CH/Internet.php',
    'Faker\\Provider\\it_CH\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/it_CH/Payment.php',
    'Faker\\Provider\\it_CH\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/it_CH/Person.php',
    'Faker\\Provider\\it_CH\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/it_CH/PhoneNumber.php',
    'Faker\\Provider\\it_CH\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/it_CH/Text.php',
    'Faker\\Provider\\it_IT\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/it_IT/Address.php',
    'Faker\\Provider\\it_IT\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/it_IT/Company.php',
    'Faker\\Provider\\it_IT\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/it_IT/Internet.php',
    'Faker\\Provider\\it_IT\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/it_IT/Payment.php',
    'Faker\\Provider\\it_IT\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/it_IT/Person.php',
    'Faker\\Provider\\it_IT\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/it_IT/PhoneNumber.php',
    'Faker\\Provider\\it_IT\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/it_IT/Text.php',
    'Faker\\Provider\\ja_JP\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ja_JP/Address.php',
    'Faker\\Provider\\ja_JP\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ja_JP/Company.php',
    'Faker\\Provider\\ja_JP\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ja_JP/Internet.php',
    'Faker\\Provider\\ja_JP\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ja_JP/Person.php',
    'Faker\\Provider\\ja_JP\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ja_JP/PhoneNumber.php',
    'Faker\\Provider\\ja_JP\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ja_JP/Text.php',
    'Faker\\Provider\\ka_GE\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ka_GE/Address.php',
    'Faker\\Provider\\ka_GE\\Color' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ka_GE/Color.php',
    'Faker\\Provider\\ka_GE\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ka_GE/Company.php',
    'Faker\\Provider\\ka_GE\\DateTime' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ka_GE/DateTime.php',
    'Faker\\Provider\\ka_GE\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ka_GE/Internet.php',
    'Faker\\Provider\\ka_GE\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ka_GE/Payment.php',
    'Faker\\Provider\\ka_GE\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ka_GE/Person.php',
    'Faker\\Provider\\ka_GE\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ka_GE/PhoneNumber.php',
    'Faker\\Provider\\ka_GE\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ka_GE/Text.php',
    'Faker\\Provider\\kk_KZ\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/kk_KZ/Address.php',
    'Faker\\Provider\\kk_KZ\\Color' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/kk_KZ/Color.php',
    'Faker\\Provider\\kk_KZ\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/kk_KZ/Company.php',
    'Faker\\Provider\\kk_KZ\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/kk_KZ/Internet.php',
    'Faker\\Provider\\kk_KZ\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/kk_KZ/Payment.php',
    'Faker\\Provider\\kk_KZ\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/kk_KZ/Person.php',
    'Faker\\Provider\\kk_KZ\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/kk_KZ/PhoneNumber.php',
    'Faker\\Provider\\kk_KZ\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/kk_KZ/Text.php',
    'Faker\\Provider\\ko_KR\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ko_KR/Address.php',
    'Faker\\Provider\\ko_KR\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ko_KR/Company.php',
    'Faker\\Provider\\ko_KR\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ko_KR/Internet.php',
    'Faker\\Provider\\ko_KR\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ko_KR/Person.php',
    'Faker\\Provider\\ko_KR\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ko_KR/PhoneNumber.php',
    'Faker\\Provider\\ko_KR\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ko_KR/Text.php',
    'Faker\\Provider\\lt_LT\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/lt_LT/Address.php',
    'Faker\\Provider\\lt_LT\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/lt_LT/Company.php',
    'Faker\\Provider\\lt_LT\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/lt_LT/Internet.php',
    'Faker\\Provider\\lt_LT\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/lt_LT/Payment.php',
    'Faker\\Provider\\lt_LT\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/lt_LT/Person.php',
    'Faker\\Provider\\lt_LT\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/lt_LT/PhoneNumber.php',
    'Faker\\Provider\\lv_LV\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/lv_LV/Address.php',
    'Faker\\Provider\\lv_LV\\Color' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/lv_LV/Color.php',
    'Faker\\Provider\\lv_LV\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/lv_LV/Internet.php',
    'Faker\\Provider\\lv_LV\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/lv_LV/Payment.php',
    'Faker\\Provider\\lv_LV\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/lv_LV/Person.php',
    'Faker\\Provider\\lv_LV\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/lv_LV/PhoneNumber.php',
    'Faker\\Provider\\me_ME\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/me_ME/Address.php',
    'Faker\\Provider\\me_ME\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/me_ME/Company.php',
    'Faker\\Provider\\me_ME\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/me_ME/Payment.php',
    'Faker\\Provider\\me_ME\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/me_ME/Person.php',
    'Faker\\Provider\\me_ME\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/me_ME/PhoneNumber.php',
    'Faker\\Provider\\mn_MN\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/mn_MN/Person.php',
    'Faker\\Provider\\mn_MN\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/mn_MN/PhoneNumber.php',
    'Faker\\Provider\\ms_MY\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ms_MY/Address.php',
    'Faker\\Provider\\ms_MY\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ms_MY/Company.php',
    'Faker\\Provider\\ms_MY\\Miscellaneous' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ms_MY/Miscellaneous.php',
    'Faker\\Provider\\ms_MY\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ms_MY/Payment.php',
    'Faker\\Provider\\ms_MY\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ms_MY/Person.php',
    'Faker\\Provider\\ms_MY\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ms_MY/PhoneNumber.php',
    'Faker\\Provider\\nb_NO\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/nb_NO/Address.php',
    'Faker\\Provider\\nb_NO\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/nb_NO/Company.php',
    'Faker\\Provider\\nb_NO\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/nb_NO/Payment.php',
    'Faker\\Provider\\nb_NO\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/nb_NO/Person.php',
    'Faker\\Provider\\nb_NO\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/nb_NO/PhoneNumber.php',
    'Faker\\Provider\\ne_NP\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ne_NP/Address.php',
    'Faker\\Provider\\ne_NP\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ne_NP/Internet.php',
    'Faker\\Provider\\ne_NP\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ne_NP/Payment.php',
    'Faker\\Provider\\ne_NP\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ne_NP/Person.php',
    'Faker\\Provider\\ne_NP\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ne_NP/PhoneNumber.php',
    'Faker\\Provider\\nl_BE\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/nl_BE/Address.php',
    'Faker\\Provider\\nl_BE\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/nl_BE/Company.php',
    'Faker\\Provider\\nl_BE\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/nl_BE/Internet.php',
    'Faker\\Provider\\nl_BE\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/nl_BE/Payment.php',
    'Faker\\Provider\\nl_BE\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/nl_BE/Person.php',
    'Faker\\Provider\\nl_BE\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/nl_BE/PhoneNumber.php',
    'Faker\\Provider\\nl_BE\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/nl_BE/Text.php',
    'Faker\\Provider\\nl_NL\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/nl_NL/Address.php',
    'Faker\\Provider\\nl_NL\\Color' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/nl_NL/Color.php',
    'Faker\\Provider\\nl_NL\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/nl_NL/Company.php',
    'Faker\\Provider\\nl_NL\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/nl_NL/Internet.php',
    'Faker\\Provider\\nl_NL\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/nl_NL/Payment.php',
    'Faker\\Provider\\nl_NL\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/nl_NL/Person.php',
    'Faker\\Provider\\nl_NL\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/nl_NL/PhoneNumber.php',
    'Faker\\Provider\\nl_NL\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/nl_NL/Text.php',
    'Faker\\Provider\\pl_PL\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/pl_PL/Address.php',
    'Faker\\Provider\\pl_PL\\Color' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/pl_PL/Color.php',
    'Faker\\Provider\\pl_PL\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/pl_PL/Company.php',
    'Faker\\Provider\\pl_PL\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/pl_PL/Internet.php',
    'Faker\\Provider\\pl_PL\\LicensePlate' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/pl_PL/LicensePlate.php',
    'Faker\\Provider\\pl_PL\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/pl_PL/Payment.php',
    'Faker\\Provider\\pl_PL\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/pl_PL/Person.php',
    'Faker\\Provider\\pl_PL\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/pl_PL/PhoneNumber.php',
    'Faker\\Provider\\pl_PL\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/pl_PL/Text.php',
    'Faker\\Provider\\pt_BR\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/pt_BR/Address.php',
    'Faker\\Provider\\pt_BR\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/pt_BR/Company.php',
    'Faker\\Provider\\pt_BR\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/pt_BR/Internet.php',
    'Faker\\Provider\\pt_BR\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/pt_BR/Payment.php',
    'Faker\\Provider\\pt_BR\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/pt_BR/Person.php',
    'Faker\\Provider\\pt_BR\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/pt_BR/PhoneNumber.php',
    'Faker\\Provider\\pt_BR\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/pt_BR/Text.php',
    'Faker\\Provider\\pt_PT\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/pt_PT/Address.php',
    'Faker\\Provider\\pt_PT\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/pt_PT/Company.php',
    'Faker\\Provider\\pt_PT\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/pt_PT/Internet.php',
    'Faker\\Provider\\pt_PT\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/pt_PT/Payment.php',
    'Faker\\Provider\\pt_PT\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/pt_PT/Person.php',
    'Faker\\Provider\\pt_PT\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/pt_PT/PhoneNumber.php',
    'Faker\\Provider\\ro_MD\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ro_MD/Address.php',
    'Faker\\Provider\\ro_MD\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ro_MD/Payment.php',
    'Faker\\Provider\\ro_MD\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ro_MD/Person.php',
    'Faker\\Provider\\ro_MD\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ro_MD/PhoneNumber.php',
    'Faker\\Provider\\ro_MD\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ro_MD/Text.php',
    'Faker\\Provider\\ro_RO\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ro_RO/Address.php',
    'Faker\\Provider\\ro_RO\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ro_RO/Payment.php',
    'Faker\\Provider\\ro_RO\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ro_RO/Person.php',
    'Faker\\Provider\\ro_RO\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ro_RO/PhoneNumber.php',
    'Faker\\Provider\\ro_RO\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ro_RO/Text.php',
    'Faker\\Provider\\ru_RU\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ru_RU/Address.php',
    'Faker\\Provider\\ru_RU\\Color' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ru_RU/Color.php',
    'Faker\\Provider\\ru_RU\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ru_RU/Company.php',
    'Faker\\Provider\\ru_RU\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ru_RU/Internet.php',
    'Faker\\Provider\\ru_RU\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ru_RU/Payment.php',
    'Faker\\Provider\\ru_RU\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ru_RU/Person.php',
    'Faker\\Provider\\ru_RU\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ru_RU/PhoneNumber.php',
    'Faker\\Provider\\ru_RU\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/ru_RU/Text.php',
    'Faker\\Provider\\sk_SK\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sk_SK/Address.php',
    'Faker\\Provider\\sk_SK\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sk_SK/Company.php',
    'Faker\\Provider\\sk_SK\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sk_SK/Internet.php',
    'Faker\\Provider\\sk_SK\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sk_SK/Payment.php',
    'Faker\\Provider\\sk_SK\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sk_SK/Person.php',
    'Faker\\Provider\\sk_SK\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sk_SK/PhoneNumber.php',
    'Faker\\Provider\\sl_SI\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sl_SI/Address.php',
    'Faker\\Provider\\sl_SI\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sl_SI/Company.php',
    'Faker\\Provider\\sl_SI\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sl_SI/Internet.php',
    'Faker\\Provider\\sl_SI\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sl_SI/Payment.php',
    'Faker\\Provider\\sl_SI\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sl_SI/Person.php',
    'Faker\\Provider\\sl_SI\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sl_SI/PhoneNumber.php',
    'Faker\\Provider\\sr_Cyrl_RS\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sr_Cyrl_RS/Address.php',
    'Faker\\Provider\\sr_Cyrl_RS\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sr_Cyrl_RS/Payment.php',
    'Faker\\Provider\\sr_Cyrl_RS\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sr_Cyrl_RS/Person.php',
    'Faker\\Provider\\sr_Latn_RS\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sr_Latn_RS/Address.php',
    'Faker\\Provider\\sr_Latn_RS\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sr_Latn_RS/Payment.php',
    'Faker\\Provider\\sr_Latn_RS\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sr_Latn_RS/Person.php',
    'Faker\\Provider\\sr_RS\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sr_RS/Address.php',
    'Faker\\Provider\\sr_RS\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sr_RS/Payment.php',
    'Faker\\Provider\\sr_RS\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sr_RS/Person.php',
    'Faker\\Provider\\sv_SE\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sv_SE/Address.php',
    'Faker\\Provider\\sv_SE\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sv_SE/Company.php',
    'Faker\\Provider\\sv_SE\\Municipality' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sv_SE/Municipality.php',
    'Faker\\Provider\\sv_SE\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sv_SE/Payment.php',
    'Faker\\Provider\\sv_SE\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sv_SE/Person.php',
    'Faker\\Provider\\sv_SE\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/sv_SE/PhoneNumber.php',
    'Faker\\Provider\\th_TH\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/th_TH/Address.php',
    'Faker\\Provider\\th_TH\\Color' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/th_TH/Color.php',
    'Faker\\Provider\\th_TH\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/th_TH/Company.php',
    'Faker\\Provider\\th_TH\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/th_TH/Internet.php',
    'Faker\\Provider\\th_TH\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/th_TH/Payment.php',
    'Faker\\Provider\\th_TH\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/th_TH/Person.php',
    'Faker\\Provider\\th_TH\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/th_TH/PhoneNumber.php',
    'Faker\\Provider\\tr_TR\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/tr_TR/Address.php',
    'Faker\\Provider\\tr_TR\\Color' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/tr_TR/Color.php',
    'Faker\\Provider\\tr_TR\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/tr_TR/Company.php',
    'Faker\\Provider\\tr_TR\\DateTime' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/tr_TR/DateTime.php',
    'Faker\\Provider\\tr_TR\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/tr_TR/Internet.php',
    'Faker\\Provider\\tr_TR\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/tr_TR/Payment.php',
    'Faker\\Provider\\tr_TR\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/tr_TR/Person.php',
    'Faker\\Provider\\tr_TR\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/tr_TR/PhoneNumber.php',
    'Faker\\Provider\\uk_UA\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/uk_UA/Address.php',
    'Faker\\Provider\\uk_UA\\Color' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/uk_UA/Color.php',
    'Faker\\Provider\\uk_UA\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/uk_UA/Company.php',
    'Faker\\Provider\\uk_UA\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/uk_UA/Internet.php',
    'Faker\\Provider\\uk_UA\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/uk_UA/Payment.php',
    'Faker\\Provider\\uk_UA\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/uk_UA/Person.php',
    'Faker\\Provider\\uk_UA\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/uk_UA/PhoneNumber.php',
    'Faker\\Provider\\uk_UA\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/uk_UA/Text.php',
    'Faker\\Provider\\vi_VN\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/vi_VN/Address.php',
    'Faker\\Provider\\vi_VN\\Color' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/vi_VN/Color.php',
    'Faker\\Provider\\vi_VN\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/vi_VN/Internet.php',
    'Faker\\Provider\\vi_VN\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/vi_VN/Person.php',
    'Faker\\Provider\\vi_VN\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/vi_VN/PhoneNumber.php',
    'Faker\\Provider\\zh_CN\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/zh_CN/Address.php',
    'Faker\\Provider\\zh_CN\\Color' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/zh_CN/Color.php',
    'Faker\\Provider\\zh_CN\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/zh_CN/Company.php',
    'Faker\\Provider\\zh_CN\\DateTime' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/zh_CN/DateTime.php',
    'Faker\\Provider\\zh_CN\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/zh_CN/Internet.php',
    'Faker\\Provider\\zh_CN\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/zh_CN/Payment.php',
    'Faker\\Provider\\zh_CN\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/zh_CN/Person.php',
    'Faker\\Provider\\zh_CN\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/zh_CN/PhoneNumber.php',
    'Faker\\Provider\\zh_TW\\Address' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/zh_TW/Address.php',
    'Faker\\Provider\\zh_TW\\Color' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/zh_TW/Color.php',
    'Faker\\Provider\\zh_TW\\Company' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/zh_TW/Company.php',
    'Faker\\Provider\\zh_TW\\DateTime' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/zh_TW/DateTime.php',
    'Faker\\Provider\\zh_TW\\Internet' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/zh_TW/Internet.php',
    'Faker\\Provider\\zh_TW\\Payment' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/zh_TW/Payment.php',
    'Faker\\Provider\\zh_TW\\Person' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/zh_TW/Person.php',
    'Faker\\Provider\\zh_TW\\PhoneNumber' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/zh_TW/PhoneNumber.php',
    'Faker\\Provider\\zh_TW\\Text' => $vendorDir . '/fakerphp/faker/src/Faker/Provider/zh_TW/Text.php',
    'Faker\\UniqueGenerator' => $vendorDir . '/fakerphp/faker/src/Faker/UniqueGenerator.php',
    'Faker\\ValidGenerator' => $vendorDir . '/fakerphp/faker/src/Faker/ValidGenerator.php',
    'Laminas\\Escaper\\Escaper' => $vendorDir . '/laminas/laminas-escaper/src/Escaper.php',
    'Laminas\\Escaper\\EscaperInterface' => $vendorDir . '/laminas/laminas-escaper/src/EscaperInterface.php',
    'Laminas\\Escaper\\Exception\\ExceptionInterface' => $vendorDir . '/laminas/laminas-escaper/src/Exception/ExceptionInterface.php',
    'Laminas\\Escaper\\Exception\\InvalidArgumentException' => $vendorDir . '/laminas/laminas-escaper/src/Exception/InvalidArgumentException.php',
    'Laminas\\Escaper\\Exception\\RuntimeException' => $vendorDir . '/laminas/laminas-escaper/src/Exception/RuntimeException.php',
    'PHPUnit\\Event\\Application\\Finished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Application/Finished.php',
    'PHPUnit\\Event\\Application\\FinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Application/FinishedSubscriber.php',
    'PHPUnit\\Event\\Application\\Started' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Application/Started.php',
    'PHPUnit\\Event\\Application\\StartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Application/StartedSubscriber.php',
    'PHPUnit\\Event\\Code\\ClassMethod' => $vendorDir . '/phpunit/phpunit/src/Event/Value/ClassMethod.php',
    'PHPUnit\\Event\\Code\\ComparisonFailure' => $vendorDir . '/phpunit/phpunit/src/Event/Value/ComparisonFailure.php',
    'PHPUnit\\Event\\Code\\ComparisonFailureBuilder' => $vendorDir . '/phpunit/phpunit/src/Event/Value/ComparisonFailureBuilder.php',
    'PHPUnit\\Event\\Code\\NoTestCaseObjectOnCallStackException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/NoTestCaseObjectOnCallStackException.php',
    'PHPUnit\\Event\\Code\\Phpt' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/Phpt.php',
    'PHPUnit\\Event\\Code\\Test' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/Test.php',
    'PHPUnit\\Event\\Code\\TestCollection' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/TestCollection.php',
    'PHPUnit\\Event\\Code\\TestCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/TestCollectionIterator.php',
    'PHPUnit\\Event\\Code\\TestDox' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/TestDox.php',
    'PHPUnit\\Event\\Code\\TestDoxBuilder' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/TestDoxBuilder.php',
    'PHPUnit\\Event\\Code\\TestMethod' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/TestMethod.php',
    'PHPUnit\\Event\\Code\\TestMethodBuilder' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/TestMethodBuilder.php',
    'PHPUnit\\Event\\Code\\Throwable' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Throwable.php',
    'PHPUnit\\Event\\Code\\ThrowableBuilder' => $vendorDir . '/phpunit/phpunit/src/Event/Value/ThrowableBuilder.php',
    'PHPUnit\\Event\\CollectingDispatcher' => $vendorDir . '/phpunit/phpunit/src/Event/Dispatcher/CollectingDispatcher.php',
    'PHPUnit\\Event\\DeferringDispatcher' => $vendorDir . '/phpunit/phpunit/src/Event/Dispatcher/DeferringDispatcher.php',
    'PHPUnit\\Event\\DirectDispatcher' => $vendorDir . '/phpunit/phpunit/src/Event/Dispatcher/DirectDispatcher.php',
    'PHPUnit\\Event\\Dispatcher' => $vendorDir . '/phpunit/phpunit/src/Event/Dispatcher/Dispatcher.php',
    'PHPUnit\\Event\\DispatchingEmitter' => $vendorDir . '/phpunit/phpunit/src/Event/Emitter/DispatchingEmitter.php',
    'PHPUnit\\Event\\Emitter' => $vendorDir . '/phpunit/phpunit/src/Event/Emitter/Emitter.php',
    'PHPUnit\\Event\\Event' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Event.php',
    'PHPUnit\\Event\\EventAlreadyAssignedException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/EventAlreadyAssignedException.php',
    'PHPUnit\\Event\\EventCollection' => $vendorDir . '/phpunit/phpunit/src/Event/Events/EventCollection.php',
    'PHPUnit\\Event\\EventCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/Event/Events/EventCollectionIterator.php',
    'PHPUnit\\Event\\EventFacadeIsSealedException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/EventFacadeIsSealedException.php',
    'PHPUnit\\Event\\Exception' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/Exception.php',
    'PHPUnit\\Event\\Facade' => $vendorDir . '/phpunit/phpunit/src/Event/Facade.php',
    'PHPUnit\\Event\\InvalidArgumentException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/InvalidArgumentException.php',
    'PHPUnit\\Event\\InvalidEventException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/InvalidEventException.php',
    'PHPUnit\\Event\\InvalidSubscriberException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/InvalidSubscriberException.php',
    'PHPUnit\\Event\\MapError' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/MapError.php',
    'PHPUnit\\Event\\NoPreviousThrowableException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/NoPreviousThrowableException.php',
    'PHPUnit\\Event\\RuntimeException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/RuntimeException.php',
    'PHPUnit\\Event\\Runtime\\OperatingSystem' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Runtime/OperatingSystem.php',
    'PHPUnit\\Event\\Runtime\\PHP' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Runtime/PHP.php',
    'PHPUnit\\Event\\Runtime\\PHPUnit' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Runtime/PHPUnit.php',
    'PHPUnit\\Event\\Runtime\\Runtime' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Runtime/Runtime.php',
    'PHPUnit\\Event\\SubscribableDispatcher' => $vendorDir . '/phpunit/phpunit/src/Event/Dispatcher/SubscribableDispatcher.php',
    'PHPUnit\\Event\\Subscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Subscriber.php',
    'PHPUnit\\Event\\SubscriberTypeAlreadyRegisteredException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/SubscriberTypeAlreadyRegisteredException.php',
    'PHPUnit\\Event\\Telemetry\\Duration' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/Duration.php',
    'PHPUnit\\Event\\Telemetry\\GarbageCollectorStatus' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/GarbageCollectorStatus.php',
    'PHPUnit\\Event\\Telemetry\\GarbageCollectorStatusProvider' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/GarbageCollectorStatusProvider.php',
    'PHPUnit\\Event\\Telemetry\\HRTime' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/HRTime.php',
    'PHPUnit\\Event\\Telemetry\\Info' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/Info.php',
    'PHPUnit\\Event\\Telemetry\\MemoryMeter' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/MemoryMeter.php',
    'PHPUnit\\Event\\Telemetry\\MemoryUsage' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/MemoryUsage.php',
    'PHPUnit\\Event\\Telemetry\\Php81GarbageCollectorStatusProvider' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/Php81GarbageCollectorStatusProvider.php',
    'PHPUnit\\Event\\Telemetry\\Php83GarbageCollectorStatusProvider' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/Php83GarbageCollectorStatusProvider.php',
    'PHPUnit\\Event\\Telemetry\\Snapshot' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/Snapshot.php',
    'PHPUnit\\Event\\Telemetry\\StopWatch' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/StopWatch.php',
    'PHPUnit\\Event\\Telemetry\\System' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/System.php',
    'PHPUnit\\Event\\Telemetry\\SystemMemoryMeter' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/SystemMemoryMeter.php',
    'PHPUnit\\Event\\Telemetry\\SystemStopWatch' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/SystemStopWatch.php',
    'PHPUnit\\Event\\Telemetry\\SystemStopWatchWithOffset' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Telemetry/SystemStopWatchWithOffset.php',
    'PHPUnit\\Event\\TestData\\DataFromDataProvider' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/TestData/DataFromDataProvider.php',
    'PHPUnit\\Event\\TestData\\DataFromTestDependency' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/TestData/DataFromTestDependency.php',
    'PHPUnit\\Event\\TestData\\MoreThanOneDataSetFromDataProviderException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/MoreThanOneDataSetFromDataProviderException.php',
    'PHPUnit\\Event\\TestData\\NoDataSetFromDataProviderException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/NoDataSetFromDataProviderException.php',
    'PHPUnit\\Event\\TestData\\TestData' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/TestData/TestData.php',
    'PHPUnit\\Event\\TestData\\TestDataCollection' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/TestData/TestDataCollection.php',
    'PHPUnit\\Event\\TestData\\TestDataCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/Event/Value/Test/TestData/TestDataCollectionIterator.php',
    'PHPUnit\\Event\\TestRunner\\BootstrapFinished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/BootstrapFinished.php',
    'PHPUnit\\Event\\TestRunner\\BootstrapFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/BootstrapFinishedSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\Configured' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/Configured.php',
    'PHPUnit\\Event\\TestRunner\\ConfiguredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ConfiguredSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\DeprecationTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/DeprecationTriggered.php',
    'PHPUnit\\Event\\TestRunner\\DeprecationTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/DeprecationTriggeredSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\EventFacadeSealed' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/EventFacadeSealed.php',
    'PHPUnit\\Event\\TestRunner\\EventFacadeSealedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/EventFacadeSealedSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\ExecutionAborted' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ExecutionAborted.php',
    'PHPUnit\\Event\\TestRunner\\ExecutionAbortedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ExecutionAbortedSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\ExecutionFinished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ExecutionFinished.php',
    'PHPUnit\\Event\\TestRunner\\ExecutionFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ExecutionFinishedSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\ExecutionStarted' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ExecutionStarted.php',
    'PHPUnit\\Event\\TestRunner\\ExecutionStartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ExecutionStartedSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\ExtensionBootstrapped' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ExtensionBootstrapped.php',
    'PHPUnit\\Event\\TestRunner\\ExtensionBootstrappedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ExtensionBootstrappedSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\ExtensionLoadedFromPhar' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ExtensionLoadedFromPhar.php',
    'PHPUnit\\Event\\TestRunner\\ExtensionLoadedFromPharSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/ExtensionLoadedFromPharSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\Finished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/Finished.php',
    'PHPUnit\\Event\\TestRunner\\FinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/FinishedSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\GarbageCollectionDisabled' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/GarbageCollectionDisabled.php',
    'PHPUnit\\Event\\TestRunner\\GarbageCollectionDisabledSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/GarbageCollectionDisabledSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\GarbageCollectionEnabled' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/GarbageCollectionEnabled.php',
    'PHPUnit\\Event\\TestRunner\\GarbageCollectionEnabledSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/GarbageCollectionEnabledSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\GarbageCollectionTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/GarbageCollectionTriggered.php',
    'PHPUnit\\Event\\TestRunner\\GarbageCollectionTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/GarbageCollectionTriggeredSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\Started' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/Started.php',
    'PHPUnit\\Event\\TestRunner\\StartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/StartedSubscriber.php',
    'PHPUnit\\Event\\TestRunner\\WarningTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/WarningTriggered.php',
    'PHPUnit\\Event\\TestRunner\\WarningTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestRunner/WarningTriggeredSubscriber.php',
    'PHPUnit\\Event\\TestSuite\\Filtered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/Filtered.php',
    'PHPUnit\\Event\\TestSuite\\FilteredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/FilteredSubscriber.php',
    'PHPUnit\\Event\\TestSuite\\Finished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/Finished.php',
    'PHPUnit\\Event\\TestSuite\\FinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/FinishedSubscriber.php',
    'PHPUnit\\Event\\TestSuite\\Loaded' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/Loaded.php',
    'PHPUnit\\Event\\TestSuite\\LoadedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/LoadedSubscriber.php',
    'PHPUnit\\Event\\TestSuite\\Skipped' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/Skipped.php',
    'PHPUnit\\Event\\TestSuite\\SkippedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/SkippedSubscriber.php',
    'PHPUnit\\Event\\TestSuite\\Sorted' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/Sorted.php',
    'PHPUnit\\Event\\TestSuite\\SortedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/SortedSubscriber.php',
    'PHPUnit\\Event\\TestSuite\\Started' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/Started.php',
    'PHPUnit\\Event\\TestSuite\\StartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/TestSuite/StartedSubscriber.php',
    'PHPUnit\\Event\\TestSuite\\TestSuite' => $vendorDir . '/phpunit/phpunit/src/Event/Value/TestSuite/TestSuite.php',
    'PHPUnit\\Event\\TestSuite\\TestSuiteBuilder' => $vendorDir . '/phpunit/phpunit/src/Event/Value/TestSuite/TestSuiteBuilder.php',
    'PHPUnit\\Event\\TestSuite\\TestSuiteForTestClass' => $vendorDir . '/phpunit/phpunit/src/Event/Value/TestSuite/TestSuiteForTestClass.php',
    'PHPUnit\\Event\\TestSuite\\TestSuiteForTestMethodWithDataProvider' => $vendorDir . '/phpunit/phpunit/src/Event/Value/TestSuite/TestSuiteForTestMethodWithDataProvider.php',
    'PHPUnit\\Event\\TestSuite\\TestSuiteWithName' => $vendorDir . '/phpunit/phpunit/src/Event/Value/TestSuite/TestSuiteWithName.php',
    'PHPUnit\\Event\\Test\\AfterLastTestMethodCalled' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterLastTestMethodCalled.php',
    'PHPUnit\\Event\\Test\\AfterLastTestMethodCalledSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterLastTestMethodCalledSubscriber.php',
    'PHPUnit\\Event\\Test\\AfterLastTestMethodErrored' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterLastTestMethodErrored.php',
    'PHPUnit\\Event\\Test\\AfterLastTestMethodErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterLastTestMethodErroredSubscriber.php',
    'PHPUnit\\Event\\Test\\AfterLastTestMethodFinished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterLastTestMethodFinished.php',
    'PHPUnit\\Event\\Test\\AfterLastTestMethodFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterLastTestMethodFinishedSubscriber.php',
    'PHPUnit\\Event\\Test\\AfterTestMethodCalled' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterTestMethodCalled.php',
    'PHPUnit\\Event\\Test\\AfterTestMethodCalledSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterTestMethodCalledSubscriber.php',
    'PHPUnit\\Event\\Test\\AfterTestMethodErrored' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterTestMethodErrored.php',
    'PHPUnit\\Event\\Test\\AfterTestMethodErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterTestMethodErroredSubscriber.php',
    'PHPUnit\\Event\\Test\\AfterTestMethodFinished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterTestMethodFinished.php',
    'PHPUnit\\Event\\Test\\AfterTestMethodFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/AfterTestMethodFinishedSubscriber.php',
    'PHPUnit\\Event\\Test\\AssertionFailed' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Assertion/AssertionFailed.php',
    'PHPUnit\\Event\\Test\\AssertionFailedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Assertion/AssertionFailedSubscriber.php',
    'PHPUnit\\Event\\Test\\AssertionSucceeded' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Assertion/AssertionSucceeded.php',
    'PHPUnit\\Event\\Test\\AssertionSucceededSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Assertion/AssertionSucceededSubscriber.php',
    'PHPUnit\\Event\\Test\\BeforeFirstTestMethodCalled' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeFirstTestMethodCalled.php',
    'PHPUnit\\Event\\Test\\BeforeFirstTestMethodCalledSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeFirstTestMethodCalledSubscriber.php',
    'PHPUnit\\Event\\Test\\BeforeFirstTestMethodErrored' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeFirstTestMethodErrored.php',
    'PHPUnit\\Event\\Test\\BeforeFirstTestMethodErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeFirstTestMethodErroredSubscriber.php',
    'PHPUnit\\Event\\Test\\BeforeFirstTestMethodFinished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeFirstTestMethodFinished.php',
    'PHPUnit\\Event\\Test\\BeforeFirstTestMethodFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeFirstTestMethodFinishedSubscriber.php',
    'PHPUnit\\Event\\Test\\BeforeTestMethodCalled' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeTestMethodCalled.php',
    'PHPUnit\\Event\\Test\\BeforeTestMethodCalledSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeTestMethodCalledSubscriber.php',
    'PHPUnit\\Event\\Test\\BeforeTestMethodErrored' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeTestMethodErrored.php',
    'PHPUnit\\Event\\Test\\BeforeTestMethodErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeTestMethodErroredSubscriber.php',
    'PHPUnit\\Event\\Test\\BeforeTestMethodFinished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeTestMethodFinished.php',
    'PHPUnit\\Event\\Test\\BeforeTestMethodFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/BeforeTestMethodFinishedSubscriber.php',
    'PHPUnit\\Event\\Test\\ComparatorRegistered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/ComparatorRegistered.php',
    'PHPUnit\\Event\\Test\\ComparatorRegisteredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/ComparatorRegisteredSubscriber.php',
    'PHPUnit\\Event\\Test\\ConsideredRisky' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/ConsideredRisky.php',
    'PHPUnit\\Event\\Test\\ConsideredRiskySubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/ConsideredRiskySubscriber.php',
    'PHPUnit\\Event\\Test\\DataProviderMethodCalled' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/DataProviderMethodCalled.php',
    'PHPUnit\\Event\\Test\\DataProviderMethodCalledSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/DataProviderMethodCalledSubscriber.php',
    'PHPUnit\\Event\\Test\\DataProviderMethodFinished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/DataProviderMethodFinished.php',
    'PHPUnit\\Event\\Test\\DataProviderMethodFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/DataProviderMethodFinishedSubscriber.php',
    'PHPUnit\\Event\\Test\\DeprecationTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/DeprecationTriggered.php',
    'PHPUnit\\Event\\Test\\DeprecationTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/DeprecationTriggeredSubscriber.php',
    'PHPUnit\\Event\\Test\\ErrorTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/ErrorTriggered.php',
    'PHPUnit\\Event\\Test\\ErrorTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/ErrorTriggeredSubscriber.php',
    'PHPUnit\\Event\\Test\\Errored' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Outcome/Errored.php',
    'PHPUnit\\Event\\Test\\ErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Outcome/ErroredSubscriber.php',
    'PHPUnit\\Event\\Test\\Failed' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Outcome/Failed.php',
    'PHPUnit\\Event\\Test\\FailedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Outcome/FailedSubscriber.php',
    'PHPUnit\\Event\\Test\\Finished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/Finished.php',
    'PHPUnit\\Event\\Test\\FinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/FinishedSubscriber.php',
    'PHPUnit\\Event\\Test\\MarkedIncomplete' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Outcome/MarkedIncomplete.php',
    'PHPUnit\\Event\\Test\\MarkedIncompleteSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Outcome/MarkedIncompleteSubscriber.php',
    'PHPUnit\\Event\\Test\\MockObjectCreated' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/MockObjectCreated.php',
    'PHPUnit\\Event\\Test\\MockObjectCreatedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/MockObjectCreatedSubscriber.php',
    'PHPUnit\\Event\\Test\\MockObjectForAbstractClassCreated' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/MockObjectForAbstractClassCreated.php',
    'PHPUnit\\Event\\Test\\MockObjectForAbstractClassCreatedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/MockObjectForAbstractClassCreatedSubscriber.php',
    'PHPUnit\\Event\\Test\\MockObjectForIntersectionOfInterfacesCreated' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/MockObjectForIntersectionOfInterfacesCreated.php',
    'PHPUnit\\Event\\Test\\MockObjectForIntersectionOfInterfacesCreatedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/MockObjectForIntersectionOfInterfacesCreatedSubscriber.php',
    'PHPUnit\\Event\\Test\\MockObjectForTraitCreated' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/MockObjectForTraitCreated.php',
    'PHPUnit\\Event\\Test\\MockObjectForTraitCreatedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/MockObjectForTraitCreatedSubscriber.php',
    'PHPUnit\\Event\\Test\\MockObjectFromWsdlCreated' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/MockObjectFromWsdlCreated.php',
    'PHPUnit\\Event\\Test\\MockObjectFromWsdlCreatedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/MockObjectFromWsdlCreatedSubscriber.php',
    'PHPUnit\\Event\\Test\\NoComparisonFailureException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/NoComparisonFailureException.php',
    'PHPUnit\\Event\\Test\\NoticeTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/NoticeTriggered.php',
    'PHPUnit\\Event\\Test\\NoticeTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/NoticeTriggeredSubscriber.php',
    'PHPUnit\\Event\\Test\\PartialMockObjectCreated' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/PartialMockObjectCreated.php',
    'PHPUnit\\Event\\Test\\PartialMockObjectCreatedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/PartialMockObjectCreatedSubscriber.php',
    'PHPUnit\\Event\\Test\\Passed' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Outcome/Passed.php',
    'PHPUnit\\Event\\Test\\PassedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Outcome/PassedSubscriber.php',
    'PHPUnit\\Event\\Test\\PhpDeprecationTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpDeprecationTriggered.php',
    'PHPUnit\\Event\\Test\\PhpDeprecationTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpDeprecationTriggeredSubscriber.php',
    'PHPUnit\\Event\\Test\\PhpNoticeTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpNoticeTriggered.php',
    'PHPUnit\\Event\\Test\\PhpNoticeTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpNoticeTriggeredSubscriber.php',
    'PHPUnit\\Event\\Test\\PhpWarningTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpWarningTriggered.php',
    'PHPUnit\\Event\\Test\\PhpWarningTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpWarningTriggeredSubscriber.php',
    'PHPUnit\\Event\\Test\\PhpunitDeprecationTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpunitDeprecationTriggered.php',
    'PHPUnit\\Event\\Test\\PhpunitDeprecationTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpunitDeprecationTriggeredSubscriber.php',
    'PHPUnit\\Event\\Test\\PhpunitErrorTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpunitErrorTriggered.php',
    'PHPUnit\\Event\\Test\\PhpunitErrorTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpunitErrorTriggeredSubscriber.php',
    'PHPUnit\\Event\\Test\\PhpunitWarningTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpunitWarningTriggered.php',
    'PHPUnit\\Event\\Test\\PhpunitWarningTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/PhpunitWarningTriggeredSubscriber.php',
    'PHPUnit\\Event\\Test\\PostConditionCalled' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PostConditionCalled.php',
    'PHPUnit\\Event\\Test\\PostConditionCalledSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PostConditionCalledSubscriber.php',
    'PHPUnit\\Event\\Test\\PostConditionErrored' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PostConditionErrored.php',
    'PHPUnit\\Event\\Test\\PostConditionErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PostConditionErroredSubscriber.php',
    'PHPUnit\\Event\\Test\\PostConditionFinished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PostConditionFinished.php',
    'PHPUnit\\Event\\Test\\PostConditionFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PostConditionFinishedSubscriber.php',
    'PHPUnit\\Event\\Test\\PreConditionCalled' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PreConditionCalled.php',
    'PHPUnit\\Event\\Test\\PreConditionCalledSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PreConditionCalledSubscriber.php',
    'PHPUnit\\Event\\Test\\PreConditionErrored' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PreConditionErrored.php',
    'PHPUnit\\Event\\Test\\PreConditionErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PreConditionErroredSubscriber.php',
    'PHPUnit\\Event\\Test\\PreConditionFinished' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PreConditionFinished.php',
    'PHPUnit\\Event\\Test\\PreConditionFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/HookMethod/PreConditionFinishedSubscriber.php',
    'PHPUnit\\Event\\Test\\PreparationFailed' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/PreparationFailed.php',
    'PHPUnit\\Event\\Test\\PreparationFailedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/PreparationFailedSubscriber.php',
    'PHPUnit\\Event\\Test\\PreparationStarted' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/PreparationStarted.php',
    'PHPUnit\\Event\\Test\\PreparationStartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/PreparationStartedSubscriber.php',
    'PHPUnit\\Event\\Test\\Prepared' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/Prepared.php',
    'PHPUnit\\Event\\Test\\PreparedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Lifecycle/PreparedSubscriber.php',
    'PHPUnit\\Event\\Test\\PrintedUnexpectedOutput' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/PrintedUnexpectedOutput.php',
    'PHPUnit\\Event\\Test\\PrintedUnexpectedOutputSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/PrintedUnexpectedOutputSubscriber.php',
    'PHPUnit\\Event\\Test\\Skipped' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Outcome/Skipped.php',
    'PHPUnit\\Event\\Test\\SkippedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Outcome/SkippedSubscriber.php',
    'PHPUnit\\Event\\Test\\TestProxyCreated' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/TestProxyCreated.php',
    'PHPUnit\\Event\\Test\\TestProxyCreatedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/TestProxyCreatedSubscriber.php',
    'PHPUnit\\Event\\Test\\TestStubCreated' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/TestStubCreated.php',
    'PHPUnit\\Event\\Test\\TestStubCreatedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/TestStubCreatedSubscriber.php',
    'PHPUnit\\Event\\Test\\TestStubForIntersectionOfInterfacesCreated' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/TestStubForIntersectionOfInterfacesCreated.php',
    'PHPUnit\\Event\\Test\\TestStubForIntersectionOfInterfacesCreatedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/TestDouble/TestStubForIntersectionOfInterfacesCreatedSubscriber.php',
    'PHPUnit\\Event\\Test\\WarningTriggered' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/WarningTriggered.php',
    'PHPUnit\\Event\\Test\\WarningTriggeredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Event/Events/Test/Issue/WarningTriggeredSubscriber.php',
    'PHPUnit\\Event\\Tracer\\Tracer' => $vendorDir . '/phpunit/phpunit/src/Event/Tracer.php',
    'PHPUnit\\Event\\TypeMap' => $vendorDir . '/phpunit/phpunit/src/Event/TypeMap.php',
    'PHPUnit\\Event\\UnknownEventException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/UnknownEventException.php',
    'PHPUnit\\Event\\UnknownEventTypeException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/UnknownEventTypeException.php',
    'PHPUnit\\Event\\UnknownSubscriberException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/UnknownSubscriberException.php',
    'PHPUnit\\Event\\UnknownSubscriberTypeException' => $vendorDir . '/phpunit/phpunit/src/Event/Exception/UnknownSubscriberTypeException.php',
    'PHPUnit\\Exception' => $vendorDir . '/phpunit/phpunit/src/Exception.php',
    'PHPUnit\\Framework\\ActualValueIsNotAnObjectException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/ObjectEquals/ActualValueIsNotAnObjectException.php',
    'PHPUnit\\Framework\\Assert' => $vendorDir . '/phpunit/phpunit/src/Framework/Assert.php',
    'PHPUnit\\Framework\\AssertionFailedError' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/AssertionFailedError.php',
    'PHPUnit\\Framework\\Attributes\\After' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/After.php',
    'PHPUnit\\Framework\\Attributes\\AfterClass' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/AfterClass.php',
    'PHPUnit\\Framework\\Attributes\\BackupGlobals' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/BackupGlobals.php',
    'PHPUnit\\Framework\\Attributes\\BackupStaticProperties' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/BackupStaticProperties.php',
    'PHPUnit\\Framework\\Attributes\\Before' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/Before.php',
    'PHPUnit\\Framework\\Attributes\\BeforeClass' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/BeforeClass.php',
    'PHPUnit\\Framework\\Attributes\\CodeCoverageIgnore' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/CodeCoverageIgnore.php',
    'PHPUnit\\Framework\\Attributes\\CoversClass' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/CoversClass.php',
    'PHPUnit\\Framework\\Attributes\\CoversFunction' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/CoversFunction.php',
    'PHPUnit\\Framework\\Attributes\\CoversNothing' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/CoversNothing.php',
    'PHPUnit\\Framework\\Attributes\\DataProvider' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/DataProvider.php',
    'PHPUnit\\Framework\\Attributes\\DataProviderExternal' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/DataProviderExternal.php',
    'PHPUnit\\Framework\\Attributes\\Depends' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/Depends.php',
    'PHPUnit\\Framework\\Attributes\\DependsExternal' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/DependsExternal.php',
    'PHPUnit\\Framework\\Attributes\\DependsExternalUsingDeepClone' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/DependsExternalUsingDeepClone.php',
    'PHPUnit\\Framework\\Attributes\\DependsExternalUsingShallowClone' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/DependsExternalUsingShallowClone.php',
    'PHPUnit\\Framework\\Attributes\\DependsOnClass' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/DependsOnClass.php',
    'PHPUnit\\Framework\\Attributes\\DependsOnClassUsingDeepClone' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/DependsOnClassUsingDeepClone.php',
    'PHPUnit\\Framework\\Attributes\\DependsOnClassUsingShallowClone' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/DependsOnClassUsingShallowClone.php',
    'PHPUnit\\Framework\\Attributes\\DependsUsingDeepClone' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/DependsUsingDeepClone.php',
    'PHPUnit\\Framework\\Attributes\\DependsUsingShallowClone' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/DependsUsingShallowClone.php',
    'PHPUnit\\Framework\\Attributes\\DoesNotPerformAssertions' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/DoesNotPerformAssertions.php',
    'PHPUnit\\Framework\\Attributes\\ExcludeGlobalVariableFromBackup' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/ExcludeGlobalVariableFromBackup.php',
    'PHPUnit\\Framework\\Attributes\\ExcludeStaticPropertyFromBackup' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/ExcludeStaticPropertyFromBackup.php',
    'PHPUnit\\Framework\\Attributes\\Group' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/Group.php',
    'PHPUnit\\Framework\\Attributes\\IgnoreClassForCodeCoverage' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/IgnoreClassForCodeCoverage.php',
    'PHPUnit\\Framework\\Attributes\\IgnoreDeprecations' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/IgnoreDeprecations.php',
    'PHPUnit\\Framework\\Attributes\\IgnoreFunctionForCodeCoverage' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/IgnoreFunctionForCodeCoverage.php',
    'PHPUnit\\Framework\\Attributes\\IgnoreMethodForCodeCoverage' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/IgnoreMethodForCodeCoverage.php',
    'PHPUnit\\Framework\\Attributes\\Large' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/Large.php',
    'PHPUnit\\Framework\\Attributes\\Medium' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/Medium.php',
    'PHPUnit\\Framework\\Attributes\\PostCondition' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/PostCondition.php',
    'PHPUnit\\Framework\\Attributes\\PreCondition' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/PreCondition.php',
    'PHPUnit\\Framework\\Attributes\\PreserveGlobalState' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/PreserveGlobalState.php',
    'PHPUnit\\Framework\\Attributes\\RequiresFunction' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/RequiresFunction.php',
    'PHPUnit\\Framework\\Attributes\\RequiresMethod' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/RequiresMethod.php',
    'PHPUnit\\Framework\\Attributes\\RequiresOperatingSystem' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/RequiresOperatingSystem.php',
    'PHPUnit\\Framework\\Attributes\\RequiresOperatingSystemFamily' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/RequiresOperatingSystemFamily.php',
    'PHPUnit\\Framework\\Attributes\\RequiresPhp' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/RequiresPhp.php',
    'PHPUnit\\Framework\\Attributes\\RequiresPhpExtension' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/RequiresPhpExtension.php',
    'PHPUnit\\Framework\\Attributes\\RequiresPhpunit' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/RequiresPhpunit.php',
    'PHPUnit\\Framework\\Attributes\\RequiresSetting' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/RequiresSetting.php',
    'PHPUnit\\Framework\\Attributes\\RunClassInSeparateProcess' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/RunClassInSeparateProcess.php',
    'PHPUnit\\Framework\\Attributes\\RunInSeparateProcess' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/RunInSeparateProcess.php',
    'PHPUnit\\Framework\\Attributes\\RunTestsInSeparateProcesses' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/RunTestsInSeparateProcesses.php',
    'PHPUnit\\Framework\\Attributes\\Small' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/Small.php',
    'PHPUnit\\Framework\\Attributes\\Test' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/Test.php',
    'PHPUnit\\Framework\\Attributes\\TestDox' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/TestDox.php',
    'PHPUnit\\Framework\\Attributes\\TestWith' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/TestWith.php',
    'PHPUnit\\Framework\\Attributes\\TestWithJson' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/TestWithJson.php',
    'PHPUnit\\Framework\\Attributes\\Ticket' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/Ticket.php',
    'PHPUnit\\Framework\\Attributes\\UsesClass' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/UsesClass.php',
    'PHPUnit\\Framework\\Attributes\\UsesFunction' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/UsesFunction.php',
    'PHPUnit\\Framework\\Attributes\\WithoutErrorHandler' => $vendorDir . '/phpunit/phpunit/src/Framework/Attributes/WithoutErrorHandler.php',
    'PHPUnit\\Framework\\CodeCoverageException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/CodeCoverageException.php',
    'PHPUnit\\Framework\\ComparisonMethodDoesNotAcceptParameterTypeException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/ObjectEquals/ComparisonMethodDoesNotAcceptParameterTypeException.php',
    'PHPUnit\\Framework\\ComparisonMethodDoesNotDeclareBoolReturnTypeException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/ObjectEquals/ComparisonMethodDoesNotDeclareBoolReturnTypeException.php',
    'PHPUnit\\Framework\\ComparisonMethodDoesNotDeclareExactlyOneParameterException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/ObjectEquals/ComparisonMethodDoesNotDeclareExactlyOneParameterException.php',
    'PHPUnit\\Framework\\ComparisonMethodDoesNotDeclareParameterTypeException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/ObjectEquals/ComparisonMethodDoesNotDeclareParameterTypeException.php',
    'PHPUnit\\Framework\\ComparisonMethodDoesNotExistException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/ObjectEquals/ComparisonMethodDoesNotExistException.php',
    'PHPUnit\\Framework\\Constraint\\ArrayHasKey' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Traversable/ArrayHasKey.php',
    'PHPUnit\\Framework\\Constraint\\BinaryOperator' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Operator/BinaryOperator.php',
    'PHPUnit\\Framework\\Constraint\\Callback' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Callback.php',
    'PHPUnit\\Framework\\Constraint\\Constraint' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Constraint.php',
    'PHPUnit\\Framework\\Constraint\\Count' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Cardinality/Count.php',
    'PHPUnit\\Framework\\Constraint\\DirectoryExists' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Filesystem/DirectoryExists.php',
    'PHPUnit\\Framework\\Constraint\\Exception' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Exception/Exception.php',
    'PHPUnit\\Framework\\Constraint\\ExceptionCode' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Exception/ExceptionCode.php',
    'PHPUnit\\Framework\\Constraint\\ExceptionMessageIsOrContains' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Exception/ExceptionMessageIsOrContains.php',
    'PHPUnit\\Framework\\Constraint\\ExceptionMessageMatchesRegularExpression' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Exception/ExceptionMessageMatchesRegularExpression.php',
    'PHPUnit\\Framework\\Constraint\\FileExists' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Filesystem/FileExists.php',
    'PHPUnit\\Framework\\Constraint\\GreaterThan' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Cardinality/GreaterThan.php',
    'PHPUnit\\Framework\\Constraint\\IsAnything' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/IsAnything.php',
    'PHPUnit\\Framework\\Constraint\\IsEmpty' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Cardinality/IsEmpty.php',
    'PHPUnit\\Framework\\Constraint\\IsEqual' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Equality/IsEqual.php',
    'PHPUnit\\Framework\\Constraint\\IsEqualCanonicalizing' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Equality/IsEqualCanonicalizing.php',
    'PHPUnit\\Framework\\Constraint\\IsEqualIgnoringCase' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Equality/IsEqualIgnoringCase.php',
    'PHPUnit\\Framework\\Constraint\\IsEqualWithDelta' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Equality/IsEqualWithDelta.php',
    'PHPUnit\\Framework\\Constraint\\IsFalse' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Boolean/IsFalse.php',
    'PHPUnit\\Framework\\Constraint\\IsFinite' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Math/IsFinite.php',
    'PHPUnit\\Framework\\Constraint\\IsIdentical' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/IsIdentical.php',
    'PHPUnit\\Framework\\Constraint\\IsInfinite' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Math/IsInfinite.php',
    'PHPUnit\\Framework\\Constraint\\IsInstanceOf' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Type/IsInstanceOf.php',
    'PHPUnit\\Framework\\Constraint\\IsJson' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/String/IsJson.php',
    'PHPUnit\\Framework\\Constraint\\IsList' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Traversable/IsList.php',
    'PHPUnit\\Framework\\Constraint\\IsNan' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Math/IsNan.php',
    'PHPUnit\\Framework\\Constraint\\IsNull' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Type/IsNull.php',
    'PHPUnit\\Framework\\Constraint\\IsReadable' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Filesystem/IsReadable.php',
    'PHPUnit\\Framework\\Constraint\\IsTrue' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Boolean/IsTrue.php',
    'PHPUnit\\Framework\\Constraint\\IsType' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Type/IsType.php',
    'PHPUnit\\Framework\\Constraint\\IsWritable' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Filesystem/IsWritable.php',
    'PHPUnit\\Framework\\Constraint\\JsonMatches' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/JsonMatches.php',
    'PHPUnit\\Framework\\Constraint\\LessThan' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Cardinality/LessThan.php',
    'PHPUnit\\Framework\\Constraint\\LogicalAnd' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Operator/LogicalAnd.php',
    'PHPUnit\\Framework\\Constraint\\LogicalNot' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Operator/LogicalNot.php',
    'PHPUnit\\Framework\\Constraint\\LogicalOr' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Operator/LogicalOr.php',
    'PHPUnit\\Framework\\Constraint\\LogicalXor' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Operator/LogicalXor.php',
    'PHPUnit\\Framework\\Constraint\\ObjectEquals' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Object/ObjectEquals.php',
    'PHPUnit\\Framework\\Constraint\\ObjectHasProperty' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Object/ObjectHasProperty.php',
    'PHPUnit\\Framework\\Constraint\\Operator' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Operator/Operator.php',
    'PHPUnit\\Framework\\Constraint\\RegularExpression' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/String/RegularExpression.php',
    'PHPUnit\\Framework\\Constraint\\SameSize' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Cardinality/SameSize.php',
    'PHPUnit\\Framework\\Constraint\\StringContains' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/String/StringContains.php',
    'PHPUnit\\Framework\\Constraint\\StringEndsWith' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/String/StringEndsWith.php',
    'PHPUnit\\Framework\\Constraint\\StringEqualsStringIgnoringLineEndings' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/String/StringEqualsStringIgnoringLineEndings.php',
    'PHPUnit\\Framework\\Constraint\\StringMatchesFormatDescription' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/String/StringMatchesFormatDescription.php',
    'PHPUnit\\Framework\\Constraint\\StringStartsWith' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/String/StringStartsWith.php',
    'PHPUnit\\Framework\\Constraint\\TraversableContains' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Traversable/TraversableContains.php',
    'PHPUnit\\Framework\\Constraint\\TraversableContainsEqual' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Traversable/TraversableContainsEqual.php',
    'PHPUnit\\Framework\\Constraint\\TraversableContainsIdentical' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Traversable/TraversableContainsIdentical.php',
    'PHPUnit\\Framework\\Constraint\\TraversableContainsOnly' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Traversable/TraversableContainsOnly.php',
    'PHPUnit\\Framework\\Constraint\\UnaryOperator' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Operator/UnaryOperator.php',
    'PHPUnit\\Framework\\DataProviderTestSuite' => $vendorDir . '/phpunit/phpunit/src/Framework/DataProviderTestSuite.php',
    'PHPUnit\\Framework\\EmptyStringException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/EmptyStringException.php',
    'PHPUnit\\Framework\\Exception' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/Exception.php',
    'PHPUnit\\Framework\\ExecutionOrderDependency' => $vendorDir . '/phpunit/phpunit/src/Framework/ExecutionOrderDependency.php',
    'PHPUnit\\Framework\\ExpectationFailedException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/ExpectationFailedException.php',
    'PHPUnit\\Framework\\GeneratorNotSupportedException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/GeneratorNotSupportedException.php',
    'PHPUnit\\Framework\\IncompleteTest' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/Incomplete/IncompleteTest.php',
    'PHPUnit\\Framework\\IncompleteTestError' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/Incomplete/IncompleteTestError.php',
    'PHPUnit\\Framework\\InvalidArgumentException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/InvalidArgumentException.php',
    'PHPUnit\\Framework\\InvalidCoversTargetException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/InvalidCoversTargetException.php',
    'PHPUnit\\Framework\\InvalidDataProviderException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/InvalidDataProviderException.php',
    'PHPUnit\\Framework\\InvalidDependencyException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/InvalidDependencyException.php',
    'PHPUnit\\Framework\\MockObject\\BadMethodCallException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/BadMethodCallException.php',
    'PHPUnit\\Framework\\MockObject\\Builder\\Identity' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Builder/Identity.php',
    'PHPUnit\\Framework\\MockObject\\Builder\\InvocationMocker' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Builder/InvocationMocker.php',
    'PHPUnit\\Framework\\MockObject\\Builder\\InvocationStubber' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Builder/InvocationStubber.php',
    'PHPUnit\\Framework\\MockObject\\Builder\\MethodNameMatch' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Builder/MethodNameMatch.php',
    'PHPUnit\\Framework\\MockObject\\Builder\\ParametersMatch' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Builder/ParametersMatch.php',
    'PHPUnit\\Framework\\MockObject\\Builder\\Stub' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Builder/Stub.php',
    'PHPUnit\\Framework\\MockObject\\CannotUseOnlyMethodsException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/CannotUseOnlyMethodsException.php',
    'PHPUnit\\Framework\\MockObject\\ConfigurableMethod' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/ConfigurableMethod.php',
    'PHPUnit\\Framework\\MockObject\\DoubledCloneMethod' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Api/DoubledCloneMethod.php',
    'PHPUnit\\Framework\\MockObject\\Exception' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/Exception.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\CannotUseAddMethodsException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/CannotUseAddMethodsException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\ClassIsEnumerationException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/ClassIsEnumerationException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\ClassIsFinalException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/ClassIsFinalException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\ClassIsReadonlyException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/ClassIsReadonlyException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\DuplicateMethodException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/DuplicateMethodException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\Exception' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/Exception.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\Generator' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Generator.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\InvalidMethodNameException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/InvalidMethodNameException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\MockClass' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/MockClass.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\MockMethod' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/MockMethod.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\MockMethodSet' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/MockMethodSet.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\MockTrait' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/MockTrait.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\MockType' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/MockType.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\NameAlreadyInUseException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/NameAlreadyInUseException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\OriginalConstructorInvocationRequiredException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/OriginalConstructorInvocationRequiredException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\ReflectionException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/ReflectionException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\RuntimeException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/RuntimeException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\SoapExtensionNotAvailableException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/SoapExtensionNotAvailableException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\TemplateLoader' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/TemplateLoader.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\UnknownClassException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/UnknownClassException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\UnknownTraitException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/UnknownTraitException.php',
    'PHPUnit\\Framework\\MockObject\\Generator\\UnknownTypeException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator/Exception/UnknownTypeException.php',
    'PHPUnit\\Framework\\MockObject\\IncompatibleReturnValueException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/IncompatibleReturnValueException.php',
    'PHPUnit\\Framework\\MockObject\\Invocation' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Invocation.php',
    'PHPUnit\\Framework\\MockObject\\InvocationHandler' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/InvocationHandler.php',
    'PHPUnit\\Framework\\MockObject\\MatchBuilderNotFoundException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/MatchBuilderNotFoundException.php',
    'PHPUnit\\Framework\\MockObject\\Matcher' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Matcher.php',
    'PHPUnit\\Framework\\MockObject\\MatcherAlreadyRegisteredException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/MatcherAlreadyRegisteredException.php',
    'PHPUnit\\Framework\\MockObject\\Method' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Api/Method.php',
    'PHPUnit\\Framework\\MockObject\\MethodCannotBeConfiguredException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/MethodCannotBeConfiguredException.php',
    'PHPUnit\\Framework\\MockObject\\MethodNameAlreadyConfiguredException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/MethodNameAlreadyConfiguredException.php',
    'PHPUnit\\Framework\\MockObject\\MethodNameConstraint' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/MethodNameConstraint.php',
    'PHPUnit\\Framework\\MockObject\\MethodNameNotConfiguredException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/MethodNameNotConfiguredException.php',
    'PHPUnit\\Framework\\MockObject\\MethodParametersAlreadyConfiguredException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/MethodParametersAlreadyConfiguredException.php',
    'PHPUnit\\Framework\\MockObject\\MockBuilder' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/MockBuilder.php',
    'PHPUnit\\Framework\\MockObject\\MockObject' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Interface/MockObject.php',
    'PHPUnit\\Framework\\MockObject\\MockObjectApi' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Api/MockObjectApi.php',
    'PHPUnit\\Framework\\MockObject\\MockObjectInternal' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Interface/MockObjectInternal.php',
    'PHPUnit\\Framework\\MockObject\\NeverReturningMethodException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/NeverReturningMethodException.php',
    'PHPUnit\\Framework\\MockObject\\NoMoreReturnValuesConfiguredException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/NoMoreReturnValuesConfiguredException.php',
    'PHPUnit\\Framework\\MockObject\\ProxiedCloneMethod' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Api/ProxiedCloneMethod.php',
    'PHPUnit\\Framework\\MockObject\\ReturnValueGenerator' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/ReturnValueGenerator.php',
    'PHPUnit\\Framework\\MockObject\\ReturnValueNotConfiguredException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/ReturnValueNotConfiguredException.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\AnyInvokedCount' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Rule/AnyInvokedCount.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\AnyParameters' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Rule/AnyParameters.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\InvocationOrder' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Rule/InvocationOrder.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\InvokedAtLeastCount' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Rule/InvokedAtLeastCount.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\InvokedAtLeastOnce' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Rule/InvokedAtLeastOnce.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\InvokedAtMostCount' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Rule/InvokedAtMostCount.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\InvokedCount' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Rule/InvokedCount.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\MethodName' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Rule/MethodName.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\Parameters' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Rule/Parameters.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\ParametersRule' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Rule/ParametersRule.php',
    'PHPUnit\\Framework\\MockObject\\RuntimeException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/RuntimeException.php',
    'PHPUnit\\Framework\\MockObject\\Stub' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Interface/Stub.php',
    'PHPUnit\\Framework\\MockObject\\StubApi' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Api/StubApi.php',
    'PHPUnit\\Framework\\MockObject\\StubInternal' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Interface/StubInternal.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\ConsecutiveCalls' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Stub/ConsecutiveCalls.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\Exception' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Stub/Exception.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\ReturnArgument' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Stub/ReturnArgument.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\ReturnCallback' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Stub/ReturnCallback.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\ReturnReference' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Stub/ReturnReference.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\ReturnSelf' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Stub/ReturnSelf.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\ReturnStub' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Stub/ReturnStub.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\ReturnValueMap' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Stub/ReturnValueMap.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\Stub' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Runtime/Stub/Stub.php',
    'PHPUnit\\Framework\\NoChildTestSuiteException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/NoChildTestSuiteException.php',
    'PHPUnit\\Framework\\PhptAssertionFailedError' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/PhptAssertionFailedError.php',
    'PHPUnit\\Framework\\ProcessIsolationException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/ProcessIsolationException.php',
    'PHPUnit\\Framework\\Reorderable' => $vendorDir . '/phpunit/phpunit/src/Framework/Reorderable.php',
    'PHPUnit\\Framework\\SelfDescribing' => $vendorDir . '/phpunit/phpunit/src/Framework/SelfDescribing.php',
    'PHPUnit\\Framework\\SkippedTest' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/Skipped/SkippedTest.php',
    'PHPUnit\\Framework\\SkippedTestSuiteError' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/Skipped/SkippedTestSuiteError.php',
    'PHPUnit\\Framework\\SkippedWithMessageException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/Skipped/SkippedWithMessageException.php',
    'PHPUnit\\Framework\\Test' => $vendorDir . '/phpunit/phpunit/src/Framework/Test.php',
    'PHPUnit\\Framework\\TestBuilder' => $vendorDir . '/phpunit/phpunit/src/Framework/TestBuilder.php',
    'PHPUnit\\Framework\\TestCase' => $vendorDir . '/phpunit/phpunit/src/Framework/TestCase.php',
    'PHPUnit\\Framework\\TestRunner' => $vendorDir . '/phpunit/phpunit/src/Framework/TestRunner.php',
    'PHPUnit\\Framework\\TestSize\\Known' => $vendorDir . '/phpunit/phpunit/src/Framework/TestSize/Known.php',
    'PHPUnit\\Framework\\TestSize\\Large' => $vendorDir . '/phpunit/phpunit/src/Framework/TestSize/Large.php',
    'PHPUnit\\Framework\\TestSize\\Medium' => $vendorDir . '/phpunit/phpunit/src/Framework/TestSize/Medium.php',
    'PHPUnit\\Framework\\TestSize\\Small' => $vendorDir . '/phpunit/phpunit/src/Framework/TestSize/Small.php',
    'PHPUnit\\Framework\\TestSize\\TestSize' => $vendorDir . '/phpunit/phpunit/src/Framework/TestSize/TestSize.php',
    'PHPUnit\\Framework\\TestSize\\Unknown' => $vendorDir . '/phpunit/phpunit/src/Framework/TestSize/Unknown.php',
    'PHPUnit\\Framework\\TestStatus\\Deprecation' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/Deprecation.php',
    'PHPUnit\\Framework\\TestStatus\\Error' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/Error.php',
    'PHPUnit\\Framework\\TestStatus\\Failure' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/Failure.php',
    'PHPUnit\\Framework\\TestStatus\\Incomplete' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/Incomplete.php',
    'PHPUnit\\Framework\\TestStatus\\Known' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/Known.php',
    'PHPUnit\\Framework\\TestStatus\\Notice' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/Notice.php',
    'PHPUnit\\Framework\\TestStatus\\Risky' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/Risky.php',
    'PHPUnit\\Framework\\TestStatus\\Skipped' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/Skipped.php',
    'PHPUnit\\Framework\\TestStatus\\Success' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/Success.php',
    'PHPUnit\\Framework\\TestStatus\\TestStatus' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/TestStatus.php',
    'PHPUnit\\Framework\\TestStatus\\Unknown' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/Unknown.php',
    'PHPUnit\\Framework\\TestStatus\\Warning' => $vendorDir . '/phpunit/phpunit/src/Framework/TestStatus/Warning.php',
    'PHPUnit\\Framework\\TestSuite' => $vendorDir . '/phpunit/phpunit/src/Framework/TestSuite.php',
    'PHPUnit\\Framework\\TestSuiteIterator' => $vendorDir . '/phpunit/phpunit/src/Framework/TestSuiteIterator.php',
    'PHPUnit\\Framework\\UnknownClassOrInterfaceException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/UnknownClassOrInterfaceException.php',
    'PHPUnit\\Framework\\UnknownTypeException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/UnknownTypeException.php',
    'PHPUnit\\Logging\\EventLogger' => $vendorDir . '/phpunit/phpunit/src/Logging/EventLogger.php',
    'PHPUnit\\Logging\\JUnit\\JunitXmlLogger' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/JunitXmlLogger.php',
    'PHPUnit\\Logging\\JUnit\\Subscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/Subscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestErroredSubscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestFailedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestFailedSubscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestFinishedSubscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestMarkedIncompleteSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestMarkedIncompleteSubscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestPreparationFailedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestPreparationFailedSubscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestPreparationStartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestPreparationStartedSubscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestPreparedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestPreparedSubscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestPrintedUnexpectedOutputSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestPrintedUnexpectedOutputSubscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestRunnerExecutionFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestRunnerExecutionFinishedSubscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestSkippedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestSkippedSubscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestSuiteFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestSuiteFinishedSubscriber.php',
    'PHPUnit\\Logging\\JUnit\\TestSuiteStartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/JUnit/Subscriber/TestSuiteStartedSubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\Subscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/Subscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TeamCityLogger' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/TeamCityLogger.php',
    'PHPUnit\\Logging\\TeamCity\\TestConsideredRiskySubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestConsideredRiskySubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TestErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestErroredSubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TestFailedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestFailedSubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TestFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestFinishedSubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TestMarkedIncompleteSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestMarkedIncompleteSubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TestPreparedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestPreparedSubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TestRunnerExecutionFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestRunnerExecutionFinishedSubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TestSkippedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestSkippedSubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TestSuiteBeforeFirstTestMethodErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestSuiteBeforeFirstTestMethodErroredSubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TestSuiteFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestSuiteFinishedSubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TestSuiteSkippedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestSuiteSkippedSubscriber.php',
    'PHPUnit\\Logging\\TeamCity\\TestSuiteStartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TeamCity/Subscriber/TestSuiteStartedSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\HtmlRenderer' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/HtmlRenderer.php',
    'PHPUnit\\Logging\\TestDox\\NamePrettifier' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/NamePrettifier.php',
    'PHPUnit\\Logging\\TestDox\\PlainTextRenderer' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/PlainTextRenderer.php',
    'PHPUnit\\Logging\\TestDox\\Subscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/Subscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestConsideredRiskySubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestConsideredRiskySubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestErroredSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestFailedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestFailedSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestFinishedSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestMarkedIncompleteSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestMarkedIncompleteSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestPassedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestPassedSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestPreparedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestPreparedSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestResult' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/TestResult.php',
    'PHPUnit\\Logging\\TestDox\\TestResultCollection' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/TestResultCollection.php',
    'PHPUnit\\Logging\\TestDox\\TestResultCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/TestResultCollectionIterator.php',
    'PHPUnit\\Logging\\TestDox\\TestResultCollector' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/TestResultCollector.php',
    'PHPUnit\\Logging\\TestDox\\TestSkippedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestSkippedSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestTriggeredDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestTriggeredDeprecationSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestTriggeredNoticeSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestTriggeredNoticeSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestTriggeredPhpDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestTriggeredPhpDeprecationSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestTriggeredPhpNoticeSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestTriggeredPhpNoticeSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestTriggeredPhpWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestTriggeredPhpWarningSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestTriggeredPhpunitDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestTriggeredPhpunitDeprecationSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestTriggeredPhpunitErrorSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestTriggeredPhpunitErrorSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestTriggeredPhpunitWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestTriggeredPhpunitWarningSubscriber.php',
    'PHPUnit\\Logging\\TestDox\\TestTriggeredWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/Logging/TestDox/TestResult/Subscriber/TestTriggeredWarningSubscriber.php',
    'PHPUnit\\Metadata\\After' => $vendorDir . '/phpunit/phpunit/src/Metadata/After.php',
    'PHPUnit\\Metadata\\AfterClass' => $vendorDir . '/phpunit/phpunit/src/Metadata/AfterClass.php',
    'PHPUnit\\Metadata\\Annotation\\Parser\\DocBlock' => $vendorDir . '/phpunit/phpunit/src/Metadata/Parser/Annotation/DocBlock.php',
    'PHPUnit\\Metadata\\Annotation\\Parser\\Registry' => $vendorDir . '/phpunit/phpunit/src/Metadata/Parser/Annotation/Registry.php',
    'PHPUnit\\Metadata\\AnnotationsAreNotSupportedForInternalClassesException' => $vendorDir . '/phpunit/phpunit/src/Metadata/Exception/AnnotationsAreNotSupportedForInternalClassesException.php',
    'PHPUnit\\Metadata\\Api\\CodeCoverage' => $vendorDir . '/phpunit/phpunit/src/Metadata/Api/CodeCoverage.php',
    'PHPUnit\\Metadata\\Api\\DataProvider' => $vendorDir . '/phpunit/phpunit/src/Metadata/Api/DataProvider.php',
    'PHPUnit\\Metadata\\Api\\Dependencies' => $vendorDir . '/phpunit/phpunit/src/Metadata/Api/Dependencies.php',
    'PHPUnit\\Metadata\\Api\\Groups' => $vendorDir . '/phpunit/phpunit/src/Metadata/Api/Groups.php',
    'PHPUnit\\Metadata\\Api\\HookMethods' => $vendorDir . '/phpunit/phpunit/src/Metadata/Api/HookMethods.php',
    'PHPUnit\\Metadata\\Api\\Requirements' => $vendorDir . '/phpunit/phpunit/src/Metadata/Api/Requirements.php',
    'PHPUnit\\Metadata\\BackupGlobals' => $vendorDir . '/phpunit/phpunit/src/Metadata/BackupGlobals.php',
    'PHPUnit\\Metadata\\BackupStaticProperties' => $vendorDir . '/phpunit/phpunit/src/Metadata/BackupStaticProperties.php',
    'PHPUnit\\Metadata\\Before' => $vendorDir . '/phpunit/phpunit/src/Metadata/Before.php',
    'PHPUnit\\Metadata\\BeforeClass' => $vendorDir . '/phpunit/phpunit/src/Metadata/BeforeClass.php',
    'PHPUnit\\Metadata\\Covers' => $vendorDir . '/phpunit/phpunit/src/Metadata/Covers.php',
    'PHPUnit\\Metadata\\CoversClass' => $vendorDir . '/phpunit/phpunit/src/Metadata/CoversClass.php',
    'PHPUnit\\Metadata\\CoversDefaultClass' => $vendorDir . '/phpunit/phpunit/src/Metadata/CoversDefaultClass.php',
    'PHPUnit\\Metadata\\CoversFunction' => $vendorDir . '/phpunit/phpunit/src/Metadata/CoversFunction.php',
    'PHPUnit\\Metadata\\CoversNothing' => $vendorDir . '/phpunit/phpunit/src/Metadata/CoversNothing.php',
    'PHPUnit\\Metadata\\DataProvider' => $vendorDir . '/phpunit/phpunit/src/Metadata/DataProvider.php',
    'PHPUnit\\Metadata\\DependsOnClass' => $vendorDir . '/phpunit/phpunit/src/Metadata/DependsOnClass.php',
    'PHPUnit\\Metadata\\DependsOnMethod' => $vendorDir . '/phpunit/phpunit/src/Metadata/DependsOnMethod.php',
    'PHPUnit\\Metadata\\DoesNotPerformAssertions' => $vendorDir . '/phpunit/phpunit/src/Metadata/DoesNotPerformAssertions.php',
    'PHPUnit\\Metadata\\Exception' => $vendorDir . '/phpunit/phpunit/src/Metadata/Exception/Exception.php',
    'PHPUnit\\Metadata\\ExcludeGlobalVariableFromBackup' => $vendorDir . '/phpunit/phpunit/src/Metadata/ExcludeGlobalVariableFromBackup.php',
    'PHPUnit\\Metadata\\ExcludeStaticPropertyFromBackup' => $vendorDir . '/phpunit/phpunit/src/Metadata/ExcludeStaticPropertyFromBackup.php',
    'PHPUnit\\Metadata\\Group' => $vendorDir . '/phpunit/phpunit/src/Metadata/Group.php',
    'PHPUnit\\Metadata\\IgnoreClassForCodeCoverage' => $vendorDir . '/phpunit/phpunit/src/Metadata/IgnoreClassForCodeCoverage.php',
    'PHPUnit\\Metadata\\IgnoreDeprecations' => $vendorDir . '/phpunit/phpunit/src/Metadata/IgnoreDeprecations.php',
    'PHPUnit\\Metadata\\IgnoreFunctionForCodeCoverage' => $vendorDir . '/phpunit/phpunit/src/Metadata/IgnoreFunctionForCodeCoverage.php',
    'PHPUnit\\Metadata\\IgnoreMethodForCodeCoverage' => $vendorDir . '/phpunit/phpunit/src/Metadata/IgnoreMethodForCodeCoverage.php',
    'PHPUnit\\Metadata\\InvalidAttributeException' => $vendorDir . '/phpunit/phpunit/src/Metadata/Exception/InvalidAttributeException.php',
    'PHPUnit\\Metadata\\InvalidVersionRequirementException' => $vendorDir . '/phpunit/phpunit/src/Metadata/Exception/InvalidVersionRequirementException.php',
    'PHPUnit\\Metadata\\Metadata' => $vendorDir . '/phpunit/phpunit/src/Metadata/Metadata.php',
    'PHPUnit\\Metadata\\MetadataCollection' => $vendorDir . '/phpunit/phpunit/src/Metadata/MetadataCollection.php',
    'PHPUnit\\Metadata\\MetadataCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/Metadata/MetadataCollectionIterator.php',
    'PHPUnit\\Metadata\\NoVersionRequirementException' => $vendorDir . '/phpunit/phpunit/src/Metadata/Exception/NoVersionRequirementException.php',
    'PHPUnit\\Metadata\\Parser\\AnnotationParser' => $vendorDir . '/phpunit/phpunit/src/Metadata/Parser/AnnotationParser.php',
    'PHPUnit\\Metadata\\Parser\\AttributeParser' => $vendorDir . '/phpunit/phpunit/src/Metadata/Parser/AttributeParser.php',
    'PHPUnit\\Metadata\\Parser\\CachingParser' => $vendorDir . '/phpunit/phpunit/src/Metadata/Parser/CachingParser.php',
    'PHPUnit\\Metadata\\Parser\\Parser' => $vendorDir . '/phpunit/phpunit/src/Metadata/Parser/Parser.php',
    'PHPUnit\\Metadata\\Parser\\ParserChain' => $vendorDir . '/phpunit/phpunit/src/Metadata/Parser/ParserChain.php',
    'PHPUnit\\Metadata\\Parser\\Registry' => $vendorDir . '/phpunit/phpunit/src/Metadata/Parser/Registry.php',
    'PHPUnit\\Metadata\\PostCondition' => $vendorDir . '/phpunit/phpunit/src/Metadata/PostCondition.php',
    'PHPUnit\\Metadata\\PreCondition' => $vendorDir . '/phpunit/phpunit/src/Metadata/PreCondition.php',
    'PHPUnit\\Metadata\\PreserveGlobalState' => $vendorDir . '/phpunit/phpunit/src/Metadata/PreserveGlobalState.php',
    'PHPUnit\\Metadata\\ReflectionException' => $vendorDir . '/phpunit/phpunit/src/Metadata/Exception/ReflectionException.php',
    'PHPUnit\\Metadata\\RequiresFunction' => $vendorDir . '/phpunit/phpunit/src/Metadata/RequiresFunction.php',
    'PHPUnit\\Metadata\\RequiresMethod' => $vendorDir . '/phpunit/phpunit/src/Metadata/RequiresMethod.php',
    'PHPUnit\\Metadata\\RequiresOperatingSystem' => $vendorDir . '/phpunit/phpunit/src/Metadata/RequiresOperatingSystem.php',
    'PHPUnit\\Metadata\\RequiresOperatingSystemFamily' => $vendorDir . '/phpunit/phpunit/src/Metadata/RequiresOperatingSystemFamily.php',
    'PHPUnit\\Metadata\\RequiresPhp' => $vendorDir . '/phpunit/phpunit/src/Metadata/RequiresPhp.php',
    'PHPUnit\\Metadata\\RequiresPhpExtension' => $vendorDir . '/phpunit/phpunit/src/Metadata/RequiresPhpExtension.php',
    'PHPUnit\\Metadata\\RequiresPhpunit' => $vendorDir . '/phpunit/phpunit/src/Metadata/RequiresPhpunit.php',
    'PHPUnit\\Metadata\\RequiresSetting' => $vendorDir . '/phpunit/phpunit/src/Metadata/RequiresSetting.php',
    'PHPUnit\\Metadata\\RunClassInSeparateProcess' => $vendorDir . '/phpunit/phpunit/src/Metadata/RunClassInSeparateProcess.php',
    'PHPUnit\\Metadata\\RunInSeparateProcess' => $vendorDir . '/phpunit/phpunit/src/Metadata/RunInSeparateProcess.php',
    'PHPUnit\\Metadata\\RunTestsInSeparateProcesses' => $vendorDir . '/phpunit/phpunit/src/Metadata/RunTestsInSeparateProcesses.php',
    'PHPUnit\\Metadata\\Test' => $vendorDir . '/phpunit/phpunit/src/Metadata/Test.php',
    'PHPUnit\\Metadata\\TestDox' => $vendorDir . '/phpunit/phpunit/src/Metadata/TestDox.php',
    'PHPUnit\\Metadata\\TestWith' => $vendorDir . '/phpunit/phpunit/src/Metadata/TestWith.php',
    'PHPUnit\\Metadata\\Uses' => $vendorDir . '/phpunit/phpunit/src/Metadata/Uses.php',
    'PHPUnit\\Metadata\\UsesClass' => $vendorDir . '/phpunit/phpunit/src/Metadata/UsesClass.php',
    'PHPUnit\\Metadata\\UsesDefaultClass' => $vendorDir . '/phpunit/phpunit/src/Metadata/UsesDefaultClass.php',
    'PHPUnit\\Metadata\\UsesFunction' => $vendorDir . '/phpunit/phpunit/src/Metadata/UsesFunction.php',
    'PHPUnit\\Metadata\\Version\\ComparisonRequirement' => $vendorDir . '/phpunit/phpunit/src/Metadata/Version/ComparisonRequirement.php',
    'PHPUnit\\Metadata\\Version\\ConstraintRequirement' => $vendorDir . '/phpunit/phpunit/src/Metadata/Version/ConstraintRequirement.php',
    'PHPUnit\\Metadata\\Version\\Requirement' => $vendorDir . '/phpunit/phpunit/src/Metadata/Version/Requirement.php',
    'PHPUnit\\Metadata\\WithoutErrorHandler' => $vendorDir . '/phpunit/phpunit/src/Metadata/WithoutErrorHandler.php',
    'PHPUnit\\Runner\\Baseline\\Baseline' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Baseline.php',
    'PHPUnit\\Runner\\Baseline\\CannotLoadBaselineException' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Exception/CannotLoadBaselineException.php',
    'PHPUnit\\Runner\\Baseline\\FileDoesNotHaveLineException' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Exception/FileDoesNotHaveLineException.php',
    'PHPUnit\\Runner\\Baseline\\Generator' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Generator.php',
    'PHPUnit\\Runner\\Baseline\\Issue' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Issue.php',
    'PHPUnit\\Runner\\Baseline\\Reader' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Reader.php',
    'PHPUnit\\Runner\\Baseline\\RelativePathCalculator' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/RelativePathCalculator.php',
    'PHPUnit\\Runner\\Baseline\\Subscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Subscriber/Subscriber.php',
    'PHPUnit\\Runner\\Baseline\\TestTriggeredDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Subscriber/TestTriggeredDeprecationSubscriber.php',
    'PHPUnit\\Runner\\Baseline\\TestTriggeredNoticeSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Subscriber/TestTriggeredNoticeSubscriber.php',
    'PHPUnit\\Runner\\Baseline\\TestTriggeredPhpDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Subscriber/TestTriggeredPhpDeprecationSubscriber.php',
    'PHPUnit\\Runner\\Baseline\\TestTriggeredPhpNoticeSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Subscriber/TestTriggeredPhpNoticeSubscriber.php',
    'PHPUnit\\Runner\\Baseline\\TestTriggeredPhpWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Subscriber/TestTriggeredPhpWarningSubscriber.php',
    'PHPUnit\\Runner\\Baseline\\TestTriggeredWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Subscriber/TestTriggeredWarningSubscriber.php',
    'PHPUnit\\Runner\\Baseline\\Writer' => $vendorDir . '/phpunit/phpunit/src/Runner/Baseline/Writer.php',
    'PHPUnit\\Runner\\ClassCannotBeFoundException' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/ClassCannotBeFoundException.php',
    'PHPUnit\\Runner\\ClassDoesNotExtendTestCaseException' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/ClassDoesNotExtendTestCaseException.php',
    'PHPUnit\\Runner\\ClassIsAbstractException' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/ClassIsAbstractException.php',
    'PHPUnit\\Runner\\CodeCoverage' => $vendorDir . '/phpunit/phpunit/src/Runner/CodeCoverage.php',
    'PHPUnit\\Runner\\DirectoryDoesNotExistException' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/DirectoryDoesNotExistException.php',
    'PHPUnit\\Runner\\ErrorException' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/ErrorException.php',
    'PHPUnit\\Runner\\ErrorHandler' => $vendorDir . '/phpunit/phpunit/src/Runner/ErrorHandler.php',
    'PHPUnit\\Runner\\Exception' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/Exception.php',
    'PHPUnit\\Runner\\Extension\\Extension' => $vendorDir . '/phpunit/phpunit/src/Runner/Extension/Extension.php',
    'PHPUnit\\Runner\\Extension\\ExtensionBootstrapper' => $vendorDir . '/phpunit/phpunit/src/Runner/Extension/ExtensionBootstrapper.php',
    'PHPUnit\\Runner\\Extension\\Facade' => $vendorDir . '/phpunit/phpunit/src/Runner/Extension/Facade.php',
    'PHPUnit\\Runner\\Extension\\ParameterCollection' => $vendorDir . '/phpunit/phpunit/src/Runner/Extension/ParameterCollection.php',
    'PHPUnit\\Runner\\Extension\\PharLoader' => $vendorDir . '/phpunit/phpunit/src/Runner/Extension/PharLoader.php',
    'PHPUnit\\Runner\\FileDoesNotExistException' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/FileDoesNotExistException.php',
    'PHPUnit\\Runner\\Filter\\ExcludeGroupFilterIterator' => $vendorDir . '/phpunit/phpunit/src/Runner/Filter/ExcludeGroupFilterIterator.php',
    'PHPUnit\\Runner\\Filter\\Factory' => $vendorDir . '/phpunit/phpunit/src/Runner/Filter/Factory.php',
    'PHPUnit\\Runner\\Filter\\GroupFilterIterator' => $vendorDir . '/phpunit/phpunit/src/Runner/Filter/GroupFilterIterator.php',
    'PHPUnit\\Runner\\Filter\\IncludeGroupFilterIterator' => $vendorDir . '/phpunit/phpunit/src/Runner/Filter/IncludeGroupFilterIterator.php',
    'PHPUnit\\Runner\\Filter\\NameFilterIterator' => $vendorDir . '/phpunit/phpunit/src/Runner/Filter/NameFilterIterator.php',
    'PHPUnit\\Runner\\Filter\\TestIdFilterIterator' => $vendorDir . '/phpunit/phpunit/src/Runner/Filter/TestIdFilterIterator.php',
    'PHPUnit\\Runner\\GarbageCollection\\ExecutionFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/GarbageCollection/Subscriber/ExecutionFinishedSubscriber.php',
    'PHPUnit\\Runner\\GarbageCollection\\ExecutionStartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/GarbageCollection/Subscriber/ExecutionStartedSubscriber.php',
    'PHPUnit\\Runner\\GarbageCollection\\GarbageCollectionHandler' => $vendorDir . '/phpunit/phpunit/src/Runner/GarbageCollection/GarbageCollectionHandler.php',
    'PHPUnit\\Runner\\GarbageCollection\\Subscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/GarbageCollection/Subscriber/Subscriber.php',
    'PHPUnit\\Runner\\GarbageCollection\\TestFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/GarbageCollection/Subscriber/TestFinishedSubscriber.php',
    'PHPUnit\\Runner\\InvalidOrderException' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/InvalidOrderException.php',
    'PHPUnit\\Runner\\InvalidPhptFileException' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/InvalidPhptFileException.php',
    'PHPUnit\\Runner\\ParameterDoesNotExistException' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/ParameterDoesNotExistException.php',
    'PHPUnit\\Runner\\PhptExternalFileCannotBeLoadedException' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/PhptExternalFileCannotBeLoadedException.php',
    'PHPUnit\\Runner\\PhptTestCase' => $vendorDir . '/phpunit/phpunit/src/Runner/PhptTestCase.php',
    'PHPUnit\\Runner\\ResultCache\\DefaultResultCache' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/DefaultResultCache.php',
    'PHPUnit\\Runner\\ResultCache\\NullResultCache' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/NullResultCache.php',
    'PHPUnit\\Runner\\ResultCache\\ResultCache' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/ResultCache.php',
    'PHPUnit\\Runner\\ResultCache\\ResultCacheHandler' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/ResultCacheHandler.php',
    'PHPUnit\\Runner\\ResultCache\\Subscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/Subscriber/Subscriber.php',
    'PHPUnit\\Runner\\ResultCache\\TestConsideredRiskySubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/Subscriber/TestConsideredRiskySubscriber.php',
    'PHPUnit\\Runner\\ResultCache\\TestErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/Subscriber/TestErroredSubscriber.php',
    'PHPUnit\\Runner\\ResultCache\\TestFailedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/Subscriber/TestFailedSubscriber.php',
    'PHPUnit\\Runner\\ResultCache\\TestFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/Subscriber/TestFinishedSubscriber.php',
    'PHPUnit\\Runner\\ResultCache\\TestMarkedIncompleteSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/Subscriber/TestMarkedIncompleteSubscriber.php',
    'PHPUnit\\Runner\\ResultCache\\TestPreparedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/Subscriber/TestPreparedSubscriber.php',
    'PHPUnit\\Runner\\ResultCache\\TestSkippedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/Subscriber/TestSkippedSubscriber.php',
    'PHPUnit\\Runner\\ResultCache\\TestSuiteFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/Subscriber/TestSuiteFinishedSubscriber.php',
    'PHPUnit\\Runner\\ResultCache\\TestSuiteStartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCache/Subscriber/TestSuiteStartedSubscriber.php',
    'PHPUnit\\Runner\\TestSuiteLoader' => $vendorDir . '/phpunit/phpunit/src/Runner/TestSuiteLoader.php',
    'PHPUnit\\Runner\\TestSuiteSorter' => $vendorDir . '/phpunit/phpunit/src/Runner/TestSuiteSorter.php',
    'PHPUnit\\Runner\\UnsupportedPhptSectionException' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception/UnsupportedPhptSectionException.php',
    'PHPUnit\\Runner\\Version' => $vendorDir . '/phpunit/phpunit/src/Runner/Version.php',
    'PHPUnit\\TestRunner\\TestResult\\AfterTestClassMethodErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/AfterTestClassMethodErroredSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\BeforeTestClassMethodErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/BeforeTestClassMethodErroredSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\Collector' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Collector.php',
    'PHPUnit\\TestRunner\\TestResult\\ExecutionStartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/ExecutionStartedSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\Facade' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Facade.php',
    'PHPUnit\\TestRunner\\TestResult\\Issues\\Issue' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Issue.php',
    'PHPUnit\\TestRunner\\TestResult\\PassedTests' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/PassedTests.php',
    'PHPUnit\\TestRunner\\TestResult\\Subscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/Subscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestConsideredRiskySubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestConsideredRiskySubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestErroredSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestFailedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestFailedSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestFinishedSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestMarkedIncompleteSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestMarkedIncompleteSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestPreparedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestPreparedSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestResult' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/TestResult.php',
    'PHPUnit\\TestRunner\\TestResult\\TestRunnerTriggeredDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestRunnerTriggeredDeprecationSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestRunnerTriggeredWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestRunnerTriggeredWarningSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestSkippedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestSkippedSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestSuiteFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestSuiteFinishedSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestSuiteSkippedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestSuiteSkippedSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestSuiteStartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestSuiteStartedSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestTriggeredDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestTriggeredDeprecationSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestTriggeredErrorSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestTriggeredErrorSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestTriggeredNoticeSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestTriggeredNoticeSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestTriggeredPhpDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestTriggeredPhpDeprecationSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestTriggeredPhpNoticeSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestTriggeredPhpNoticeSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestTriggeredPhpWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestTriggeredPhpWarningSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestTriggeredPhpunitDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestTriggeredPhpunitDeprecationSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestTriggeredPhpunitErrorSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestTriggeredPhpunitErrorSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestTriggeredPhpunitWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestTriggeredPhpunitWarningSubscriber.php',
    'PHPUnit\\TestRunner\\TestResult\\TestTriggeredWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResult/Subscriber/TestTriggeredWarningSubscriber.php',
    'PHPUnit\\TextUI\\Application' => $vendorDir . '/phpunit/phpunit/src/TextUI/Application.php',
    'PHPUnit\\TextUI\\CannotOpenSocketException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Exception/CannotOpenSocketException.php',
    'PHPUnit\\TextUI\\CliArguments\\Builder' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Cli/Builder.php',
    'PHPUnit\\TextUI\\CliArguments\\Configuration' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Cli/Configuration.php',
    'PHPUnit\\TextUI\\CliArguments\\Exception' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Cli/Exception.php',
    'PHPUnit\\TextUI\\CliArguments\\XmlConfigurationFileFinder' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Cli/XmlConfigurationFileFinder.php',
    'PHPUnit\\TextUI\\Command\\AtLeastVersionCommand' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Commands/AtLeastVersionCommand.php',
    'PHPUnit\\TextUI\\Command\\Command' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Command.php',
    'PHPUnit\\TextUI\\Command\\GenerateConfigurationCommand' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Commands/GenerateConfigurationCommand.php',
    'PHPUnit\\TextUI\\Command\\ListGroupsCommand' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Commands/ListGroupsCommand.php',
    'PHPUnit\\TextUI\\Command\\ListTestSuitesCommand' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Commands/ListTestSuitesCommand.php',
    'PHPUnit\\TextUI\\Command\\ListTestsAsTextCommand' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Commands/ListTestsAsTextCommand.php',
    'PHPUnit\\TextUI\\Command\\ListTestsAsXmlCommand' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Commands/ListTestsAsXmlCommand.php',
    'PHPUnit\\TextUI\\Command\\MigrateConfigurationCommand' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Commands/MigrateConfigurationCommand.php',
    'PHPUnit\\TextUI\\Command\\Result' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Result.php',
    'PHPUnit\\TextUI\\Command\\ShowHelpCommand' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Commands/ShowHelpCommand.php',
    'PHPUnit\\TextUI\\Command\\ShowVersionCommand' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Commands/ShowVersionCommand.php',
    'PHPUnit\\TextUI\\Command\\VersionCheckCommand' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Commands/VersionCheckCommand.php',
    'PHPUnit\\TextUI\\Command\\WarmCodeCoverageCacheCommand' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command/Commands/WarmCodeCoverageCacheCommand.php',
    'PHPUnit\\TextUI\\Configuration\\Builder' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Builder.php',
    'PHPUnit\\TextUI\\Configuration\\CodeCoverageFilterRegistry' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/CodeCoverageFilterRegistry.php',
    'PHPUnit\\TextUI\\Configuration\\CodeCoverageReportNotConfiguredException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/CodeCoverageReportNotConfiguredException.php',
    'PHPUnit\\TextUI\\Configuration\\Configuration' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Configuration.php',
    'PHPUnit\\TextUI\\Configuration\\ConfigurationCannotBeBuiltException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/ConfigurationCannotBeBuiltException.php',
    'PHPUnit\\TextUI\\Configuration\\Constant' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/Constant.php',
    'PHPUnit\\TextUI\\Configuration\\ConstantCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/ConstantCollection.php',
    'PHPUnit\\TextUI\\Configuration\\ConstantCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/ConstantCollectionIterator.php',
    'PHPUnit\\TextUI\\Configuration\\Directory' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/Directory.php',
    'PHPUnit\\TextUI\\Configuration\\DirectoryCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/DirectoryCollection.php',
    'PHPUnit\\TextUI\\Configuration\\DirectoryCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/DirectoryCollectionIterator.php',
    'PHPUnit\\TextUI\\Configuration\\Exception' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/Exception.php',
    'PHPUnit\\TextUI\\Configuration\\ExtensionBootstrap' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/ExtensionBootstrap.php',
    'PHPUnit\\TextUI\\Configuration\\ExtensionBootstrapCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/ExtensionBootstrapCollection.php',
    'PHPUnit\\TextUI\\Configuration\\ExtensionBootstrapCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/ExtensionBootstrapCollectionIterator.php',
    'PHPUnit\\TextUI\\Configuration\\File' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/File.php',
    'PHPUnit\\TextUI\\Configuration\\FileCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/FileCollection.php',
    'PHPUnit\\TextUI\\Configuration\\FileCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/FileCollectionIterator.php',
    'PHPUnit\\TextUI\\Configuration\\FilterDirectory' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/FilterDirectory.php',
    'PHPUnit\\TextUI\\Configuration\\FilterDirectoryCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/FilterDirectoryCollection.php',
    'PHPUnit\\TextUI\\Configuration\\FilterDirectoryCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/FilterDirectoryCollectionIterator.php',
    'PHPUnit\\TextUI\\Configuration\\FilterNotConfiguredException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/FilterNotConfiguredException.php',
    'PHPUnit\\TextUI\\Configuration\\Group' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/Group.php',
    'PHPUnit\\TextUI\\Configuration\\GroupCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/GroupCollection.php',
    'PHPUnit\\TextUI\\Configuration\\GroupCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/GroupCollectionIterator.php',
    'PHPUnit\\TextUI\\Configuration\\IniSetting' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/IniSetting.php',
    'PHPUnit\\TextUI\\Configuration\\IniSettingCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/IniSettingCollection.php',
    'PHPUnit\\TextUI\\Configuration\\IniSettingCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/IniSettingCollectionIterator.php',
    'PHPUnit\\TextUI\\Configuration\\LoggingNotConfiguredException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/LoggingNotConfiguredException.php',
    'PHPUnit\\TextUI\\Configuration\\Merger' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Merger.php',
    'PHPUnit\\TextUI\\Configuration\\NoBaselineException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/NoBaselineException.php',
    'PHPUnit\\TextUI\\Configuration\\NoBootstrapException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/NoBootstrapException.php',
    'PHPUnit\\TextUI\\Configuration\\NoCacheDirectoryException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/NoCacheDirectoryException.php',
    'PHPUnit\\TextUI\\Configuration\\NoCliArgumentException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/NoCliArgumentException.php',
    'PHPUnit\\TextUI\\Configuration\\NoConfigurationFileException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/NoConfigurationFileException.php',
    'PHPUnit\\TextUI\\Configuration\\NoCoverageCacheDirectoryException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/NoCoverageCacheDirectoryException.php',
    'PHPUnit\\TextUI\\Configuration\\NoCustomCssFileException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/NoCustomCssFileException.php',
    'PHPUnit\\TextUI\\Configuration\\NoDefaultTestSuiteException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/NoDefaultTestSuiteException.php',
    'PHPUnit\\TextUI\\Configuration\\NoPharExtensionDirectoryException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/NoPharExtensionDirectoryException.php',
    'PHPUnit\\TextUI\\Configuration\\Php' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/Php.php',
    'PHPUnit\\TextUI\\Configuration\\PhpHandler' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/PhpHandler.php',
    'PHPUnit\\TextUI\\Configuration\\Registry' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Registry.php',
    'PHPUnit\\TextUI\\Configuration\\Source' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/Source.php',
    'PHPUnit\\TextUI\\Configuration\\SourceFilter' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/SourceFilter.php',
    'PHPUnit\\TextUI\\Configuration\\SourceMapper' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/SourceMapper.php',
    'PHPUnit\\TextUI\\Configuration\\TestDirectory' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/TestDirectory.php',
    'PHPUnit\\TextUI\\Configuration\\TestDirectoryCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/TestDirectoryCollection.php',
    'PHPUnit\\TextUI\\Configuration\\TestDirectoryCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/TestDirectoryCollectionIterator.php',
    'PHPUnit\\TextUI\\Configuration\\TestFile' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/TestFile.php',
    'PHPUnit\\TextUI\\Configuration\\TestFileCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/TestFileCollection.php',
    'PHPUnit\\TextUI\\Configuration\\TestFileCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/TestFileCollectionIterator.php',
    'PHPUnit\\TextUI\\Configuration\\TestSuite' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/TestSuite.php',
    'PHPUnit\\TextUI\\Configuration\\TestSuiteBuilder' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/TestSuiteBuilder.php',
    'PHPUnit\\TextUI\\Configuration\\TestSuiteCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/TestSuiteCollection.php',
    'PHPUnit\\TextUI\\Configuration\\TestSuiteCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/TestSuiteCollectionIterator.php',
    'PHPUnit\\TextUI\\Configuration\\Variable' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/Variable.php',
    'PHPUnit\\TextUI\\Configuration\\VariableCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/VariableCollection.php',
    'PHPUnit\\TextUI\\Configuration\\VariableCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Value/VariableCollectionIterator.php',
    'PHPUnit\\TextUI\\Exception' => $vendorDir . '/phpunit/phpunit/src/TextUI/Exception/Exception.php',
    'PHPUnit\\TextUI\\Help' => $vendorDir . '/phpunit/phpunit/src/TextUI/Help.php',
    'PHPUnit\\TextUI\\InvalidSocketException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Exception/InvalidSocketException.php',
    'PHPUnit\\TextUI\\Output\\DefaultPrinter' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Printer/DefaultPrinter.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\BeforeTestClassMethodErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/BeforeTestClassMethodErroredSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\ProgressPrinter' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/ProgressPrinter.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\Subscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/Subscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestConsideredRiskySubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestConsideredRiskySubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestErroredSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestErroredSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestFailedSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestFailedSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestFinishedSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestFinishedSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestMarkedIncompleteSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestMarkedIncompleteSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestPreparedSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestPreparedSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestRunnerExecutionStartedSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestRunnerExecutionStartedSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestSkippedSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestSkippedSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestTriggeredDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestTriggeredDeprecationSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestTriggeredErrorSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestTriggeredErrorSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestTriggeredNoticeSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestTriggeredNoticeSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestTriggeredPhpDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestTriggeredPhpDeprecationSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestTriggeredPhpNoticeSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestTriggeredPhpNoticeSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestTriggeredPhpWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestTriggeredPhpWarningSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestTriggeredPhpunitDeprecationSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestTriggeredPhpunitDeprecationSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestTriggeredPhpunitWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestTriggeredPhpunitWarningSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ProgressPrinter\\TestTriggeredWarningSubscriber' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ProgressPrinter/Subscriber/TestTriggeredWarningSubscriber.php',
    'PHPUnit\\TextUI\\Output\\Default\\ResultPrinter' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/ResultPrinter.php',
    'PHPUnit\\TextUI\\Output\\Default\\UnexpectedOutputPrinter' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Default/UnexpectedOutputPrinter.php',
    'PHPUnit\\TextUI\\Output\\Facade' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Facade.php',
    'PHPUnit\\TextUI\\Output\\NullPrinter' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Printer/NullPrinter.php',
    'PHPUnit\\TextUI\\Output\\Printer' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/Printer/Printer.php',
    'PHPUnit\\TextUI\\Output\\SummaryPrinter' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/SummaryPrinter.php',
    'PHPUnit\\TextUI\\Output\\TestDox\\ResultPrinter' => $vendorDir . '/phpunit/phpunit/src/TextUI/Output/TestDox/ResultPrinter.php',
    'PHPUnit\\TextUI\\RuntimeException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Exception/RuntimeException.php',
    'PHPUnit\\TextUI\\ShellExitCodeCalculator' => $vendorDir . '/phpunit/phpunit/src/TextUI/ShellExitCodeCalculator.php',
    'PHPUnit\\TextUI\\TestDirectoryNotFoundException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Exception/TestDirectoryNotFoundException.php',
    'PHPUnit\\TextUI\\TestFileNotFoundException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Exception/TestFileNotFoundException.php',
    'PHPUnit\\TextUI\\TestRunner' => $vendorDir . '/phpunit/phpunit/src/TextUI/TestRunner.php',
    'PHPUnit\\TextUI\\TestSuiteFilterProcessor' => $vendorDir . '/phpunit/phpunit/src/TextUI/TestSuiteFilterProcessor.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CannotFindSchemaException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Exception/CannotFindSchemaException.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\CodeCoverage' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/CodeCoverage/CodeCoverage.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Report\\Clover' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/CodeCoverage/Report/Clover.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Report\\Cobertura' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/CodeCoverage/Report/Cobertura.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Report\\Crap4j' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/CodeCoverage/Report/Crap4j.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Report\\Html' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/CodeCoverage/Report/Html.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Report\\Php' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/CodeCoverage/Report/Php.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Report\\Text' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/CodeCoverage/Report/Text.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Report\\Xml' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/CodeCoverage/Report/Xml.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Configuration' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Configuration.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\ConvertLogTypes' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/ConvertLogTypes.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CoverageCloverToReport' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/CoverageCloverToReport.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CoverageCrap4jToReport' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/CoverageCrap4jToReport.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CoverageHtmlToReport' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/CoverageHtmlToReport.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CoveragePhpToReport' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/CoveragePhpToReport.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CoverageTextToReport' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/CoverageTextToReport.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CoverageXmlToReport' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/CoverageXmlToReport.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\DefaultConfiguration' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/DefaultConfiguration.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Exception' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Exception.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\FailedSchemaDetectionResult' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/SchemaDetector/FailedSchemaDetectionResult.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Generator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Generator.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Groups' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Groups.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\IntroduceCacheDirectoryAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/IntroduceCacheDirectoryAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\IntroduceCoverageElement' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/IntroduceCoverageElement.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\LoadedFromFileConfiguration' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/LoadedFromFileConfiguration.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Loader' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Loader.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\LogToReportMigration' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/LogToReportMigration.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Logging\\Junit' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Logging/Junit.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Logging\\Logging' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Logging/Logging.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Logging\\TeamCity' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Logging/TeamCity.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Logging\\TestDox\\Html' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Logging/TestDox/Html.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Logging\\TestDox\\Text' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Logging/TestDox/Text.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Migration' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/Migration.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\MigrationBuilder' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/MigrationBuilder.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\MigrationException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/MigrationException.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Migrator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrator.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\MoveAttributesFromFilterWhitelistToCoverage' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/MoveAttributesFromFilterWhitelistToCoverage.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\MoveAttributesFromRootToCoverage' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/MoveAttributesFromRootToCoverage.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\MoveCoverageDirectoriesToSource' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/MoveCoverageDirectoriesToSource.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\MoveWhitelistExcludesToCoverage' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/MoveWhitelistExcludesToCoverage.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\MoveWhitelistIncludesToCoverage' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/MoveWhitelistIncludesToCoverage.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\PHPUnit' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/PHPUnit.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveBeStrictAboutResourceUsageDuringSmallTestsAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveBeStrictAboutResourceUsageDuringSmallTestsAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveBeStrictAboutTodoAnnotatedTestsAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveBeStrictAboutTodoAnnotatedTestsAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveCacheResultFileAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveCacheResultFileAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveCacheTokensAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveCacheTokensAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveConversionToExceptionsAttributes' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveConversionToExceptionsAttributes.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveCoverageElementCacheDirectoryAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveCoverageElementCacheDirectoryAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveCoverageElementProcessUncoveredFilesAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveCoverageElementProcessUncoveredFilesAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveEmptyFilter' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveEmptyFilter.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveListeners' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveListeners.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveLogTypes' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveLogTypes.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveLoggingElements' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveLoggingElements.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveNoInteractionAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveNoInteractionAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemovePrinterAttributes' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemovePrinterAttributes.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveTestDoxGroupsElement' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveTestDoxGroupsElement.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveTestSuiteLoaderAttributes' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveTestSuiteLoaderAttributes.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveVerboseAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RemoveVerboseAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RenameBackupStaticAttributesAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RenameBackupStaticAttributesAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RenameBeStrictAboutCoversAnnotationAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RenameBeStrictAboutCoversAnnotationAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RenameForceCoversAnnotationAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/RenameForceCoversAnnotationAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\SchemaDetectionResult' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/SchemaDetector/SchemaDetectionResult.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\SchemaDetector' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/SchemaDetector/SchemaDetector.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\SchemaFinder' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/SchemaFinder.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\SnapshotNodeList' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/SnapshotNodeList.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\SuccessfulSchemaDetectionResult' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/SchemaDetector/SuccessfulSchemaDetectionResult.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\TestSuiteMapper' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/TestSuiteMapper.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\UpdateSchemaLocation' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Migration/Migrations/UpdateSchemaLocation.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\ValidationResult' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Validator/ValidationResult.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Validator' => $vendorDir . '/phpunit/phpunit/src/TextUI/Configuration/Xml/Validator/Validator.php',
    'PHPUnit\\Util\\Cloner' => $vendorDir . '/phpunit/phpunit/src/Util/Cloner.php',
    'PHPUnit\\Util\\Color' => $vendorDir . '/phpunit/phpunit/src/Util/Color.php',
    'PHPUnit\\Util\\Exception' => $vendorDir . '/phpunit/phpunit/src/Util/Exception/Exception.php',
    'PHPUnit\\Util\\ExcludeList' => $vendorDir . '/phpunit/phpunit/src/Util/ExcludeList.php',
    'PHPUnit\\Util\\Exporter' => $vendorDir . '/phpunit/phpunit/src/Util/Exporter.php',
    'PHPUnit\\Util\\Filesystem' => $vendorDir . '/phpunit/phpunit/src/Util/Filesystem.php',
    'PHPUnit\\Util\\Filter' => $vendorDir . '/phpunit/phpunit/src/Util/Filter.php',
    'PHPUnit\\Util\\GlobalState' => $vendorDir . '/phpunit/phpunit/src/Util/GlobalState.php',
    'PHPUnit\\Util\\Http\\Downloader' => $vendorDir . '/phpunit/phpunit/src/Util/Http/Downloader.php',
    'PHPUnit\\Util\\Http\\PhpDownloader' => $vendorDir . '/phpunit/phpunit/src/Util/Http/PhpDownloader.php',
    'PHPUnit\\Util\\InvalidDirectoryException' => $vendorDir . '/phpunit/phpunit/src/Util/Exception/InvalidDirectoryException.php',
    'PHPUnit\\Util\\InvalidJsonException' => $vendorDir . '/phpunit/phpunit/src/Util/Exception/InvalidJsonException.php',
    'PHPUnit\\Util\\InvalidVersionOperatorException' => $vendorDir . '/phpunit/phpunit/src/Util/Exception/InvalidVersionOperatorException.php',
    'PHPUnit\\Util\\Json' => $vendorDir . '/phpunit/phpunit/src/Util/Json.php',
    'PHPUnit\\Util\\PHP\\AbstractPhpProcess' => $vendorDir . '/phpunit/phpunit/src/Util/PHP/AbstractPhpProcess.php',
    'PHPUnit\\Util\\PHP\\DefaultPhpProcess' => $vendorDir . '/phpunit/phpunit/src/Util/PHP/DefaultPhpProcess.php',
    'PHPUnit\\Util\\PHP\\PhpProcessException' => $vendorDir . '/phpunit/phpunit/src/Util/Exception/PhpProcessException.php',
    'PHPUnit\\Util\\Reflection' => $vendorDir . '/phpunit/phpunit/src/Util/Reflection.php',
    'PHPUnit\\Util\\Test' => $vendorDir . '/phpunit/phpunit/src/Util/Test.php',
    'PHPUnit\\Util\\ThrowableToStringMapper' => $vendorDir . '/phpunit/phpunit/src/Util/ThrowableToStringMapper.php',
    'PHPUnit\\Util\\VersionComparisonOperator' => $vendorDir . '/phpunit/phpunit/src/Util/VersionComparisonOperator.php',
    'PHPUnit\\Util\\Xml' => $vendorDir . '/phpunit/phpunit/src/Util/Xml/Xml.php',
    'PHPUnit\\Util\\Xml\\Loader' => $vendorDir . '/phpunit/phpunit/src/Util/Xml/Loader.php',
    'PHPUnit\\Util\\Xml\\XmlException' => $vendorDir . '/phpunit/phpunit/src/Util/Exception/XmlException.php',
    'PharIo\\Manifest\\Application' => $vendorDir . '/phar-io/manifest/src/values/Application.php',
    'PharIo\\Manifest\\ApplicationName' => $vendorDir . '/phar-io/manifest/src/values/ApplicationName.php',
    'PharIo\\Manifest\\Author' => $vendorDir . '/phar-io/manifest/src/values/Author.php',
    'PharIo\\Manifest\\AuthorCollection' => $vendorDir . '/phar-io/manifest/src/values/AuthorCollection.php',
    'PharIo\\Manifest\\AuthorCollectionIterator' => $vendorDir . '/phar-io/manifest/src/values/AuthorCollectionIterator.php',
    'PharIo\\Manifest\\AuthorElement' => $vendorDir . '/phar-io/manifest/src/xml/AuthorElement.php',
    'PharIo\\Manifest\\AuthorElementCollection' => $vendorDir . '/phar-io/manifest/src/xml/AuthorElementCollection.php',
    'PharIo\\Manifest\\BundledComponent' => $vendorDir . '/phar-io/manifest/src/values/BundledComponent.php',
    'PharIo\\Manifest\\BundledComponentCollection' => $vendorDir . '/phar-io/manifest/src/values/BundledComponentCollection.php',
    'PharIo\\Manifest\\BundledComponentCollectionIterator' => $vendorDir . '/phar-io/manifest/src/values/BundledComponentCollectionIterator.php',
    'PharIo\\Manifest\\BundlesElement' => $vendorDir . '/phar-io/manifest/src/xml/BundlesElement.php',
    'PharIo\\Manifest\\ComponentElement' => $vendorDir . '/phar-io/manifest/src/xml/ComponentElement.php',
    'PharIo\\Manifest\\ComponentElementCollection' => $vendorDir . '/phar-io/manifest/src/xml/ComponentElementCollection.php',
    'PharIo\\Manifest\\ContainsElement' => $vendorDir . '/phar-io/manifest/src/xml/ContainsElement.php',
    'PharIo\\Manifest\\CopyrightElement' => $vendorDir . '/phar-io/manifest/src/xml/CopyrightElement.php',
    'PharIo\\Manifest\\CopyrightInformation' => $vendorDir . '/phar-io/manifest/src/values/CopyrightInformation.php',
    'PharIo\\Manifest\\ElementCollection' => $vendorDir . '/phar-io/manifest/src/xml/ElementCollection.php',
    'PharIo\\Manifest\\ElementCollectionException' => $vendorDir . '/phar-io/manifest/src/exceptions/ElementCollectionException.php',
    'PharIo\\Manifest\\Email' => $vendorDir . '/phar-io/manifest/src/values/Email.php',
    'PharIo\\Manifest\\Exception' => $vendorDir . '/phar-io/manifest/src/exceptions/Exception.php',
    'PharIo\\Manifest\\ExtElement' => $vendorDir . '/phar-io/manifest/src/xml/ExtElement.php',
    'PharIo\\Manifest\\ExtElementCollection' => $vendorDir . '/phar-io/manifest/src/xml/ExtElementCollection.php',
    'PharIo\\Manifest\\Extension' => $vendorDir . '/phar-io/manifest/src/values/Extension.php',
    'PharIo\\Manifest\\ExtensionElement' => $vendorDir . '/phar-io/manifest/src/xml/ExtensionElement.php',
    'PharIo\\Manifest\\InvalidApplicationNameException' => $vendorDir . '/phar-io/manifest/src/exceptions/InvalidApplicationNameException.php',
    'PharIo\\Manifest\\InvalidEmailException' => $vendorDir . '/phar-io/manifest/src/exceptions/InvalidEmailException.php',
    'PharIo\\Manifest\\InvalidUrlException' => $vendorDir . '/phar-io/manifest/src/exceptions/InvalidUrlException.php',
    'PharIo\\Manifest\\Library' => $vendorDir . '/phar-io/manifest/src/values/Library.php',
    'PharIo\\Manifest\\License' => $vendorDir . '/phar-io/manifest/src/values/License.php',
    'PharIo\\Manifest\\LicenseElement' => $vendorDir . '/phar-io/manifest/src/xml/LicenseElement.php',
    'PharIo\\Manifest\\Manifest' => $vendorDir . '/phar-io/manifest/src/values/Manifest.php',
    'PharIo\\Manifest\\ManifestDocument' => $vendorDir . '/phar-io/manifest/src/xml/ManifestDocument.php',
    'PharIo\\Manifest\\ManifestDocumentException' => $vendorDir . '/phar-io/manifest/src/exceptions/ManifestDocumentException.php',
    'PharIo\\Manifest\\ManifestDocumentLoadingException' => $vendorDir . '/phar-io/manifest/src/exceptions/ManifestDocumentLoadingException.php',
    'PharIo\\Manifest\\ManifestDocumentMapper' => $vendorDir . '/phar-io/manifest/src/ManifestDocumentMapper.php',
    'PharIo\\Manifest\\ManifestDocumentMapperException' => $vendorDir . '/phar-io/manifest/src/exceptions/ManifestDocumentMapperException.php',
    'PharIo\\Manifest\\ManifestElement' => $vendorDir . '/phar-io/manifest/src/xml/ManifestElement.php',
    'PharIo\\Manifest\\ManifestElementException' => $vendorDir . '/phar-io/manifest/src/exceptions/ManifestElementException.php',
    'PharIo\\Manifest\\ManifestLoader' => $vendorDir . '/phar-io/manifest/src/ManifestLoader.php',
    'PharIo\\Manifest\\ManifestLoaderException' => $vendorDir . '/phar-io/manifest/src/exceptions/ManifestLoaderException.php',
    'PharIo\\Manifest\\ManifestSerializer' => $vendorDir . '/phar-io/manifest/src/ManifestSerializer.php',
    'PharIo\\Manifest\\NoEmailAddressException' => $vendorDir . '/phar-io/manifest/src/exceptions/NoEmailAddressException.php',
    'PharIo\\Manifest\\PhpElement' => $vendorDir . '/phar-io/manifest/src/xml/PhpElement.php',
    'PharIo\\Manifest\\PhpExtensionRequirement' => $vendorDir . '/phar-io/manifest/src/values/PhpExtensionRequirement.php',
    'PharIo\\Manifest\\PhpVersionRequirement' => $vendorDir . '/phar-io/manifest/src/values/PhpVersionRequirement.php',
    'PharIo\\Manifest\\Requirement' => $vendorDir . '/phar-io/manifest/src/values/Requirement.php',
    'PharIo\\Manifest\\RequirementCollection' => $vendorDir . '/phar-io/manifest/src/values/RequirementCollection.php',
    'PharIo\\Manifest\\RequirementCollectionIterator' => $vendorDir . '/phar-io/manifest/src/values/RequirementCollectionIterator.php',
    'PharIo\\Manifest\\RequiresElement' => $vendorDir . '/phar-io/manifest/src/xml/RequiresElement.php',
    'PharIo\\Manifest\\Type' => $vendorDir . '/phar-io/manifest/src/values/Type.php',
    'PharIo\\Manifest\\Url' => $vendorDir . '/phar-io/manifest/src/values/Url.php',
    'PharIo\\Version\\AbstractVersionConstraint' => $vendorDir . '/phar-io/version/src/constraints/AbstractVersionConstraint.php',
    'PharIo\\Version\\AndVersionConstraintGroup' => $vendorDir . '/phar-io/version/src/constraints/AndVersionConstraintGroup.php',
    'PharIo\\Version\\AnyVersionConstraint' => $vendorDir . '/phar-io/version/src/constraints/AnyVersionConstraint.php',
    'PharIo\\Version\\BuildMetaData' => $vendorDir . '/phar-io/version/src/BuildMetaData.php',
    'PharIo\\Version\\ExactVersionConstraint' => $vendorDir . '/phar-io/version/src/constraints/ExactVersionConstraint.php',
    'PharIo\\Version\\Exception' => $vendorDir . '/phar-io/version/src/exceptions/Exception.php',
    'PharIo\\Version\\GreaterThanOrEqualToVersionConstraint' => $vendorDir . '/phar-io/version/src/constraints/GreaterThanOrEqualToVersionConstraint.php',
    'PharIo\\Version\\InvalidPreReleaseSuffixException' => $vendorDir . '/phar-io/version/src/exceptions/InvalidPreReleaseSuffixException.php',
    'PharIo\\Version\\InvalidVersionException' => $vendorDir . '/phar-io/version/src/exceptions/InvalidVersionException.php',
    'PharIo\\Version\\NoBuildMetaDataException' => $vendorDir . '/phar-io/version/src/exceptions/NoBuildMetaDataException.php',
    'PharIo\\Version\\NoPreReleaseSuffixException' => $vendorDir . '/phar-io/version/src/exceptions/NoPreReleaseSuffixException.php',
    'PharIo\\Version\\OrVersionConstraintGroup' => $vendorDir . '/phar-io/version/src/constraints/OrVersionConstraintGroup.php',
    'PharIo\\Version\\PreReleaseSuffix' => $vendorDir . '/phar-io/version/src/PreReleaseSuffix.php',
    'PharIo\\Version\\SpecificMajorAndMinorVersionConstraint' => $vendorDir . '/phar-io/version/src/constraints/SpecificMajorAndMinorVersionConstraint.php',
    'PharIo\\Version\\SpecificMajorVersionConstraint' => $vendorDir . '/phar-io/version/src/constraints/SpecificMajorVersionConstraint.php',
    'PharIo\\Version\\UnsupportedVersionConstraintException' => $vendorDir . '/phar-io/version/src/exceptions/UnsupportedVersionConstraintException.php',
    'PharIo\\Version\\Version' => $vendorDir . '/phar-io/version/src/Version.php',
    'PharIo\\Version\\VersionConstraint' => $vendorDir . '/phar-io/version/src/constraints/VersionConstraint.php',
    'PharIo\\Version\\VersionConstraintParser' => $vendorDir . '/phar-io/version/src/VersionConstraintParser.php',
    'PharIo\\Version\\VersionConstraintValue' => $vendorDir . '/phar-io/version/src/VersionConstraintValue.php',
    'PharIo\\Version\\VersionNumber' => $vendorDir . '/phar-io/version/src/VersionNumber.php',
    'PhpParser\\Builder' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder.php',
    'PhpParser\\BuilderFactory' => $vendorDir . '/nikic/php-parser/lib/PhpParser/BuilderFactory.php',
    'PhpParser\\BuilderHelpers' => $vendorDir . '/nikic/php-parser/lib/PhpParser/BuilderHelpers.php',
    'PhpParser\\Builder\\ClassConst' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/ClassConst.php',
    'PhpParser\\Builder\\Class_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Class_.php',
    'PhpParser\\Builder\\Declaration' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Declaration.php',
    'PhpParser\\Builder\\EnumCase' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/EnumCase.php',
    'PhpParser\\Builder\\Enum_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Enum_.php',
    'PhpParser\\Builder\\FunctionLike' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/FunctionLike.php',
    'PhpParser\\Builder\\Function_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Function_.php',
    'PhpParser\\Builder\\Interface_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Interface_.php',
    'PhpParser\\Builder\\Method' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Method.php',
    'PhpParser\\Builder\\Namespace_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Namespace_.php',
    'PhpParser\\Builder\\Param' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Param.php',
    'PhpParser\\Builder\\Property' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Property.php',
    'PhpParser\\Builder\\TraitUse' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/TraitUse.php',
    'PhpParser\\Builder\\TraitUseAdaptation' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/TraitUseAdaptation.php',
    'PhpParser\\Builder\\Trait_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Trait_.php',
    'PhpParser\\Builder\\Use_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Use_.php',
    'PhpParser\\Comment' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Comment.php',
    'PhpParser\\Comment\\Doc' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Comment/Doc.php',
    'PhpParser\\ConstExprEvaluationException' => $vendorDir . '/nikic/php-parser/lib/PhpParser/ConstExprEvaluationException.php',
    'PhpParser\\ConstExprEvaluator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/ConstExprEvaluator.php',
    'PhpParser\\Error' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Error.php',
    'PhpParser\\ErrorHandler' => $vendorDir . '/nikic/php-parser/lib/PhpParser/ErrorHandler.php',
    'PhpParser\\ErrorHandler\\Collecting' => $vendorDir . '/nikic/php-parser/lib/PhpParser/ErrorHandler/Collecting.php',
    'PhpParser\\ErrorHandler\\Throwing' => $vendorDir . '/nikic/php-parser/lib/PhpParser/ErrorHandler/Throwing.php',
    'PhpParser\\Internal\\DiffElem' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Internal/DiffElem.php',
    'PhpParser\\Internal\\Differ' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Internal/Differ.php',
    'PhpParser\\Internal\\PrintableNewAnonClassNode' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Internal/PrintableNewAnonClassNode.php',
    'PhpParser\\Internal\\TokenPolyfill' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Internal/TokenPolyfill.php',
    'PhpParser\\Internal\\TokenStream' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Internal/TokenStream.php',
    'PhpParser\\JsonDecoder' => $vendorDir . '/nikic/php-parser/lib/PhpParser/JsonDecoder.php',
    'PhpParser\\Lexer' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer.php',
    'PhpParser\\Lexer\\Emulative' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/Emulative.php',
    'PhpParser\\Lexer\\TokenEmulator\\AsymmetricVisibilityTokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/AsymmetricVisibilityTokenEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\AttributeEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/AttributeEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\EnumTokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/EnumTokenEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\ExplicitOctalEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/ExplicitOctalEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\KeywordEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/KeywordEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\MatchTokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/MatchTokenEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\NullsafeTokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/NullsafeTokenEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\PropertyTokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/PropertyTokenEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\ReadonlyFunctionTokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/ReadonlyFunctionTokenEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\ReadonlyTokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/ReadonlyTokenEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\ReverseEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/ReverseEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\TokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/TokenEmulator.php',
    'PhpParser\\Modifiers' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Modifiers.php',
    'PhpParser\\NameContext' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NameContext.php',
    'PhpParser\\Node' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node.php',
    'PhpParser\\NodeAbstract' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeAbstract.php',
    'PhpParser\\NodeDumper' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeDumper.php',
    'PhpParser\\NodeFinder' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeFinder.php',
    'PhpParser\\NodeTraverser' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeTraverser.php',
    'PhpParser\\NodeTraverserInterface' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeTraverserInterface.php',
    'PhpParser\\NodeVisitor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor.php',
    'PhpParser\\NodeVisitorAbstract' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitorAbstract.php',
    'PhpParser\\NodeVisitor\\CloningVisitor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor/CloningVisitor.php',
    'PhpParser\\NodeVisitor\\CommentAnnotatingVisitor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor/CommentAnnotatingVisitor.php',
    'PhpParser\\NodeVisitor\\FindingVisitor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor/FindingVisitor.php',
    'PhpParser\\NodeVisitor\\FirstFindingVisitor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor/FirstFindingVisitor.php',
    'PhpParser\\NodeVisitor\\NameResolver' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor/NameResolver.php',
    'PhpParser\\NodeVisitor\\NodeConnectingVisitor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor/NodeConnectingVisitor.php',
    'PhpParser\\NodeVisitor\\ParentConnectingVisitor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor/ParentConnectingVisitor.php',
    'PhpParser\\Node\\Arg' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Arg.php',
    'PhpParser\\Node\\ArrayItem' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/ArrayItem.php',
    'PhpParser\\Node\\Attribute' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Attribute.php',
    'PhpParser\\Node\\AttributeGroup' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/AttributeGroup.php',
    'PhpParser\\Node\\ClosureUse' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/ClosureUse.php',
    'PhpParser\\Node\\ComplexType' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/ComplexType.php',
    'PhpParser\\Node\\Const_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Const_.php',
    'PhpParser\\Node\\DeclareItem' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/DeclareItem.php',
    'PhpParser\\Node\\Expr' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr.php',
    'PhpParser\\Node\\Expr\\ArrayDimFetch' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ArrayDimFetch.php',
    'PhpParser\\Node\\Expr\\ArrayItem' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ArrayItem.php',
    'PhpParser\\Node\\Expr\\Array_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Array_.php',
    'PhpParser\\Node\\Expr\\ArrowFunction' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ArrowFunction.php',
    'PhpParser\\Node\\Expr\\Assign' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Assign.php',
    'PhpParser\\Node\\Expr\\AssignOp' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp.php',
    'PhpParser\\Node\\Expr\\AssignOp\\BitwiseAnd' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/BitwiseAnd.php',
    'PhpParser\\Node\\Expr\\AssignOp\\BitwiseOr' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/BitwiseOr.php',
    'PhpParser\\Node\\Expr\\AssignOp\\BitwiseXor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/BitwiseXor.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Coalesce' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Coalesce.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Concat' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Concat.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Div' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Div.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Minus' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Minus.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Mod' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Mod.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Mul' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Mul.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Plus' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Plus.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Pow' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Pow.php',
    'PhpParser\\Node\\Expr\\AssignOp\\ShiftLeft' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/ShiftLeft.php',
    'PhpParser\\Node\\Expr\\AssignOp\\ShiftRight' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/ShiftRight.php',
    'PhpParser\\Node\\Expr\\AssignRef' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignRef.php',
    'PhpParser\\Node\\Expr\\BinaryOp' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\BitwiseAnd' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/BitwiseAnd.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\BitwiseOr' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/BitwiseOr.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\BitwiseXor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/BitwiseXor.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\BooleanAnd' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/BooleanAnd.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\BooleanOr' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/BooleanOr.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Coalesce' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Coalesce.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Concat' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Concat.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Div' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Div.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Equal' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Equal.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Greater' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Greater.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\GreaterOrEqual' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/GreaterOrEqual.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Identical' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Identical.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\LogicalAnd' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/LogicalAnd.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\LogicalOr' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/LogicalOr.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\LogicalXor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/LogicalXor.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Minus' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Minus.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Mod' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Mod.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Mul' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Mul.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\NotEqual' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/NotEqual.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\NotIdentical' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/NotIdentical.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Plus' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Plus.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Pow' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Pow.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\ShiftLeft' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/ShiftLeft.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\ShiftRight' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/ShiftRight.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Smaller' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Smaller.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\SmallerOrEqual' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/SmallerOrEqual.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Spaceship' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Spaceship.php',
    'PhpParser\\Node\\Expr\\BitwiseNot' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BitwiseNot.php',
    'PhpParser\\Node\\Expr\\BooleanNot' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BooleanNot.php',
    'PhpParser\\Node\\Expr\\CallLike' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/CallLike.php',
    'PhpParser\\Node\\Expr\\Cast' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast.php',
    'PhpParser\\Node\\Expr\\Cast\\Array_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast/Array_.php',
    'PhpParser\\Node\\Expr\\Cast\\Bool_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast/Bool_.php',
    'PhpParser\\Node\\Expr\\Cast\\Double' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast/Double.php',
    'PhpParser\\Node\\Expr\\Cast\\Int_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast/Int_.php',
    'PhpParser\\Node\\Expr\\Cast\\Object_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast/Object_.php',
    'PhpParser\\Node\\Expr\\Cast\\String_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast/String_.php',
    'PhpParser\\Node\\Expr\\Cast\\Unset_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast/Unset_.php',
    'PhpParser\\Node\\Expr\\ClassConstFetch' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ClassConstFetch.php',
    'PhpParser\\Node\\Expr\\Clone_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Clone_.php',
    'PhpParser\\Node\\Expr\\Closure' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Closure.php',
    'PhpParser\\Node\\Expr\\ClosureUse' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ClosureUse.php',
    'PhpParser\\Node\\Expr\\ConstFetch' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ConstFetch.php',
    'PhpParser\\Node\\Expr\\Empty_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Empty_.php',
    'PhpParser\\Node\\Expr\\Error' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Error.php',
    'PhpParser\\Node\\Expr\\ErrorSuppress' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ErrorSuppress.php',
    'PhpParser\\Node\\Expr\\Eval_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Eval_.php',
    'PhpParser\\Node\\Expr\\Exit_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Exit_.php',
    'PhpParser\\Node\\Expr\\FuncCall' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/FuncCall.php',
    'PhpParser\\Node\\Expr\\Include_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Include_.php',
    'PhpParser\\Node\\Expr\\Instanceof_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Instanceof_.php',
    'PhpParser\\Node\\Expr\\Isset_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Isset_.php',
    'PhpParser\\Node\\Expr\\List_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/List_.php',
    'PhpParser\\Node\\Expr\\Match_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Match_.php',
    'PhpParser\\Node\\Expr\\MethodCall' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/MethodCall.php',
    'PhpParser\\Node\\Expr\\New_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/New_.php',
    'PhpParser\\Node\\Expr\\NullsafeMethodCall' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/NullsafeMethodCall.php',
    'PhpParser\\Node\\Expr\\NullsafePropertyFetch' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/NullsafePropertyFetch.php',
    'PhpParser\\Node\\Expr\\PostDec' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/PostDec.php',
    'PhpParser\\Node\\Expr\\PostInc' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/PostInc.php',
    'PhpParser\\Node\\Expr\\PreDec' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/PreDec.php',
    'PhpParser\\Node\\Expr\\PreInc' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/PreInc.php',
    'PhpParser\\Node\\Expr\\Print_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Print_.php',
    'PhpParser\\Node\\Expr\\PropertyFetch' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/PropertyFetch.php',
    'PhpParser\\Node\\Expr\\ShellExec' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ShellExec.php',
    'PhpParser\\Node\\Expr\\StaticCall' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/StaticCall.php',
    'PhpParser\\Node\\Expr\\StaticPropertyFetch' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/StaticPropertyFetch.php',
    'PhpParser\\Node\\Expr\\Ternary' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Ternary.php',
    'PhpParser\\Node\\Expr\\Throw_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Throw_.php',
    'PhpParser\\Node\\Expr\\UnaryMinus' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/UnaryMinus.php',
    'PhpParser\\Node\\Expr\\UnaryPlus' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/UnaryPlus.php',
    'PhpParser\\Node\\Expr\\Variable' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Variable.php',
    'PhpParser\\Node\\Expr\\YieldFrom' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/YieldFrom.php',
    'PhpParser\\Node\\Expr\\Yield_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Yield_.php',
    'PhpParser\\Node\\FunctionLike' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/FunctionLike.php',
    'PhpParser\\Node\\Identifier' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Identifier.php',
    'PhpParser\\Node\\InterpolatedStringPart' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/InterpolatedStringPart.php',
    'PhpParser\\Node\\IntersectionType' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/IntersectionType.php',
    'PhpParser\\Node\\MatchArm' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/MatchArm.php',
    'PhpParser\\Node\\Name' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Name.php',
    'PhpParser\\Node\\Name\\FullyQualified' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Name/FullyQualified.php',
    'PhpParser\\Node\\Name\\Relative' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Name/Relative.php',
    'PhpParser\\Node\\NullableType' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/NullableType.php',
    'PhpParser\\Node\\Param' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Param.php',
    'PhpParser\\Node\\PropertyHook' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/PropertyHook.php',
    'PhpParser\\Node\\PropertyItem' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/PropertyItem.php',
    'PhpParser\\Node\\Scalar' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar.php',
    'PhpParser\\Node\\Scalar\\DNumber' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/DNumber.php',
    'PhpParser\\Node\\Scalar\\Encapsed' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/Encapsed.php',
    'PhpParser\\Node\\Scalar\\EncapsedStringPart' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/EncapsedStringPart.php',
    'PhpParser\\Node\\Scalar\\Float_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/Float_.php',
    'PhpParser\\Node\\Scalar\\Int_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/Int_.php',
    'PhpParser\\Node\\Scalar\\InterpolatedString' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/InterpolatedString.php',
    'PhpParser\\Node\\Scalar\\LNumber' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/LNumber.php',
    'PhpParser\\Node\\Scalar\\MagicConst' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Class_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Class_.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Dir' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Dir.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\File' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/File.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Function_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Function_.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Line' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Line.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Method' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Method.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Namespace_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Namespace_.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Property' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Property.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Trait_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Trait_.php',
    'PhpParser\\Node\\Scalar\\String_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/String_.php',
    'PhpParser\\Node\\StaticVar' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/StaticVar.php',
    'PhpParser\\Node\\Stmt' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt.php',
    'PhpParser\\Node\\Stmt\\Block' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Block.php',
    'PhpParser\\Node\\Stmt\\Break_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Break_.php',
    'PhpParser\\Node\\Stmt\\Case_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Case_.php',
    'PhpParser\\Node\\Stmt\\Catch_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Catch_.php',
    'PhpParser\\Node\\Stmt\\ClassConst' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/ClassConst.php',
    'PhpParser\\Node\\Stmt\\ClassLike' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/ClassLike.php',
    'PhpParser\\Node\\Stmt\\ClassMethod' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/ClassMethod.php',
    'PhpParser\\Node\\Stmt\\Class_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Class_.php',
    'PhpParser\\Node\\Stmt\\Const_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Const_.php',
    'PhpParser\\Node\\Stmt\\Continue_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Continue_.php',
    'PhpParser\\Node\\Stmt\\DeclareDeclare' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/DeclareDeclare.php',
    'PhpParser\\Node\\Stmt\\Declare_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Declare_.php',
    'PhpParser\\Node\\Stmt\\Do_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Do_.php',
    'PhpParser\\Node\\Stmt\\Echo_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Echo_.php',
    'PhpParser\\Node\\Stmt\\ElseIf_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/ElseIf_.php',
    'PhpParser\\Node\\Stmt\\Else_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Else_.php',
    'PhpParser\\Node\\Stmt\\EnumCase' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/EnumCase.php',
    'PhpParser\\Node\\Stmt\\Enum_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Enum_.php',
    'PhpParser\\Node\\Stmt\\Expression' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Expression.php',
    'PhpParser\\Node\\Stmt\\Finally_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Finally_.php',
    'PhpParser\\Node\\Stmt\\For_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/For_.php',
    'PhpParser\\Node\\Stmt\\Foreach_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Foreach_.php',
    'PhpParser\\Node\\Stmt\\Function_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Function_.php',
    'PhpParser\\Node\\Stmt\\Global_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Global_.php',
    'PhpParser\\Node\\Stmt\\Goto_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Goto_.php',
    'PhpParser\\Node\\Stmt\\GroupUse' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/GroupUse.php',
    'PhpParser\\Node\\Stmt\\HaltCompiler' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/HaltCompiler.php',
    'PhpParser\\Node\\Stmt\\If_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/If_.php',
    'PhpParser\\Node\\Stmt\\InlineHTML' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/InlineHTML.php',
    'PhpParser\\Node\\Stmt\\Interface_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Interface_.php',
    'PhpParser\\Node\\Stmt\\Label' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Label.php',
    'PhpParser\\Node\\Stmt\\Namespace_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Namespace_.php',
    'PhpParser\\Node\\Stmt\\Nop' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Nop.php',
    'PhpParser\\Node\\Stmt\\Property' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Property.php',
    'PhpParser\\Node\\Stmt\\PropertyProperty' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/PropertyProperty.php',
    'PhpParser\\Node\\Stmt\\Return_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Return_.php',
    'PhpParser\\Node\\Stmt\\StaticVar' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/StaticVar.php',
    'PhpParser\\Node\\Stmt\\Static_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Static_.php',
    'PhpParser\\Node\\Stmt\\Switch_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Switch_.php',
    'PhpParser\\Node\\Stmt\\TraitUse' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/TraitUse.php',
    'PhpParser\\Node\\Stmt\\TraitUseAdaptation' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/TraitUseAdaptation.php',
    'PhpParser\\Node\\Stmt\\TraitUseAdaptation\\Alias' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/TraitUseAdaptation/Alias.php',
    'PhpParser\\Node\\Stmt\\TraitUseAdaptation\\Precedence' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/TraitUseAdaptation/Precedence.php',
    'PhpParser\\Node\\Stmt\\Trait_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Trait_.php',
    'PhpParser\\Node\\Stmt\\TryCatch' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/TryCatch.php',
    'PhpParser\\Node\\Stmt\\Unset_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Unset_.php',
    'PhpParser\\Node\\Stmt\\UseUse' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/UseUse.php',
    'PhpParser\\Node\\Stmt\\Use_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Use_.php',
    'PhpParser\\Node\\Stmt\\While_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/While_.php',
    'PhpParser\\Node\\UnionType' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/UnionType.php',
    'PhpParser\\Node\\UseItem' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/UseItem.php',
    'PhpParser\\Node\\VarLikeIdentifier' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/VarLikeIdentifier.php',
    'PhpParser\\Node\\VariadicPlaceholder' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/VariadicPlaceholder.php',
    'PhpParser\\Parser' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Parser.php',
    'PhpParser\\ParserAbstract' => $vendorDir . '/nikic/php-parser/lib/PhpParser/ParserAbstract.php',
    'PhpParser\\ParserFactory' => $vendorDir . '/nikic/php-parser/lib/PhpParser/ParserFactory.php',
    'PhpParser\\Parser\\Php7' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Parser/Php7.php',
    'PhpParser\\Parser\\Php8' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Parser/Php8.php',
    'PhpParser\\PhpVersion' => $vendorDir . '/nikic/php-parser/lib/PhpParser/PhpVersion.php',
    'PhpParser\\PrettyPrinter' => $vendorDir . '/nikic/php-parser/lib/PhpParser/PrettyPrinter.php',
    'PhpParser\\PrettyPrinterAbstract' => $vendorDir . '/nikic/php-parser/lib/PhpParser/PrettyPrinterAbstract.php',
    'PhpParser\\PrettyPrinter\\Standard' => $vendorDir . '/nikic/php-parser/lib/PhpParser/PrettyPrinter/Standard.php',
    'PhpParser\\Token' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Token.php',
    'Psr\\Container\\ContainerExceptionInterface' => $vendorDir . '/psr/container/src/ContainerExceptionInterface.php',
    'Psr\\Container\\ContainerInterface' => $vendorDir . '/psr/container/src/ContainerInterface.php',
    'Psr\\Container\\NotFoundExceptionInterface' => $vendorDir . '/psr/container/src/NotFoundExceptionInterface.php',
    'Psr\\Log\\AbstractLogger' => $vendorDir . '/psr/log/src/AbstractLogger.php',
    'Psr\\Log\\InvalidArgumentException' => $vendorDir . '/psr/log/src/InvalidArgumentException.php',
    'Psr\\Log\\LogLevel' => $vendorDir . '/psr/log/src/LogLevel.php',
    'Psr\\Log\\LoggerAwareInterface' => $vendorDir . '/psr/log/src/LoggerAwareInterface.php',
    'Psr\\Log\\LoggerAwareTrait' => $vendorDir . '/psr/log/src/LoggerAwareTrait.php',
    'Psr\\Log\\LoggerInterface' => $vendorDir . '/psr/log/src/LoggerInterface.php',
    'Psr\\Log\\LoggerTrait' => $vendorDir . '/psr/log/src/LoggerTrait.php',
    'Psr\\Log\\NullLogger' => $vendorDir . '/psr/log/src/NullLogger.php',
    'SebastianBergmann\\CliParser\\AmbiguousOptionException' => $vendorDir . '/sebastian/cli-parser/src/exceptions/AmbiguousOptionException.php',
    'SebastianBergmann\\CliParser\\Exception' => $vendorDir . '/sebastian/cli-parser/src/exceptions/Exception.php',
    'SebastianBergmann\\CliParser\\OptionDoesNotAllowArgumentException' => $vendorDir . '/sebastian/cli-parser/src/exceptions/OptionDoesNotAllowArgumentException.php',
    'SebastianBergmann\\CliParser\\Parser' => $vendorDir . '/sebastian/cli-parser/src/Parser.php',
    'SebastianBergmann\\CliParser\\RequiredOptionArgumentMissingException' => $vendorDir . '/sebastian/cli-parser/src/exceptions/RequiredOptionArgumentMissingException.php',
    'SebastianBergmann\\CliParser\\UnknownOptionException' => $vendorDir . '/sebastian/cli-parser/src/exceptions/UnknownOptionException.php',
    'SebastianBergmann\\CodeCoverage\\BranchAndPathCoverageNotSupportedException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/BranchAndPathCoverageNotSupportedException.php',
    'SebastianBergmann\\CodeCoverage\\CodeCoverage' => $vendorDir . '/phpunit/php-code-coverage/src/CodeCoverage.php',
    'SebastianBergmann\\CodeCoverage\\Data\\ProcessedCodeCoverageData' => $vendorDir . '/phpunit/php-code-coverage/src/Data/ProcessedCodeCoverageData.php',
    'SebastianBergmann\\CodeCoverage\\Data\\RawCodeCoverageData' => $vendorDir . '/phpunit/php-code-coverage/src/Data/RawCodeCoverageData.php',
    'SebastianBergmann\\CodeCoverage\\DeadCodeDetectionNotSupportedException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/DeadCodeDetectionNotSupportedException.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\Driver' => $vendorDir . '/phpunit/php-code-coverage/src/Driver/Driver.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\PathExistsButIsNotDirectoryException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/PathExistsButIsNotDirectoryException.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\PcovDriver' => $vendorDir . '/phpunit/php-code-coverage/src/Driver/PcovDriver.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\PcovNotAvailableException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/PcovNotAvailableException.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\Selector' => $vendorDir . '/phpunit/php-code-coverage/src/Driver/Selector.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\WriteOperationFailedException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/WriteOperationFailedException.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\XdebugDriver' => $vendorDir . '/phpunit/php-code-coverage/src/Driver/XdebugDriver.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\XdebugNotAvailableException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/XdebugNotAvailableException.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\XdebugNotEnabledException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/XdebugNotEnabledException.php',
    'SebastianBergmann\\CodeCoverage\\Exception' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/Exception.php',
    'SebastianBergmann\\CodeCoverage\\FileCouldNotBeWrittenException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/FileCouldNotBeWrittenException.php',
    'SebastianBergmann\\CodeCoverage\\Filter' => $vendorDir . '/phpunit/php-code-coverage/src/Filter.php',
    'SebastianBergmann\\CodeCoverage\\InvalidArgumentException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/InvalidArgumentException.php',
    'SebastianBergmann\\CodeCoverage\\NoCodeCoverageDriverAvailableException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/NoCodeCoverageDriverAvailableException.php',
    'SebastianBergmann\\CodeCoverage\\NoCodeCoverageDriverWithPathCoverageSupportAvailableException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/NoCodeCoverageDriverWithPathCoverageSupportAvailableException.php',
    'SebastianBergmann\\CodeCoverage\\Node\\AbstractNode' => $vendorDir . '/phpunit/php-code-coverage/src/Node/AbstractNode.php',
    'SebastianBergmann\\CodeCoverage\\Node\\Builder' => $vendorDir . '/phpunit/php-code-coverage/src/Node/Builder.php',
    'SebastianBergmann\\CodeCoverage\\Node\\CrapIndex' => $vendorDir . '/phpunit/php-code-coverage/src/Node/CrapIndex.php',
    'SebastianBergmann\\CodeCoverage\\Node\\Directory' => $vendorDir . '/phpunit/php-code-coverage/src/Node/Directory.php',
    'SebastianBergmann\\CodeCoverage\\Node\\File' => $vendorDir . '/phpunit/php-code-coverage/src/Node/File.php',
    'SebastianBergmann\\CodeCoverage\\Node\\Iterator' => $vendorDir . '/phpunit/php-code-coverage/src/Node/Iterator.php',
    'SebastianBergmann\\CodeCoverage\\ParserException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/ParserException.php',
    'SebastianBergmann\\CodeCoverage\\ReflectionException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/ReflectionException.php',
    'SebastianBergmann\\CodeCoverage\\ReportAlreadyFinalizedException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/ReportAlreadyFinalizedException.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Clover' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Clover.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Cobertura' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Cobertura.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Crap4j' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Crap4j.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Html\\Colors' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Html/Colors.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Html\\CustomCssFile' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Html/CustomCssFile.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Html\\Dashboard' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Html/Renderer/Dashboard.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Html\\Directory' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Html/Renderer/Directory.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Html\\Facade' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Html/Facade.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Html\\File' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Html/Renderer/File.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Html\\Renderer' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Html/Renderer.php',
    'SebastianBergmann\\CodeCoverage\\Report\\PHP' => $vendorDir . '/phpunit/php-code-coverage/src/Report/PHP.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Text' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Text.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Thresholds' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Thresholds.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\BuildInformation' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/BuildInformation.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Coverage' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Coverage.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Directory' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Directory.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Facade' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Facade.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\File' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/File.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Method' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Method.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Node' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Node.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Project' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Project.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Report' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Report.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Source' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Source.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Tests' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Tests.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Totals' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Totals.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Unit' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Unit.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysisCacheNotConfiguredException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/StaticAnalysisCacheNotConfiguredException.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysis\\CacheWarmer' => $vendorDir . '/phpunit/php-code-coverage/src/StaticAnalysis/CacheWarmer.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysis\\CachingFileAnalyser' => $vendorDir . '/phpunit/php-code-coverage/src/StaticAnalysis/CachingFileAnalyser.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysis\\CodeUnitFindingVisitor' => $vendorDir . '/phpunit/php-code-coverage/src/StaticAnalysis/CodeUnitFindingVisitor.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysis\\ExecutableLinesFindingVisitor' => $vendorDir . '/phpunit/php-code-coverage/src/StaticAnalysis/ExecutableLinesFindingVisitor.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysis\\FileAnalyser' => $vendorDir . '/phpunit/php-code-coverage/src/StaticAnalysis/FileAnalyser.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysis\\IgnoredLinesFindingVisitor' => $vendorDir . '/phpunit/php-code-coverage/src/StaticAnalysis/IgnoredLinesFindingVisitor.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysis\\ParsingFileAnalyser' => $vendorDir . '/phpunit/php-code-coverage/src/StaticAnalysis/ParsingFileAnalyser.php',
    'SebastianBergmann\\CodeCoverage\\TestIdMissingException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/TestIdMissingException.php',
    'SebastianBergmann\\CodeCoverage\\Test\\TestSize\\Known' => $vendorDir . '/phpunit/php-code-coverage/src/TestSize/Known.php',
    'SebastianBergmann\\CodeCoverage\\Test\\TestSize\\Large' => $vendorDir . '/phpunit/php-code-coverage/src/TestSize/Large.php',
    'SebastianBergmann\\CodeCoverage\\Test\\TestSize\\Medium' => $vendorDir . '/phpunit/php-code-coverage/src/TestSize/Medium.php',
    'SebastianBergmann\\CodeCoverage\\Test\\TestSize\\Small' => $vendorDir . '/phpunit/php-code-coverage/src/TestSize/Small.php',
    'SebastianBergmann\\CodeCoverage\\Test\\TestSize\\TestSize' => $vendorDir . '/phpunit/php-code-coverage/src/TestSize/TestSize.php',
    'SebastianBergmann\\CodeCoverage\\Test\\TestSize\\Unknown' => $vendorDir . '/phpunit/php-code-coverage/src/TestSize/Unknown.php',
    'SebastianBergmann\\CodeCoverage\\Test\\TestStatus\\Failure' => $vendorDir . '/phpunit/php-code-coverage/src/TestStatus/Failure.php',
    'SebastianBergmann\\CodeCoverage\\Test\\TestStatus\\Known' => $vendorDir . '/phpunit/php-code-coverage/src/TestStatus/Known.php',
    'SebastianBergmann\\CodeCoverage\\Test\\TestStatus\\Success' => $vendorDir . '/phpunit/php-code-coverage/src/TestStatus/Success.php',
    'SebastianBergmann\\CodeCoverage\\Test\\TestStatus\\TestStatus' => $vendorDir . '/phpunit/php-code-coverage/src/TestStatus/TestStatus.php',
    'SebastianBergmann\\CodeCoverage\\Test\\TestStatus\\Unknown' => $vendorDir . '/phpunit/php-code-coverage/src/TestStatus/Unknown.php',
    'SebastianBergmann\\CodeCoverage\\UnintentionallyCoveredCodeException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/UnintentionallyCoveredCodeException.php',
    'SebastianBergmann\\CodeCoverage\\Util\\DirectoryCouldNotBeCreatedException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/DirectoryCouldNotBeCreatedException.php',
    'SebastianBergmann\\CodeCoverage\\Util\\Filesystem' => $vendorDir . '/phpunit/php-code-coverage/src/Util/Filesystem.php',
    'SebastianBergmann\\CodeCoverage\\Util\\Percentage' => $vendorDir . '/phpunit/php-code-coverage/src/Util/Percentage.php',
    'SebastianBergmann\\CodeCoverage\\Version' => $vendorDir . '/phpunit/php-code-coverage/src/Version.php',
    'SebastianBergmann\\CodeCoverage\\XmlException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/XmlException.php',
    'SebastianBergmann\\CodeUnitReverseLookup\\Wizard' => $vendorDir . '/sebastian/code-unit-reverse-lookup/src/Wizard.php',
    'SebastianBergmann\\CodeUnit\\ClassMethodUnit' => $vendorDir . '/sebastian/code-unit/src/ClassMethodUnit.php',
    'SebastianBergmann\\CodeUnit\\ClassUnit' => $vendorDir . '/sebastian/code-unit/src/ClassUnit.php',
    'SebastianBergmann\\CodeUnit\\CodeUnit' => $vendorDir . '/sebastian/code-unit/src/CodeUnit.php',
    'SebastianBergmann\\CodeUnit\\CodeUnitCollection' => $vendorDir . '/sebastian/code-unit/src/CodeUnitCollection.php',
    'SebastianBergmann\\CodeUnit\\CodeUnitCollectionIterator' => $vendorDir . '/sebastian/code-unit/src/CodeUnitCollectionIterator.php',
    'SebastianBergmann\\CodeUnit\\Exception' => $vendorDir . '/sebastian/code-unit/src/exceptions/Exception.php',
    'SebastianBergmann\\CodeUnit\\FileUnit' => $vendorDir . '/sebastian/code-unit/src/FileUnit.php',
    'SebastianBergmann\\CodeUnit\\FunctionUnit' => $vendorDir . '/sebastian/code-unit/src/FunctionUnit.php',
    'SebastianBergmann\\CodeUnit\\InterfaceMethodUnit' => $vendorDir . '/sebastian/code-unit/src/InterfaceMethodUnit.php',
    'SebastianBergmann\\CodeUnit\\InterfaceUnit' => $vendorDir . '/sebastian/code-unit/src/InterfaceUnit.php',
    'SebastianBergmann\\CodeUnit\\InvalidCodeUnitException' => $vendorDir . '/sebastian/code-unit/src/exceptions/InvalidCodeUnitException.php',
    'SebastianBergmann\\CodeUnit\\Mapper' => $vendorDir . '/sebastian/code-unit/src/Mapper.php',
    'SebastianBergmann\\CodeUnit\\NoTraitException' => $vendorDir . '/sebastian/code-unit/src/exceptions/NoTraitException.php',
    'SebastianBergmann\\CodeUnit\\ReflectionException' => $vendorDir . '/sebastian/code-unit/src/exceptions/ReflectionException.php',
    'SebastianBergmann\\CodeUnit\\TraitMethodUnit' => $vendorDir . '/sebastian/code-unit/src/TraitMethodUnit.php',
    'SebastianBergmann\\CodeUnit\\TraitUnit' => $vendorDir . '/sebastian/code-unit/src/TraitUnit.php',
    'SebastianBergmann\\Comparator\\ArrayComparator' => $vendorDir . '/sebastian/comparator/src/ArrayComparator.php',
    'SebastianBergmann\\Comparator\\Comparator' => $vendorDir . '/sebastian/comparator/src/Comparator.php',
    'SebastianBergmann\\Comparator\\ComparisonFailure' => $vendorDir . '/sebastian/comparator/src/ComparisonFailure.php',
    'SebastianBergmann\\Comparator\\DOMNodeComparator' => $vendorDir . '/sebastian/comparator/src/DOMNodeComparator.php',
    'SebastianBergmann\\Comparator\\DateTimeComparator' => $vendorDir . '/sebastian/comparator/src/DateTimeComparator.php',
    'SebastianBergmann\\Comparator\\Exception' => $vendorDir . '/sebastian/comparator/src/exceptions/Exception.php',
    'SebastianBergmann\\Comparator\\ExceptionComparator' => $vendorDir . '/sebastian/comparator/src/ExceptionComparator.php',
    'SebastianBergmann\\Comparator\\Factory' => $vendorDir . '/sebastian/comparator/src/Factory.php',
    'SebastianBergmann\\Comparator\\MockObjectComparator' => $vendorDir . '/sebastian/comparator/src/MockObjectComparator.php',
    'SebastianBergmann\\Comparator\\NumericComparator' => $vendorDir . '/sebastian/comparator/src/NumericComparator.php',
    'SebastianBergmann\\Comparator\\ObjectComparator' => $vendorDir . '/sebastian/comparator/src/ObjectComparator.php',
    'SebastianBergmann\\Comparator\\ResourceComparator' => $vendorDir . '/sebastian/comparator/src/ResourceComparator.php',
    'SebastianBergmann\\Comparator\\RuntimeException' => $vendorDir . '/sebastian/comparator/src/exceptions/RuntimeException.php',
    'SebastianBergmann\\Comparator\\ScalarComparator' => $vendorDir . '/sebastian/comparator/src/ScalarComparator.php',
    'SebastianBergmann\\Comparator\\SplObjectStorageComparator' => $vendorDir . '/sebastian/comparator/src/SplObjectStorageComparator.php',
    'SebastianBergmann\\Comparator\\TypeComparator' => $vendorDir . '/sebastian/comparator/src/TypeComparator.php',
    'SebastianBergmann\\Complexity\\Calculator' => $vendorDir . '/sebastian/complexity/src/Calculator.php',
    'SebastianBergmann\\Complexity\\Complexity' => $vendorDir . '/sebastian/complexity/src/Complexity/Complexity.php',
    'SebastianBergmann\\Complexity\\ComplexityCalculatingVisitor' => $vendorDir . '/sebastian/complexity/src/Visitor/ComplexityCalculatingVisitor.php',
    'SebastianBergmann\\Complexity\\ComplexityCollection' => $vendorDir . '/sebastian/complexity/src/Complexity/ComplexityCollection.php',
    'SebastianBergmann\\Complexity\\ComplexityCollectionIterator' => $vendorDir . '/sebastian/complexity/src/Complexity/ComplexityCollectionIterator.php',
    'SebastianBergmann\\Complexity\\CyclomaticComplexityCalculatingVisitor' => $vendorDir . '/sebastian/complexity/src/Visitor/CyclomaticComplexityCalculatingVisitor.php',
    'SebastianBergmann\\Complexity\\Exception' => $vendorDir . '/sebastian/complexity/src/Exception/Exception.php',
    'SebastianBergmann\\Complexity\\RuntimeException' => $vendorDir . '/sebastian/complexity/src/Exception/RuntimeException.php',
    'SebastianBergmann\\Diff\\Chunk' => $vendorDir . '/sebastian/diff/src/Chunk.php',
    'SebastianBergmann\\Diff\\ConfigurationException' => $vendorDir . '/sebastian/diff/src/Exception/ConfigurationException.php',
    'SebastianBergmann\\Diff\\Diff' => $vendorDir . '/sebastian/diff/src/Diff.php',
    'SebastianBergmann\\Diff\\Differ' => $vendorDir . '/sebastian/diff/src/Differ.php',
    'SebastianBergmann\\Diff\\Exception' => $vendorDir . '/sebastian/diff/src/Exception/Exception.php',
    'SebastianBergmann\\Diff\\InvalidArgumentException' => $vendorDir . '/sebastian/diff/src/Exception/InvalidArgumentException.php',
    'SebastianBergmann\\Diff\\Line' => $vendorDir . '/sebastian/diff/src/Line.php',
    'SebastianBergmann\\Diff\\LongestCommonSubsequenceCalculator' => $vendorDir . '/sebastian/diff/src/LongestCommonSubsequenceCalculator.php',
    'SebastianBergmann\\Diff\\MemoryEfficientLongestCommonSubsequenceCalculator' => $vendorDir . '/sebastian/diff/src/MemoryEfficientLongestCommonSubsequenceCalculator.php',
    'SebastianBergmann\\Diff\\Output\\AbstractChunkOutputBuilder' => $vendorDir . '/sebastian/diff/src/Output/AbstractChunkOutputBuilder.php',
    'SebastianBergmann\\Diff\\Output\\DiffOnlyOutputBuilder' => $vendorDir . '/sebastian/diff/src/Output/DiffOnlyOutputBuilder.php',
    'SebastianBergmann\\Diff\\Output\\DiffOutputBuilderInterface' => $vendorDir . '/sebastian/diff/src/Output/DiffOutputBuilderInterface.php',
    'SebastianBergmann\\Diff\\Output\\StrictUnifiedDiffOutputBuilder' => $vendorDir . '/sebastian/diff/src/Output/StrictUnifiedDiffOutputBuilder.php',
    'SebastianBergmann\\Diff\\Output\\UnifiedDiffOutputBuilder' => $vendorDir . '/sebastian/diff/src/Output/UnifiedDiffOutputBuilder.php',
    'SebastianBergmann\\Diff\\Parser' => $vendorDir . '/sebastian/diff/src/Parser.php',
    'SebastianBergmann\\Diff\\TimeEfficientLongestCommonSubsequenceCalculator' => $vendorDir . '/sebastian/diff/src/TimeEfficientLongestCommonSubsequenceCalculator.php',
    'SebastianBergmann\\Environment\\Console' => $vendorDir . '/sebastian/environment/src/Console.php',
    'SebastianBergmann\\Environment\\Runtime' => $vendorDir . '/sebastian/environment/src/Runtime.php',
    'SebastianBergmann\\Exporter\\Exporter' => $vendorDir . '/sebastian/exporter/src/Exporter.php',
    'SebastianBergmann\\FileIterator\\ExcludeIterator' => $vendorDir . '/phpunit/php-file-iterator/src/ExcludeIterator.php',
    'SebastianBergmann\\FileIterator\\Facade' => $vendorDir . '/phpunit/php-file-iterator/src/Facade.php',
    'SebastianBergmann\\FileIterator\\Factory' => $vendorDir . '/phpunit/php-file-iterator/src/Factory.php',
    'SebastianBergmann\\FileIterator\\Iterator' => $vendorDir . '/phpunit/php-file-iterator/src/Iterator.php',
    'SebastianBergmann\\GlobalState\\CodeExporter' => $vendorDir . '/sebastian/global-state/src/CodeExporter.php',
    'SebastianBergmann\\GlobalState\\Exception' => $vendorDir . '/sebastian/global-state/src/exceptions/Exception.php',
    'SebastianBergmann\\GlobalState\\ExcludeList' => $vendorDir . '/sebastian/global-state/src/ExcludeList.php',
    'SebastianBergmann\\GlobalState\\Restorer' => $vendorDir . '/sebastian/global-state/src/Restorer.php',
    'SebastianBergmann\\GlobalState\\RuntimeException' => $vendorDir . '/sebastian/global-state/src/exceptions/RuntimeException.php',
    'SebastianBergmann\\GlobalState\\Snapshot' => $vendorDir . '/sebastian/global-state/src/Snapshot.php',
    'SebastianBergmann\\Invoker\\Exception' => $vendorDir . '/phpunit/php-invoker/src/exceptions/Exception.php',
    'SebastianBergmann\\Invoker\\Invoker' => $vendorDir . '/phpunit/php-invoker/src/Invoker.php',
    'SebastianBergmann\\Invoker\\ProcessControlExtensionNotLoadedException' => $vendorDir . '/phpunit/php-invoker/src/exceptions/ProcessControlExtensionNotLoadedException.php',
    'SebastianBergmann\\Invoker\\TimeoutException' => $vendorDir . '/phpunit/php-invoker/src/exceptions/TimeoutException.php',
    'SebastianBergmann\\LinesOfCode\\Counter' => $vendorDir . '/sebastian/lines-of-code/src/Counter.php',
    'SebastianBergmann\\LinesOfCode\\Exception' => $vendorDir . '/sebastian/lines-of-code/src/Exception/Exception.php',
    'SebastianBergmann\\LinesOfCode\\IllogicalValuesException' => $vendorDir . '/sebastian/lines-of-code/src/Exception/IllogicalValuesException.php',
    'SebastianBergmann\\LinesOfCode\\LineCountingVisitor' => $vendorDir . '/sebastian/lines-of-code/src/LineCountingVisitor.php',
    'SebastianBergmann\\LinesOfCode\\LinesOfCode' => $vendorDir . '/sebastian/lines-of-code/src/LinesOfCode.php',
    'SebastianBergmann\\LinesOfCode\\NegativeValueException' => $vendorDir . '/sebastian/lines-of-code/src/Exception/NegativeValueException.php',
    'SebastianBergmann\\LinesOfCode\\RuntimeException' => $vendorDir . '/sebastian/lines-of-code/src/Exception/RuntimeException.php',
    'SebastianBergmann\\ObjectEnumerator\\Enumerator' => $vendorDir . '/sebastian/object-enumerator/src/Enumerator.php',
    'SebastianBergmann\\ObjectReflector\\ObjectReflector' => $vendorDir . '/sebastian/object-reflector/src/ObjectReflector.php',
    'SebastianBergmann\\RecursionContext\\Context' => $vendorDir . '/sebastian/recursion-context/src/Context.php',
    'SebastianBergmann\\Template\\Exception' => $vendorDir . '/phpunit/php-text-template/src/exceptions/Exception.php',
    'SebastianBergmann\\Template\\InvalidArgumentException' => $vendorDir . '/phpunit/php-text-template/src/exceptions/InvalidArgumentException.php',
    'SebastianBergmann\\Template\\RuntimeException' => $vendorDir . '/phpunit/php-text-template/src/exceptions/RuntimeException.php',
    'SebastianBergmann\\Template\\Template' => $vendorDir . '/phpunit/php-text-template/src/Template.php',
    'SebastianBergmann\\Timer\\Duration' => $vendorDir . '/phpunit/php-timer/src/Duration.php',
    'SebastianBergmann\\Timer\\Exception' => $vendorDir . '/phpunit/php-timer/src/exceptions/Exception.php',
    'SebastianBergmann\\Timer\\NoActiveTimerException' => $vendorDir . '/phpunit/php-timer/src/exceptions/NoActiveTimerException.php',
    'SebastianBergmann\\Timer\\ResourceUsageFormatter' => $vendorDir . '/phpunit/php-timer/src/ResourceUsageFormatter.php',
    'SebastianBergmann\\Timer\\TimeSinceStartOfRequestNotAvailableException' => $vendorDir . '/phpunit/php-timer/src/exceptions/TimeSinceStartOfRequestNotAvailableException.php',
    'SebastianBergmann\\Timer\\Timer' => $vendorDir . '/phpunit/php-timer/src/Timer.php',
    'SebastianBergmann\\Type\\CallableType' => $vendorDir . '/sebastian/type/src/type/CallableType.php',
    'SebastianBergmann\\Type\\Exception' => $vendorDir . '/sebastian/type/src/exception/Exception.php',
    'SebastianBergmann\\Type\\FalseType' => $vendorDir . '/sebastian/type/src/type/FalseType.php',
    'SebastianBergmann\\Type\\GenericObjectType' => $vendorDir . '/sebastian/type/src/type/GenericObjectType.php',
    'SebastianBergmann\\Type\\IntersectionType' => $vendorDir . '/sebastian/type/src/type/IntersectionType.php',
    'SebastianBergmann\\Type\\IterableType' => $vendorDir . '/sebastian/type/src/type/IterableType.php',
    'SebastianBergmann\\Type\\MixedType' => $vendorDir . '/sebastian/type/src/type/MixedType.php',
    'SebastianBergmann\\Type\\NeverType' => $vendorDir . '/sebastian/type/src/type/NeverType.php',
    'SebastianBergmann\\Type\\NullType' => $vendorDir . '/sebastian/type/src/type/NullType.php',
    'SebastianBergmann\\Type\\ObjectType' => $vendorDir . '/sebastian/type/src/type/ObjectType.php',
    'SebastianBergmann\\Type\\Parameter' => $vendorDir . '/sebastian/type/src/Parameter.php',
    'SebastianBergmann\\Type\\ReflectionMapper' => $vendorDir . '/sebastian/type/src/ReflectionMapper.php',
    'SebastianBergmann\\Type\\RuntimeException' => $vendorDir . '/sebastian/type/src/exception/RuntimeException.php',
    'SebastianBergmann\\Type\\SimpleType' => $vendorDir . '/sebastian/type/src/type/SimpleType.php',
    'SebastianBergmann\\Type\\StaticType' => $vendorDir . '/sebastian/type/src/type/StaticType.php',
    'SebastianBergmann\\Type\\TrueType' => $vendorDir . '/sebastian/type/src/type/TrueType.php',
    'SebastianBergmann\\Type\\Type' => $vendorDir . '/sebastian/type/src/type/Type.php',
    'SebastianBergmann\\Type\\TypeName' => $vendorDir . '/sebastian/type/src/TypeName.php',
    'SebastianBergmann\\Type\\UnionType' => $vendorDir . '/sebastian/type/src/type/UnionType.php',
    'SebastianBergmann\\Type\\UnknownType' => $vendorDir . '/sebastian/type/src/type/UnknownType.php',
    'SebastianBergmann\\Type\\VoidType' => $vendorDir . '/sebastian/type/src/type/VoidType.php',
    'SebastianBergmann\\Version' => $vendorDir . '/sebastian/version/src/Version.php',
    'Tests\\Support\\Database\\Seeds\\ExampleSeeder' => $baseDir . '/tests/_support/Database/Seeds/ExampleSeeder.php',
    'Tests\\Support\\Libraries\\ConfigReader' => $baseDir . '/tests/_support/Libraries/ConfigReader.php',
    'Tests\\Support\\Models\\ExampleModel' => $baseDir . '/tests/_support/Models/ExampleModel.php',
    'TheSeer\\Tokenizer\\Exception' => $vendorDir . '/theseer/tokenizer/src/Exception.php',
    'TheSeer\\Tokenizer\\NamespaceUri' => $vendorDir . '/theseer/tokenizer/src/NamespaceUri.php',
    'TheSeer\\Tokenizer\\NamespaceUriException' => $vendorDir . '/theseer/tokenizer/src/NamespaceUriException.php',
    'TheSeer\\Tokenizer\\Token' => $vendorDir . '/theseer/tokenizer/src/Token.php',
    'TheSeer\\Tokenizer\\TokenCollection' => $vendorDir . '/theseer/tokenizer/src/TokenCollection.php',
    'TheSeer\\Tokenizer\\TokenCollectionException' => $vendorDir . '/theseer/tokenizer/src/TokenCollectionException.php',
    'TheSeer\\Tokenizer\\Tokenizer' => $vendorDir . '/theseer/tokenizer/src/Tokenizer.php',
    'TheSeer\\Tokenizer\\XMLSerializer' => $vendorDir . '/theseer/tokenizer/src/XMLSerializer.php',
    'org\\bovigo\\vfs\\DotDirectory' => $vendorDir . '/mikey179/vfsstream/src/main/php/org/bovigo/vfs/DotDirectory.php',
    'org\\bovigo\\vfs\\Quota' => $vendorDir . '/mikey179/vfsstream/src/main/php/org/bovigo/vfs/Quota.php',
    'org\\bovigo\\vfs\\content\\FileContent' => $vendorDir . '/mikey179/vfsstream/src/main/php/org/bovigo/vfs/content/FileContent.php',
    'org\\bovigo\\vfs\\content\\LargeFileContent' => $vendorDir . '/mikey179/vfsstream/src/main/php/org/bovigo/vfs/content/LargeFileContent.php',
    'org\\bovigo\\vfs\\content\\SeekableFileContent' => $vendorDir . '/mikey179/vfsstream/src/main/php/org/bovigo/vfs/content/SeekableFileContent.php',
    'org\\bovigo\\vfs\\content\\StringBasedFileContent' => $vendorDir . '/mikey179/vfsstream/src/main/php/org/bovigo/vfs/content/StringBasedFileContent.php',
    'org\\bovigo\\vfs\\vfsStream' => $vendorDir . '/mikey179/vfsstream/src/main/php/org/bovigo/vfs/vfsStream.php',
    'org\\bovigo\\vfs\\vfsStreamAbstractContent' => $vendorDir . '/mikey179/vfsstream/src/main/php/org/bovigo/vfs/vfsStreamAbstractContent.php',
    'org\\bovigo\\vfs\\vfsStreamBlock' => $vendorDir . '/mikey179/vfsstream/src/main/php/org/bovigo/vfs/vfsStreamBlock.php',
    'org\\bovigo\\vfs\\vfsStreamContainer' => $vendorDir . '/mikey179/vfsstream/src/main/php/org/bovigo/vfs/vfsStreamContainer.php',
    'org\\bovigo\\vfs\\vfsStreamContainerIterator' => $vendorDir . '/mikey179/vfsstream/src/main/php/org/bovigo/vfs/vfsStreamContainerIterator.php',
    'org\\bovigo\\vfs\\vfsStreamContent' => $vendorDir . '/mikey179/vfsstream/src/main/php/org/bovigo/vfs/vfsStreamContent.php',
    'org\\bovigo\\vfs\\vfsStreamDirectory' => $vendorDir . '/mikey179/vfsstream/src/main/php/org/bovigo/vfs/vfsStreamDirectory.php',
    'org\\bovigo\\vfs\\vfsStreamException' => $vendorDir . '/mikey179/vfsstream/src/main/php/org/bovigo/vfs/vfsStreamException.php',
    'org\\bovigo\\vfs\\vfsStreamFile' => $vendorDir . '/mikey179/vfsstream/src/main/php/org/bovigo/vfs/vfsStreamFile.php',
    'org\\bovigo\\vfs\\vfsStreamWrapper' => $vendorDir . '/mikey179/vfsstream/src/main/php/org/bovigo/vfs/vfsStreamWrapper.php',
    'org\\bovigo\\vfs\\visitor\\vfsStreamAbstractVisitor' => $vendorDir . '/mikey179/vfsstream/src/main/php/org/bovigo/vfs/visitor/vfsStreamAbstractVisitor.php',
    'org\\bovigo\\vfs\\visitor\\vfsStreamPrintVisitor' => $vendorDir . '/mikey179/vfsstream/src/main/php/org/bovigo/vfs/visitor/vfsStreamPrintVisitor.php',
    'org\\bovigo\\vfs\\visitor\\vfsStreamStructureVisitor' => $vendorDir . '/mikey179/vfsstream/src/main/php/org/bovigo/vfs/visitor/vfsStreamStructureVisitor.php',
    'org\\bovigo\\vfs\\visitor\\vfsStreamVisitor' => $vendorDir . '/mikey179/vfsstream/src/main/php/org/bovigo/vfs/visitor/vfsStreamVisitor.php',
);
