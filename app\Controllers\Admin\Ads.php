<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\Ad;

class Ads extends BaseController
{
    protected $adModel;

    public function __construct()
    {
        $this->adModel = new Ad();
    }

    public function index()
    {
        $data = [
            'title' => 'Advertisement Management',
            'ads' => $this->adModel->findAll()
        ];

        return view('admin/ads/index', $data);
    }

    public function create()
    {
        $data = [
            'title' => 'Add New Advertisement'
        ];

        return view('admin/ads/create', $data);
    }

    public function store()
    {
        $rules = [
            'title'    => 'required|min_length[3]|max_length[255]',
            'position' => 'required|in_list[header,sidebar,footer,content_top,content_bottom,between_news]',
            'type'     => 'required|in_list[banner,text,video]',
            'status'   => 'required|in_list[active,inactive]',
        ];

        // Add image validation for banner type
        if ($this->request->getPost('type') === 'banner') {
            $rules['image'] = 'uploaded[image]|is_image[image]|max_size[image,2048]';
        }

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Handle image upload for banner ads
        $imageName = null;
        if ($this->request->getPost('type') === 'banner') {
            $image = $this->request->getFile('image');
            if ($image && $image->isValid() && !$image->hasMoved()) {
                $imageName = $image->getRandomName();
                $image->move(FCPATH . 'uploads/ads/', $imageName);
            }
        }

        $data = [
            'title'       => $this->request->getPost('title'),
            'description' => $this->request->getPost('description'),
            'image'       => $imageName,
            'link_url'    => $this->request->getPost('link_url'),
            'position'    => $this->request->getPost('position'),
            'type'        => $this->request->getPost('type'),
            'status'      => $this->request->getPost('status'),
            'start_date'  => $this->request->getPost('start_date') ?: null,
            'end_date'    => $this->request->getPost('end_date') ?: null,
        ];

        if ($this->adModel->save($data)) {
            return redirect()->to('/admin/ads')->with('success', 'Advertisement created successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create advertisement');
        }
    }

    public function edit($id)
    {
        $ad = $this->adModel->find($id);

        if (!$ad) {
            return redirect()->to('/admin/ads')->with('error', 'Advertisement not found');
        }

        $data = [
            'title' => 'Edit Advertisement',
            'ad' => $ad
        ];

        return view('admin/ads/edit', $data);
    }

    public function update($id)
    {
        $ad = $this->adModel->find($id);

        if (!$ad) {
            return redirect()->to('/admin/ads')->with('error', 'Advertisement not found');
        }

        $rules = [
            'title'    => 'required|min_length[3]|max_length[255]',
            'position' => 'required|in_list[header,sidebar,footer,content_top,content_bottom,between_news]',
            'type'     => 'required|in_list[banner,text,video]',
            'status'   => 'required|in_list[active,inactive]',
        ];

        // Add image validation if new image is uploaded for banner type
        if ($this->request->getPost('type') === 'banner' && $this->request->getFile('image')->isValid()) {
            $rules['image'] = 'is_image[image]|max_size[image,2048]';
        }

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'title'       => $this->request->getPost('title'),
            'description' => $this->request->getPost('description'),
            'link_url'    => $this->request->getPost('link_url'),
            'position'    => $this->request->getPost('position'),
            'type'        => $this->request->getPost('type'),
            'status'      => $this->request->getPost('status'),
            'start_date'  => $this->request->getPost('start_date') ?: null,
            'end_date'    => $this->request->getPost('end_date') ?: null,
        ];

        // Handle image upload if new image is provided for banner ads
        if ($this->request->getPost('type') === 'banner') {
            $image = $this->request->getFile('image');
            if ($image && $image->isValid() && !$image->hasMoved()) {
                // Delete old image if exists
                if ($ad['image'] && file_exists(FCPATH . 'uploads/ads/' . $ad['image'])) {
                    unlink(FCPATH . 'uploads/ads/' . $ad['image']);
                }

                $imageName = $image->getRandomName();
                $image->move(FCPATH . 'uploads/ads/', $imageName);
                $data['image'] = $imageName;
            }
        } else {
            // If type changed from banner to text/video, remove image
            if ($ad['type'] === 'banner' && $ad['image']) {
                if (file_exists(FCPATH . 'uploads/ads/' . $ad['image'])) {
                    unlink(FCPATH . 'uploads/ads/' . $ad['image']);
                }
                $data['image'] = null;
            }
        }

        if ($this->adModel->update($id, $data)) {
            return redirect()->to('/admin/ads')->with('success', 'Advertisement updated successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update advertisement');
        }
    }

    public function delete($id)
    {
        $ad = $this->adModel->find($id);

        if (!$ad) {
            return redirect()->to('/admin/ads')->with('error', 'Advertisement not found');
        }

        // Delete associated image
        if ($ad['image'] && file_exists(FCPATH . 'uploads/ads/' . $ad['image'])) {
            unlink(FCPATH . 'uploads/ads/' . $ad['image']);
        }

        if ($this->adModel->delete($id)) {
            return redirect()->to('/admin/ads')->with('success', 'Advertisement deleted successfully');
        } else {
            return redirect()->to('/admin/ads')->with('error', 'Failed to delete advertisement');
        }
    }

    public function analytics()
    {
        $ads = $this->adModel->findAll();

        // Calculate summary statistics
        $totalAds = count($ads);
        $totalImpressions = array_sum(array_column($ads, 'impressions'));
        $totalClicks = array_sum(array_column($ads, 'clicks'));
        $averageCTR = $totalImpressions > 0 ? round(($totalClicks / $totalImpressions) * 100, 2) : 0;

        // Position statistics
        $positionStats = [];
        foreach ($ads as $ad) {
            $position = $ad['position'];
            if (!isset($positionStats[$position])) {
                $positionStats[$position] = ['impressions' => 0, 'clicks' => 0];
            }
            $positionStats[$position]['impressions'] += $ad['impressions'];
            $positionStats[$position]['clicks'] += $ad['clicks'];
        }

        // Type statistics
        $typeStats = [];
        foreach ($ads as $ad) {
            $type = $ad['type'];
            if (!isset($typeStats[$type])) {
                $typeStats[$type] = 0;
            }
            $typeStats[$type]++;
        }

        // Top performing ads
        usort($ads, function ($a, $b) {
            $ctrA = $a['impressions'] > 0 ? ($a['clicks'] / $a['impressions']) * 100 : 0;
            $ctrB = $b['impressions'] > 0 ? ($b['clicks'] / $b['impressions']) * 100 : 0;
            return $ctrB <=> $ctrA;
        });
        $topAds = array_slice($ads, 0, 10);

        $data = [
            'title' => 'Advertisement Analytics',
            'totalAds' => $totalAds,
            'totalImpressions' => $totalImpressions,
            'totalClicks' => $totalClicks,
            'averageCTR' => $averageCTR,
            'positionStats' => $positionStats,
            'typeStats' => $typeStats,
            'topAds' => $topAds
        ];

        return view('admin/ads/analytics', $data);
    }
}
