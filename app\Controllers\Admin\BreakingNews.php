<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\BreakingNews as BreakingNewsModel;

class BreakingNews extends BaseController
{
    protected $breakingNewsModel;

    public function __construct()
    {
        $this->breakingNewsModel = new BreakingNewsModel();
    }

    public function index()
    {
        $data = [
            'title' => 'Breaking News Management',
            'breakingNews' => $this->breakingNewsModel->getBreakingNewsWithDetails()
        ];

        return view('admin/breaking_news/index', $data);
    }

    public function create()
    {
        $data = [
            'title' => 'Add Breaking News'
        ];

        return view('admin/breaking_news/create', $data);
    }

    public function store()
    {
        $rules = [
            'title'    => 'required|min_length[5]|max_length[500]',
            'priority' => 'required|in_list[1,2,3,4]',
            'status'   => 'required|in_list[active,inactive]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'title'      => $this->request->getPost('title'),
            'content'    => $this->request->getPost('content'),
            'link_url'   => $this->request->getPost('link_url'),
            'priority'   => $this->request->getPost('priority'),
            'status'     => $this->request->getPost('status'),
            'start_time' => $this->request->getPost('start_time') ?: null,
            'end_time'   => $this->request->getPost('end_time') ?: null,
            'created_by' => session()->get('user_id'),
        ];

        if ($this->breakingNewsModel->save($data)) {
            return redirect()->to('/admin/breaking-news')->with('success', 'Breaking news created successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create breaking news');
        }
    }

    public function edit($id)
    {
        $breakingNews = $this->breakingNewsModel->find($id);

        if (!$breakingNews) {
            return redirect()->to('/admin/breaking-news')->with('error', 'Breaking news not found');
        }

        $data = [
            'title' => 'Edit Breaking News',
            'breakingNews' => $breakingNews
        ];

        return view('admin/breaking_news/edit', $data);
    }

    public function update($id)
    {
        $breakingNews = $this->breakingNewsModel->find($id);

        if (!$breakingNews) {
            return redirect()->to('/admin/breaking-news')->with('error', 'Breaking news not found');
        }

        $rules = [
            'title'    => 'required|min_length[5]|max_length[500]',
            'priority' => 'required|in_list[1,2,3,4]',
            'status'   => 'required|in_list[active,inactive]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'title'      => $this->request->getPost('title'),
            'content'    => $this->request->getPost('content'),
            'link_url'   => $this->request->getPost('link_url'),
            'priority'   => $this->request->getPost('priority'),
            'status'     => $this->request->getPost('status'),
            'start_time' => $this->request->getPost('start_time') ?: null,
            'end_time'   => $this->request->getPost('end_time') ?: null,
        ];

        if ($this->breakingNewsModel->update($id, $data)) {
            return redirect()->to('/admin/breaking-news')->with('success', 'Breaking news updated successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update breaking news');
        }
    }

    public function delete($id)
    {
        $breakingNews = $this->breakingNewsModel->find($id);

        if (!$breakingNews) {
            return redirect()->to('/admin/breaking-news')->with('error', 'Breaking news not found');
        }

        if ($this->breakingNewsModel->delete($id)) {
            return redirect()->to('/admin/breaking-news')->with('success', 'Breaking news deleted successfully');
        } else {
            return redirect()->to('/admin/breaking-news')->with('error', 'Failed to delete breaking news');
        }
    }

    public function toggleStatus($id)
    {
        $breakingNews = $this->breakingNewsModel->find($id);

        if (!$breakingNews) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Breaking news not found']);
        }

        $newStatus = $breakingNews['status'] === 'active' ? 'inactive' : 'active';

        if ($this->breakingNewsModel->update($id, ['status' => $newStatus])) {
            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Status updated successfully',
                'new_status' => $newStatus
            ]);
        } else {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Failed to update status']);
        }
    }
}
