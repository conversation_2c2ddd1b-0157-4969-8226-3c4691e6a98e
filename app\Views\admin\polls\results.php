<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-bar me-2"></i>Poll Results</h2>
                <div>
                    <a href="<?= base_url('admin/polls/analytics') ?>" class="btn btn-info me-2">
                        <i class="fas fa-chart-line me-2"></i>Analytics
                    </a>
                    <a href="<?= base_url('admin/polls') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Polls
                    </a>
                </div>
            </div>

            <!-- Poll Information -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-poll me-2"></i><?= esc($poll['title']) ?>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <?php if ($poll['description']): ?>
                                        <p class="text-muted"><?= esc($poll['description']) ?></p>
                                    <?php endif; ?>
                                    
                                    <div class="row">
                                        <div class="col-md-3">
                                            <strong>Status:</strong>
                                            <?php
                                            $statusClass = match($poll['status']) {
                                                'active' => 'bg-success',
                                                'inactive' => 'bg-secondary',
                                                'closed' => 'bg-danger',
                                                default => 'bg-secondary'
                                            };
                                            ?>
                                            <span class="badge <?= $statusClass ?>"><?= ucfirst($poll['status']) ?></span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>Type:</strong>
                                            <span class="badge <?= $poll['multiple_choice'] ? 'bg-info' : 'bg-primary' ?>">
                                                <?= $poll['multiple_choice'] ? 'Multiple Choice' : 'Single Choice' ?>
                                            </span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>Total Votes:</strong>
                                            <span class="badge bg-warning text-dark fs-6">
                                                <?= number_format($poll['total_votes']) ?>
                                            </span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>Created:</strong>
                                            <small><?= date('d M Y, H:i', strtotime($poll['created_at'])) ?></small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-end">
                                        <a href="<?= base_url('admin/polls/edit/' . $poll['id']) ?>" class="btn btn-primary">
                                            <i class="fas fa-edit me-2"></i>Edit Poll
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Results Overview -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-pie me-2"></i>Vote Distribution
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if ($poll['total_votes'] > 0): ?>
                                <?php foreach ($poll['options'] as $option): ?>
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between align-items-center mb-1">
                                            <span class="fw-bold"><?= esc($option['option_text']) ?></span>
                                            <span class="badge bg-primary">
                                                <?= number_format($option['vote_count']) ?> votes (<?= $option['percentage'] ?>%)
                                            </span>
                                        </div>
                                        <div class="progress" style="height: 25px;">
                                            <div class="progress-bar bg-<?= ['primary', 'success', 'info', 'warning', 'danger', 'secondary'][array_search($option, $poll['options']) % 6] ?>" 
                                                 role="progressbar" 
                                                 style="width: <?= $option['percentage'] ?>%"
                                                 aria-valuenow="<?= $option['percentage'] ?>" 
                                                 aria-valuemin="0" 
                                                 aria-valuemax="100">
                                                <?= $option['percentage'] ?>%
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-vote-yea fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No votes yet</h5>
                                    <p class="text-muted">This poll hasn't received any votes yet.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-doughnut me-2"></i>Results Chart
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if ($poll['total_votes'] > 0): ?>
                                <canvas id="resultsChart" width="300" height="300"></canvas>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">Chart will appear when votes are received</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Voting Timeline -->
            <?php if (!empty($votingTimeline)): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-line me-2"></i>Voting Timeline
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="timelineChart" width="400" height="100"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Hourly Voting Pattern -->
            <?php if (!empty($hourlyPattern)): ?>
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-clock me-2"></i>Hourly Voting Pattern
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="hourlyChart" width="400" height="100"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
<?php if ($poll['total_votes'] > 0): ?>
// Results Pie Chart
const resultsCtx = document.getElementById('resultsChart').getContext('2d');
const resultsChart = new Chart(resultsCtx, {
    type: 'doughnut',
    data: {
        labels: <?= json_encode(array_column($poll['options'], 'option_text')) ?>,
        datasets: [{
            data: <?= json_encode(array_column($poll['options'], 'vote_count')) ?>,
            backgroundColor: [
                'rgba(0, 123, 255, 0.8)',
                'rgba(40, 167, 69, 0.8)',
                'rgba(23, 162, 184, 0.8)',
                'rgba(255, 193, 7, 0.8)',
                'rgba(220, 53, 69, 0.8)',
                'rgba(108, 117, 125, 0.8)',
            ],
            borderColor: [
                'rgba(0, 123, 255, 1)',
                'rgba(40, 167, 69, 1)',
                'rgba(23, 162, 184, 1)',
                'rgba(255, 193, 7, 1)',
                'rgba(220, 53, 69, 1)',
                'rgba(108, 117, 125, 1)',
            ],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    boxWidth: 12,
                    font: {
                        size: 11
                    }
                }
            }
        }
    }
});
<?php endif; ?>

<?php if (!empty($votingTimeline)): ?>
// Voting Timeline Chart
const timelineCtx = document.getElementById('timelineChart').getContext('2d');
const timelineChart = new Chart(timelineCtx, {
    type: 'line',
    data: {
        labels: <?= json_encode(array_column($votingTimeline, 'vote_date')) ?>,
        datasets: [{
            label: 'Daily Votes',
            data: <?= json_encode(array_column($votingTimeline, 'vote_count')) ?>,
            borderColor: 'rgba(0, 123, 255, 1)',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
<?php endif; ?>

<?php if (!empty($hourlyPattern)): ?>
// Hourly Pattern Chart
const hourlyCtx = document.getElementById('hourlyChart').getContext('2d');
const hourlyChart = new Chart(hourlyCtx, {
    type: 'bar',
    data: {
        labels: <?= json_encode(array_map(fn($h) => $h['vote_hour'] . ':00', $hourlyPattern)) ?>,
        datasets: [{
            label: 'Votes by Hour',
            data: <?= json_encode(array_column($hourlyPattern, 'vote_count')) ?>,
            backgroundColor: 'rgba(40, 167, 69, 0.8)',
            borderColor: 'rgba(40, 167, 69, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
<?php endif; ?>
</script>
<?= $this->endSection() ?>
