<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-cogs me-2"></i>Settings Management</h2>
            </div>

            <!-- Settings Categories -->
            <div class="row">
                <!-- Profile Management -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 border-primary">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-user-circle fa-3x text-primary"></i>
                            </div>
                            <h5 class="card-title">Profile Management</h5>
                            <p class="card-text">Manage your profile information, upload profile picture, and update personal details.</p>
                            <div class="d-grid gap-2">
                                <a href="<?= base_url('admin/settings/profile') ?>" class="btn btn-primary">
                                    <i class="fas fa-user-edit me-2"></i>Edit Profile
                                </a>
                                <a href="<?= base_url('admin/settings/change-password') ?>" class="btn btn-outline-primary">
                                    <i class="fas fa-key me-2"></i>Change Password
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Site Settings -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 border-success">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-globe fa-3x text-success"></i>
                            </div>
                            <h5 class="card-title">Site Settings</h5>
                            <p class="card-text">Configure site name, logo, favicon, and other general website settings.</p>
                            <div class="d-grid">
                                <a href="<?= base_url('admin/settings/site') ?>" class="btn btn-success">
                                    <i class="fas fa-cog me-2"></i>Site Settings
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 border-info">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-address-book fa-3x text-info"></i>
                            </div>
                            <h5 class="card-title">Contact Information</h5>
                            <p class="card-text">Manage contact details including email, phone, address, and WhatsApp number.</p>
                            <div class="d-grid">
                                <a href="<?= base_url('admin/settings/contact') ?>" class="btn btn-info">
                                    <i class="fas fa-phone me-2"></i>Contact Settings
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Social Media -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 border-warning">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-share-alt fa-3x text-warning"></i>
                            </div>
                            <h5 class="card-title">Social Media</h5>
                            <p class="card-text">Configure social media links for Facebook, Twitter, Instagram, YouTube, and more.</p>
                            <div class="d-grid">
                                <a href="<?= base_url('admin/settings/social') ?>" class="btn btn-warning">
                                    <i class="fab fa-facebook me-2"></i>Social Settings
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Footer Management -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 border-secondary">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-copyright fa-3x text-secondary"></i>
                            </div>
                            <h5 class="card-title">Footer Management</h5>
                            <p class="card-text">Manage footer copyright text, description, and other footer content.</p>
                            <div class="d-grid">
                                <a href="<?= base_url('admin/settings/footer') ?>" class="btn btn-secondary">
                                    <i class="fas fa-edit me-2"></i>Footer Settings
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analytics -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 border-danger">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-chart-line fa-3x text-danger"></i>
                            </div>
                            <h5 class="card-title">Analytics & Tracking</h5>
                            <p class="card-text">Configure Google Analytics, Tag Manager, Facebook Pixel, and other tracking codes.</p>
                            <div class="d-grid">
                                <a href="<?= base_url('admin/settings/analytics') ?>" class="btn btn-danger">
                                    <i class="fas fa-analytics me-2"></i>Analytics Settings
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SEO Settings -->
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 border-dark">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-search fa-3x text-dark"></i>
                            </div>
                            <h5 class="card-title">SEO Settings</h5>
                            <p class="card-text">Configure meta descriptions, keywords, and other SEO-related settings.</p>
                            <div class="d-grid">
                                <a href="<?= base_url('admin/settings/seo') ?>" class="btn btn-dark">
                                    <i class="fas fa-search-plus me-2"></i>SEO Settings
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Settings Overview -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-list me-2"></i>Current Settings Overview
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($settingsGrouped)): ?>
                                <div class="row">
                                    <?php foreach ($settingsGrouped as $category => $settings): ?>
                                        <div class="col-md-6 col-lg-4 mb-3">
                                            <div class="card bg-light">
                                                <div class="card-header">
                                                    <h6 class="mb-0 text-capitalize">
                                                        <i class="fas fa-folder me-2"></i><?= ucfirst($category) ?> Settings
                                                    </h6>
                                                </div>
                                                <div class="card-body p-2">
                                                    <div class="table-responsive">
                                                        <table class="table table-sm table-borderless mb-0">
                                                            <?php foreach (array_slice($settings, 0, 3) as $setting): ?>
                                                                <tr>
                                                                    <td class="small fw-bold"><?= esc(str_replace('_', ' ', $setting['setting_key'])) ?>:</td>
                                                                    <td class="small">
                                                                        <?php if ($setting['setting_type'] === 'image' || $setting['setting_type'] === 'file'): ?>
                                                                            <?= $setting['setting_value'] ? '<i class="fas fa-check text-success"></i> Set' : '<i class="fas fa-times text-danger"></i> Not Set' ?>
                                                                        <?php else: ?>
                                                                            <?= esc(substr($setting['setting_value'], 0, 30)) ?><?= strlen($setting['setting_value']) > 30 ? '...' : '' ?>
                                                                        <?php endif; ?>
                                                                    </td>
                                                                </tr>
                                                            <?php endforeach; ?>
                                                            <?php if (count($settings) > 3): ?>
                                                                <tr>
                                                                    <td colspan="2" class="text-center">
                                                                        <small class="text-muted">... and <?= count($settings) - 3 ?> more</small>
                                                                    </td>
                                                                </tr>
                                                            <?php endif; ?>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-cogs fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No settings configured yet</h5>
                                    <p class="text-muted">Start by configuring your site settings.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card border-primary">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>Quick Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-2">
                                    <a href="<?= base_url('admin/settings/profile') ?>" class="btn btn-outline-primary btn-sm w-100">
                                        <i class="fas fa-user me-2"></i>Update Profile
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="<?= base_url('admin/settings/site') ?>" class="btn btn-outline-success btn-sm w-100">
                                        <i class="fas fa-globe me-2"></i>Site Settings
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="<?= base_url('admin/settings/social') ?>" class="btn btn-outline-warning btn-sm w-100">
                                        <i class="fab fa-facebook me-2"></i>Social Media
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="<?= base_url('admin/settings/analytics') ?>" class="btn btn-outline-danger btn-sm w-100">
                                        <i class="fas fa-chart-line me-2"></i>Analytics
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
