<?= $this->extend('frontend/layout') ?>

<?= $this->section('content') ?>
<!-- Header Ads -->
<?= view('components/ad_display', [
    'ads' => $headerAds,
    'position' => 'header',
    'showLabel' => true
]) ?>



<div class="container-fluid mt-4">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-9">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-3">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>">होम</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('epaper') ?>">ई-पेपर</a></li>
                    <li class="breadcrumb-item active" aria-current="page">आर्काइव</li>
                </ol>
            </nav>

            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-archive me-2"></i>ई-पेपर आर्काइव
                    <?php if ($currentYear): ?>
                        - <?= $currentYear ?>
                        <?php if ($currentMonth): ?>
                            <?= getMonthName($currentMonth) ?>
                        <?php endif; ?>
                    <?php endif; ?>
                </h1>
                <div>
                    <a href="<?= base_url('epaper') ?>" class="btn btn-outline-primary">
                        <i class="fas fa-home me-2"></i>ई-पेपर होम
                    </a>
                </div>
            </div>

            <!-- Archive Navigation -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>आर्काइव नेवीगेशन
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Years -->
                        <div class="col-md-6">
                            <h6><i class="fas fa-calendar me-2"></i>वर्ष चुनें</h6>
                            <div class="d-flex flex-wrap gap-2 mb-3">
                                <?php foreach ($archiveYears as $yearData): ?>
                                    <a href="<?= base_url('epaper/archive/' . $yearData['year']) ?>"
                                        class="btn btn-<?= ($currentYear == $yearData['year']) ? 'primary' : 'outline-secondary' ?> btn-sm">
                                        <?= $yearData['year'] ?>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- Months (if year is selected) -->
                        <?php if ($currentYear && !empty($archiveMonths)): ?>
                            <div class="col-md-6">
                                <h6><i class="fas fa-calendar-week me-2"></i>महीना चुनें</h6>
                                <div class="d-flex flex-wrap gap-2 mb-3">
                                    <a href="<?= base_url('epaper/archive/' . $currentYear) ?>"
                                        class="btn btn-<?= !$currentMonth ? 'primary' : 'outline-secondary' ?> btn-sm">
                                        सभी
                                    </a>
                                    <?php foreach ($archiveMonths as $monthData): ?>
                                        <a href="<?= base_url('epaper/archive/' . $currentYear . '/' . $monthData['month']) ?>"
                                            class="btn btn-<?= ($currentMonth == $monthData['month']) ? 'primary' : 'outline-secondary' ?> btn-sm">
                                            <?= getMonthName($monthData['month']) ?>
                                            <span class="badge bg-light text-dark ms-1"><?= $monthData['count'] ?></span>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Epapers List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-newspaper me-2"></i>
                        <?php if ($currentYear && $currentMonth): ?>
                            <?= getMonthName($currentMonth) ?> <?= $currentYear ?> के ई-पेपर
                        <?php elseif ($currentYear): ?>
                            <?= $currentYear ?> के ई-पेपर
                        <?php else: ?>
                            सभी ई-पेपर
                        <?php endif; ?>
                        <span class="badge bg-primary ms-2"><?= count($epapers) ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($epapers)): ?>
                        <div class="row">
                            <?php foreach ($epapers as $epaper): ?>
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card h-100 shadow-sm">
                                        <div class="card-body">
                                            <!-- Thumbnail -->
                                            <?php if ($epaper['thumbnail']): ?>
                                                <img src="<?= base_url('uploads/epapers/thumbnails/' . $epaper['thumbnail']) ?>"
                                                    alt="<?= esc($epaper['title']) ?>"
                                                    class="img-fluid rounded mb-3">
                                            <?php else: ?>
                                                <div class="bg-light rounded d-flex align-items-center justify-content-center mb-3" style="height: 150px;">
                                                    <div class="text-center">
                                                        <i class="fas fa-file-pdf fa-3x text-muted mb-2"></i>
                                                        <div class="text-muted small">PDF</div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>

                                            <!-- Title -->
                                            <h6 class="card-title"><?= esc($epaper['title']) ?></h6>

                                            <!-- Publication Info -->
                                            <div class="small text-muted mb-3">
                                                <div class="mb-1">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    <strong><?= formatDateHindi($epaper['publication_date']) ?></strong>
                                                </div>
                                                <div class="mb-1">
                                                    <i class="fas fa-bookmark me-1"></i>
                                                    <?= esc($epaper['edition']) ?>
                                                </div>
                                                <div class="mb-1">
                                                    <i class="fas fa-language me-1"></i>
                                                    <?= esc($epaper['language']) ?>
                                                </div>
                                            </div>

                                            <!-- File Info -->
                                            <div class="small text-muted mb-3">
                                                <div class="row">
                                                    <div class="col-6">
                                                        <i class="fas fa-file-alt me-1"></i>
                                                        <?= formatBytes($epaper['file_size']) ?>
                                                    </div>
                                                    <?php if ($epaper['page_count']): ?>
                                                        <div class="col-6">
                                                            <i class="fas fa-file-pdf me-1"></i>
                                                            <?= $epaper['page_count'] ?> पृष्ठ
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>

                                            <!-- Statistics -->
                                            <div class="small text-muted mb-3">
                                                <div class="row">
                                                    <div class="col-6">
                                                        <i class="fas fa-eye me-1 text-primary"></i>
                                                        <?= number_format($epaper['view_count']) ?>
                                                    </div>
                                                    <div class="col-6">
                                                        <i class="fas fa-download me-1 text-success"></i>
                                                        <?= number_format($epaper['download_count']) ?>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Featured Badge -->
                                            <?php if ($epaper['featured']): ?>
                                                <div class="mb-2">
                                                    <span class="badge bg-warning text-dark">
                                                        <i class="fas fa-star me-1"></i>विशेष
                                                    </span>
                                                </div>
                                            <?php endif; ?>
                                        </div>

                                        <!-- Actions -->
                                        <div class="card-footer bg-transparent">
                                            <div class="d-flex gap-2">
                                                <a href="<?= base_url('epaper/view/' . $epaper['id']) ?>"
                                                    class="btn btn-primary btn-sm flex-fill">
                                                    <i class="fas fa-eye me-1"></i>देखें
                                                </a>
                                                <a href="<?= base_url('epaper/download/' . $epaper['id']) ?>"
                                                    class="btn btn-success btn-sm flex-fill">
                                                    <i class="fas fa-download me-1"></i>डाउनलोड
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-archive fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">कोई ई-पेपर नहीं मिला</h5>
                            <p class="text-muted">
                                <?php if ($currentYear && $currentMonth): ?>
                                    <?= getMonthName($currentMonth) ?> <?= $currentYear ?> में कोई ई-पेपर उपलब्ध नहीं है।
                                <?php elseif ($currentYear): ?>
                                    <?= $currentYear ?> में कोई ई-पेपर उपलब्ध नहीं है।
                                <?php else: ?>
                                    इस समय कोई ई-पेपर उपलब्ध नहीं है।
                                <?php endif; ?>
                            </p>
                            <a href="<?= base_url('epaper') ?>" class="btn btn-primary">
                                <i class="fas fa-home me-2"></i>ई-पेपर होम पर जाएं
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-3">
            <!-- Quick Stats -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>आर्काइव आंकड़े
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="row">
                            <div class="col-6 mb-3">
                                <div class="h4 text-primary mb-1"><?= count($archiveYears) ?></div>
                                <small class="text-muted">वर्ष</small>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="h4 text-success mb-1"><?= count($epapers) ?></div>
                                <small class="text-muted">ई-पेपर</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Navigation -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-compass me-2"></i>त्वरित नेवीगेशन
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= base_url('epaper') ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-home me-2"></i>ई-पेपर होम
                        </a>
                        <a href="<?= base_url('epaper/search') ?>" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-search me-2"></i>खोजें
                        </a>
                        <a href="<?= base_url('epaper/archive') ?>" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-archive me-2"></i>पूरा आर्काइव
                        </a>
                    </div>
                </div>
            </div>

            <!-- Sidebar Ads -->
            <?= view('components/ad_display', [
                'ads' => $sidebarAds,
                'position' => 'sidebar',
                'showLabel' => true
            ]) ?>
        </div>
    </div>
</div>

<!-- Footer Ads -->
<?= view('components/ad_display', [
    'ads' => $footerAds,
    'position' => 'footer',
    'showLabel' => true
]) ?>
<?= $this->endSection() ?>