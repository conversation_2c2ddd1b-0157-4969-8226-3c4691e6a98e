<?php

namespace App\Models;

use CodeIgniter\Model;

class Setting extends Model
{
    protected $table            = 'settings';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'setting_key', 'setting_value', 'setting_type', 'category', 'description', 'is_active'
    ];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules = [
        'setting_key'   => 'required|max_length[100]',
        'setting_type'  => 'required|in_list[text,textarea,image,file,json,boolean]',
        'category'      => 'required|max_length[50]',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Get setting value by key
     */
    public function getSetting($key, $default = null)
    {
        $setting = $this->where('setting_key', $key)
                        ->where('is_active', 1)
                        ->first();
        
        return $setting ? $setting['setting_value'] : $default;
    }

    /**
     * Set setting value
     */
    public function setSetting($key, $value, $type = 'text', $category = 'general')
    {
        $existing = $this->where('setting_key', $key)->first();
        
        if ($existing) {
            return $this->update($existing['id'], ['setting_value' => $value]);
        } else {
            return $this->save([
                'setting_key' => $key,
                'setting_value' => $value,
                'setting_type' => $type,
                'category' => $category,
                'is_active' => 1
            ]);
        }
    }

    /**
     * Get settings by category
     */
    public function getSettingsByCategory($category)
    {
        return $this->where('category', $category)
                    ->where('is_active', 1)
                    ->orderBy('setting_key', 'ASC')
                    ->findAll();
    }

    /**
     * Get all settings as key-value pairs
     */
    public function getAllSettings()
    {
        $settings = $this->where('is_active', 1)->findAll();
        $result = [];
        
        foreach ($settings as $setting) {
            $result[$setting['setting_key']] = $setting['setting_value'];
        }
        
        return $result;
    }

    /**
     * Get settings grouped by category
     */
    public function getSettingsGrouped()
    {
        $settings = $this->where('is_active', 1)
                         ->orderBy('category', 'ASC')
                         ->orderBy('setting_key', 'ASC')
                         ->findAll();
        
        $grouped = [];
        foreach ($settings as $setting) {
            $grouped[$setting['category']][] = $setting;
        }
        
        return $grouped;
    }

    /**
     * Update multiple settings
     */
    public function updateSettings($settings)
    {
        $success = true;
        
        foreach ($settings as $key => $value) {
            if (!$this->setSetting($key, $value)) {
                $success = false;
            }
        }
        
        return $success;
    }

    /**
     * Get categories
     */
    public function getCategories()
    {
        return $this->select('category')
                    ->distinct()
                    ->where('is_active', 1)
                    ->orderBy('category', 'ASC')
                    ->findColumn('category');
    }

    /**
     * Get site settings for frontend
     */
    public function getSiteSettings()
    {
        $settings = $this->getAllSettings();
        
        return [
            'site_name' => $settings['site_name'] ?? 'BBC News Portal',
            'site_tagline' => $settings['site_tagline'] ?? 'Latest Hindi News Portal',
            'site_logo' => $settings['site_logo'] ?? '',
            'site_favicon' => $settings['site_favicon'] ?? '',
            'contact_email' => $settings['contact_email'] ?? '',
            'contact_phone' => $settings['contact_phone'] ?? '',
            'contact_address' => $settings['contact_address'] ?? '',
            'contact_whatsapp' => $settings['contact_whatsapp'] ?? '',
            'facebook_url' => $settings['facebook_url'] ?? '',
            'twitter_url' => $settings['twitter_url'] ?? '',
            'instagram_url' => $settings['instagram_url'] ?? '',
            'youtube_url' => $settings['youtube_url'] ?? '',
            'linkedin_url' => $settings['linkedin_url'] ?? '',
            'telegram_url' => $settings['telegram_url'] ?? '',
            'footer_copyright' => $settings['footer_copyright'] ?? '',
            'footer_description' => $settings['footer_description'] ?? '',
            'google_analytics_id' => $settings['google_analytics_id'] ?? '',
            'google_tag_manager_id' => $settings['google_tag_manager_id'] ?? '',
            'facebook_pixel_id' => $settings['facebook_pixel_id'] ?? '',
            'meta_description' => $settings['meta_description'] ?? '',
            'meta_keywords' => $settings['meta_keywords'] ?? '',
        ];
    }

    /**
     * Get social media links
     */
    public function getSocialMediaLinks()
    {
        $settings = $this->getSettingsByCategory('social');
        $links = [];
        
        foreach ($settings as $setting) {
            if (!empty($setting['setting_value'])) {
                $links[$setting['setting_key']] = $setting['setting_value'];
            }
        }
        
        return $links;
    }

    /**
     * Get analytics codes
     */
    public function getAnalyticsCodes()
    {
        return $this->getSettingsByCategory('analytics');
    }

    /**
     * Check if setting exists
     */
    public function settingExists($key)
    {
        return $this->where('setting_key', $key)->countAllResults() > 0;
    }

    /**
     * Delete setting
     */
    public function deleteSetting($key)
    {
        return $this->where('setting_key', $key)->delete();
    }

    /**
     * Toggle setting status
     */
    public function toggleSetting($key)
    {
        $setting = $this->where('setting_key', $key)->first();
        
        if ($setting) {
            return $this->update($setting['id'], [
                'is_active' => $setting['is_active'] ? 0 : 1
            ]);
        }
        
        return false;
    }
}
