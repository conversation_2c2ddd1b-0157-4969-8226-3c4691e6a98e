<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">Tag Management</h4>
    <a href="<?= base_url('admin/tags/create') ?>" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Add New Tag
    </a>
</div>

<div class="card">
    <div class="card-body">
        <?php if (empty($tags)): ?>
            <div class="text-center py-5">
                <i class="fas fa-hashtag fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Tags Found</h5>
                <p class="text-muted">Start by creating your first news tag.</p>
                <a href="<?= base_url('admin/tags/create') ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Create First Tag
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>ID</th>
                            <th>Name (नाम)</th>
                            <th>Slug</th>
                            <th>Status</th>
                            <th>Created At</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($tags as $tag): ?>
                            <tr>
                                <td><?= $tag['id'] ?></td>
                                <td>
                                    <span class="badge bg-info">#<?= esc($tag['name']) ?></span>
                                </td>
                                <td>
                                    <code><?= esc($tag['slug']) ?></code>
                                </td>
                                <td>
                                    <?php if ($tag['status'] === 'active'): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td><?= date('M d, Y', strtotime($tag['created_at'])) ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= base_url('admin/tags/edit/' . $tag['id']) ?>" 
                                           class="btn btn-sm btn-outline-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="<?= base_url('admin/tags/delete/' . $tag['id']) ?>" 
                                           class="btn btn-sm btn-outline-danger" 
                                           onclick="return confirm('Are you sure you want to delete this tag?')" 
                                           title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Tag Statistics -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card border-left-primary">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Tags
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= count($tags) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-hashtag fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card border-left-success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Active Tags
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= count(array_filter($tags, function($t) { return $t['status'] === 'active'; })) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card border-left-warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Inactive Tags
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= count(array_filter($tags, function($t) { return $t['status'] === 'inactive'; })) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-pause-circle fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<style>
    .border-left-primary {
        border-left: 4px solid #667eea !important;
    }
    .border-left-success {
        border-left: 4px solid #28a745 !important;
    }
    .border-left-warning {
        border-left: 4px solid #ffc107 !important;
    }
</style>
<?= $this->endSection() ?>
