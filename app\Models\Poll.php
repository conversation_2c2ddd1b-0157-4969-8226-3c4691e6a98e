<?php

namespace App\Models;

use CodeIgniter\Model;

class Poll extends Model
{
    protected $table            = 'polls';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'title',
        'description',
        'status',
        'multiple_choice',
        'show_results',
        'start_date',
        'end_date',
        'total_votes',
        'created_by'
    ];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules = [
        'title'          => 'required|min_length[5]|max_length[500]',
        'status'         => 'required|in_list[active,inactive,closed]',
        'multiple_choice' => 'required|in_list[0,1]',
        'show_results'   => 'required|in_list[after_vote,always,never]',
        'created_by'     => 'required|integer',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Get polls with creator details
     */
    public function getPollsWithDetails($limit = null)
    {
        $builder = $this->select('polls.*, users.full_name as created_by_name')
            ->join('users', 'users.id = polls.created_by')
            ->orderBy('polls.created_at', 'DESC');

        if ($limit) {
            $builder->limit($limit);
        }

        return $builder->findAll();
    }

    /**
     * Get active polls
     */
    public function getActivePolls($limit = null)
    {
        $builder = $this->select('polls.*, users.full_name as created_by_name')
            ->join('users', 'users.id = polls.created_by')
            ->where('polls.status', 'active')
            ->where('(polls.start_date IS NULL OR polls.start_date <= NOW())', null, false)
            ->where('(polls.end_date IS NULL OR polls.end_date >= NOW())', null, false)
            ->orderBy('polls.created_at', 'DESC');

        if ($limit) {
            $builder->limit($limit);
        }

        return $builder->findAll();
    }

    /**
     * Get poll with options
     */
    public function getPollWithOptions($pollId)
    {
        $poll = $this->select('polls.*, users.full_name as created_by_name')
            ->join('users', 'users.id = polls.created_by')
            ->find($pollId);

        if (!$poll) {
            return null;
        }

        $optionsModel = new \App\Models\PollOption();
        $poll['options'] = $optionsModel->where('poll_id', $pollId)
            ->orderBy('sort_order', 'ASC')
            ->findAll();

        return $poll;
    }

    /**
     * Check if poll is active
     */
    public function isPollActive($pollId)
    {
        $poll = $this->find($pollId);

        if (!$poll || $poll['status'] !== 'active') {
            return false;
        }

        $now = date('Y-m-d H:i:s');

        if ($poll['start_date'] && $poll['start_date'] > $now) {
            return false;
        }

        if ($poll['end_date'] && $poll['end_date'] < $now) {
            return false;
        }

        return true;
    }

    /**
     * Update total votes count
     */
    public function updateTotalVotes($pollId)
    {
        $votesModel = new \App\Models\PollVote();
        $totalVotes = $votesModel->where('poll_id', $pollId)->countAllResults();

        return $this->update($pollId, ['total_votes' => $totalVotes]);
    }
}
