<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\Category;

class Categories extends BaseController
{
    protected $categoryModel;

    public function __construct()
    {
        $this->categoryModel = new Category();
    }

    public function index()
    {
        $data = [
            'title' => 'Category Management',
            'categories' => $this->categoryModel->findAll()
        ];

        return view('admin/categories/index', $data);
    }

    public function create()
    {
        $data = [
            'title' => 'Add New Category'
        ];

        return view('admin/categories/create', $data);
    }

    public function store()
    {
        $rules = [
            'name' => 'required|min_length[2]|max_length[255]',
            'slug' => 'required|min_length[2]|max_length[255]|is_unique[categories.slug]',
            'status' => 'required|in_list[active,inactive]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name'        => $this->request->getPost('name'),
            'slug'        => $this->request->getPost('slug'),
            'description' => $this->request->getPost('description'),
            'status'      => $this->request->getPost('status'),
        ];

        if ($this->categoryModel->save($data)) {
            return redirect()->to('/admin/categories')->with('success', 'Category created successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create category');
        }
    }

    public function edit($id)
    {
        $category = $this->categoryModel->find($id);

        if (!$category) {
            return redirect()->to('/admin/categories')->with('error', 'Category not found');
        }

        $data = [
            'title' => 'Edit Category',
            'category' => $category
        ];

        return view('admin/categories/edit', $data);
    }

    public function update($id)
    {
        $category = $this->categoryModel->find($id);

        if (!$category) {
            return redirect()->to('/admin/categories')->with('error', 'Category not found');
        }

        $rules = [
            'name' => 'required|min_length[2]|max_length[255]',
            'slug' => 'required|min_length[2]|max_length[255]',
            'status' => 'required|in_list[active,inactive]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Check for slug uniqueness manually
        $slug = $this->request->getPost('slug');
        $existingCategory = $this->categoryModel->where('slug', $slug)->where('id !=', $id)->first();
        if ($existingCategory) {
            return redirect()->back()->withInput()->with('error', 'Slug already exists. Please choose a different slug.');
        }

        $data = [
            'name'        => $this->request->getPost('name'),
            'slug'        => $slug,
            'description' => $this->request->getPost('description'),
            'status'      => $this->request->getPost('status'),
        ];

        // Attempt to update the category
        try {
            if ($this->categoryModel->update($id, $data)) {
                return redirect()->to('/admin/categories')->with('success', 'Category updated successfully');
            } else {
                // Get validation errors from the model
                $errors = $this->categoryModel->errors();
                if (!empty($errors)) {
                    return redirect()->back()->withInput()->with('errors', $errors);
                } else {
                    return redirect()->back()->withInput()->with('error', 'Failed to update category. Please check your data and try again.');
                }
            }
        } catch (\Exception $e) {
            log_message('error', 'Category update error: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while updating the category: ' . $e->getMessage());
        }
    }

    public function delete($id)
    {
        $category = $this->categoryModel->find($id);

        if (!$category) {
            return redirect()->to('/admin/categories')->with('error', 'Category not found');
        }

        // Check if category has news articles
        $newsModel = new \App\Models\News();
        $newsCount = $newsModel->where('category_id', $id)->countAllResults();

        if ($newsCount > 0) {
            return redirect()->to('/admin/categories')->with('error', 'Cannot delete category. It has ' . $newsCount . ' news articles.');
        }

        if ($this->categoryModel->delete($id)) {
            return redirect()->to('/admin/categories')->with('success', 'Category deleted successfully');
        } else {
            return redirect()->to('/admin/categories')->with('error', 'Failed to delete category');
        }
    }
}
