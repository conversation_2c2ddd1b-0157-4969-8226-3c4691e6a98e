<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-share-alt me-2"></i>Social Media Settings</h2>
                <a href="<?= base_url('admin/settings') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Settings
                </a>
            </div>

            <!-- Success/Error Messages -->
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fab fa-facebook me-2"></i>Social Media Links
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="<?= base_url('admin/settings/social/update') ?>" method="post">
                                <?= csrf_field() ?>
                                
                                <!-- Facebook -->
                                <div class="mb-3">
                                    <label for="facebook_url" class="form-label">
                                        <i class="fab fa-facebook text-primary me-2"></i>Facebook Page URL
                                    </label>
                                    <input type="url" class="form-control" id="facebook_url" name="facebook_url" 
                                           value="<?= old('facebook_url', getSetting('facebook_url')) ?>" 
                                           placeholder="https://facebook.com/yourpage">
                                </div>

                                <!-- Twitter -->
                                <div class="mb-3">
                                    <label for="twitter_url" class="form-label">
                                        <i class="fab fa-twitter text-info me-2"></i>Twitter Profile URL
                                    </label>
                                    <input type="url" class="form-control" id="twitter_url" name="twitter_url" 
                                           value="<?= old('twitter_url', getSetting('twitter_url')) ?>" 
                                           placeholder="https://twitter.com/yourprofile">
                                </div>

                                <!-- Instagram -->
                                <div class="mb-3">
                                    <label for="instagram_url" class="form-label">
                                        <i class="fab fa-instagram text-danger me-2"></i>Instagram Profile URL
                                    </label>
                                    <input type="url" class="form-control" id="instagram_url" name="instagram_url" 
                                           value="<?= old('instagram_url', getSetting('instagram_url')) ?>" 
                                           placeholder="https://instagram.com/yourprofile">
                                </div>

                                <!-- YouTube -->
                                <div class="mb-3">
                                    <label for="youtube_url" class="form-label">
                                        <i class="fab fa-youtube text-danger me-2"></i>YouTube Channel URL
                                    </label>
                                    <input type="url" class="form-control" id="youtube_url" name="youtube_url" 
                                           value="<?= old('youtube_url', getSetting('youtube_url')) ?>" 
                                           placeholder="https://youtube.com/c/yourchannel">
                                </div>

                                <!-- LinkedIn -->
                                <div class="mb-3">
                                    <label for="linkedin_url" class="form-label">
                                        <i class="fab fa-linkedin text-primary me-2"></i>LinkedIn Profile URL
                                    </label>
                                    <input type="url" class="form-control" id="linkedin_url" name="linkedin_url" 
                                           value="<?= old('linkedin_url', getSetting('linkedin_url')) ?>" 
                                           placeholder="https://linkedin.com/company/yourcompany">
                                </div>

                                <!-- Telegram -->
                                <div class="mb-3">
                                    <label for="telegram_url" class="form-label">
                                        <i class="fab fa-telegram text-info me-2"></i>Telegram Channel URL
                                    </label>
                                    <input type="url" class="form-control" id="telegram_url" name="telegram_url" 
                                           value="<?= old('telegram_url', getSetting('telegram_url')) ?>" 
                                           placeholder="https://t.me/yourchannel">
                                </div>

                                <!-- Submit Button -->
                                <div class="d-flex justify-content-between">
                                    <a href="<?= base_url('admin/settings') ?>" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Update Social Media Settings
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Social Media Preview -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-eye me-2"></i>Social Media Links Preview
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="social-preview">
                                <h6 class="mb-3">Follow Us</h6>
                                
                                <div class="d-flex flex-wrap gap-2">
                                    <a href="#" id="preview-facebook" class="btn btn-outline-primary btn-sm" style="display: none;">
                                        <i class="fab fa-facebook me-1"></i>Facebook
                                    </a>
                                    <a href="#" id="preview-twitter" class="btn btn-outline-info btn-sm" style="display: none;">
                                        <i class="fab fa-twitter me-1"></i>Twitter
                                    </a>
                                    <a href="#" id="preview-instagram" class="btn btn-outline-danger btn-sm" style="display: none;">
                                        <i class="fab fa-instagram me-1"></i>Instagram
                                    </a>
                                    <a href="#" id="preview-youtube" class="btn btn-outline-danger btn-sm" style="display: none;">
                                        <i class="fab fa-youtube me-1"></i>YouTube
                                    </a>
                                    <a href="#" id="preview-linkedin" class="btn btn-outline-primary btn-sm" style="display: none;">
                                        <i class="fab fa-linkedin me-1"></i>LinkedIn
                                    </a>
                                    <a href="#" id="preview-telegram" class="btn btn-outline-info btn-sm" style="display: none;">
                                        <i class="fab fa-telegram me-1"></i>Telegram
                                    </a>
                                </div>
                                
                                <div class="mt-3">
                                    <small class="text-muted">Only links with URLs will be displayed</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Social Media Guidelines -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>Guidelines
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled small mb-0">
                                <li><i class="fas fa-check text-success me-2"></i>Use complete URLs with https://</li>
                                <li><i class="fas fa-check text-success me-2"></i>Verify links are working</li>
                                <li><i class="fas fa-check text-success me-2"></i>Use official page/profile URLs</li>
                                <li><i class="fas fa-check text-success me-2"></i>Keep profiles active and updated</li>
                                <li><i class="fas fa-check text-success me-2"></i>Empty fields won't show on website</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Current Social Links -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-link me-2"></i>Current Links
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="current-links">
                                <?php
                                $socialLinks = [
                                    'facebook_url' => ['icon' => 'fab fa-facebook', 'name' => 'Facebook', 'color' => 'primary'],
                                    'twitter_url' => ['icon' => 'fab fa-twitter', 'name' => 'Twitter', 'color' => 'info'],
                                    'instagram_url' => ['icon' => 'fab fa-instagram', 'name' => 'Instagram', 'color' => 'danger'],
                                    'youtube_url' => ['icon' => 'fab fa-youtube', 'name' => 'YouTube', 'color' => 'danger'],
                                    'linkedin_url' => ['icon' => 'fab fa-linkedin', 'name' => 'LinkedIn', 'color' => 'primary'],
                                    'telegram_url' => ['icon' => 'fab fa-telegram', 'name' => 'Telegram', 'color' => 'info'],
                                ];
                                
                                $hasLinks = false;
                                foreach ($socialLinks as $key => $social):
                                    $url = getSetting($key);
                                    if ($url):
                                        $hasLinks = true;
                                ?>
                                    <div class="mb-2">
                                        <i class="<?= $social['icon'] ?> text-<?= $social['color'] ?> me-2"></i>
                                        <small><?= $social['name'] ?>: 
                                            <a href="<?= esc($url) ?>" target="_blank" class="text-decoration-none">
                                                <?= esc(substr($url, 0, 30)) ?><?= strlen($url) > 30 ? '...' : '' ?>
                                            </a>
                                        </small>
                                    </div>
                                <?php 
                                    endif;
                                endforeach;
                                
                                if (!$hasLinks):
                                ?>
                                    <small class="text-muted">No social media links configured yet</small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live preview updates
    const socialInputs = {
        'facebook_url': 'preview-facebook',
        'twitter_url': 'preview-twitter',
        'instagram_url': 'preview-instagram',
        'youtube_url': 'preview-youtube',
        'linkedin_url': 'preview-linkedin',
        'telegram_url': 'preview-telegram'
    };
    
    Object.keys(socialInputs).forEach(inputId => {
        const input = document.getElementById(inputId);
        const preview = document.getElementById(socialInputs[inputId]);
        
        input.addEventListener('input', function() {
            if (this.value.trim()) {
                preview.href = this.value;
                preview.style.display = 'inline-block';
            } else {
                preview.style.display = 'none';
            }
        });
        
        // Initialize preview state
        if (input.value.trim()) {
            preview.href = input.value;
            preview.style.display = 'inline-block';
        }
    });
    
    // URL validation
    const urlInputs = document.querySelectorAll('input[type="url"]');
    urlInputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value && !isValidUrl(this.value)) {
                this.setCustomValidity('Please enter a valid URL starting with https://');
            } else {
                this.setCustomValidity('');
            }
        });
    });
    
    function isValidUrl(string) {
        try {
            new URL(string);
            return string.startsWith('https://') || string.startsWith('http://');
        } catch (_) {
            return false;
        }
    }
});
</script>
<?= $this->endSection() ?>
