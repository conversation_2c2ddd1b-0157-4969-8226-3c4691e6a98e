{"url": "http://localhost/bbc_news/public/index.php/", "method": "GET", "isAJAX": false, "startTime": **********.580138, "totalTime": 108.60000000000001, "totalMemory": "6.430", "segmentDuration": 20, "segmentCount": 6, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.5832, "duration": 0.016560077667236328}, {"name": "Required Before Filters", "component": "Timer", "start": **********.599761, "duration": 0.0020749568939208984}, {"name": "Routing", "component": "Timer", "start": **********.601844, "duration": 0.0006530284881591797}, {"name": "Before Filters", "component": "Timer", "start": **********.602655, "duration": 1.4066696166992188e-05}, {"name": "Controller", "component": "Timer", "start": **********.602671, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.602671, "duration": 0.*****************}, {"name": "After Filters", "component": "Timer", "start": **********.688379, "duration": 5.0067901611328125e-06}, {"name": "Required After Filters", "component": "Timer", "start": **********.688412, "duration": 0.0003819465637207031}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(10 total Queries, 10 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "18.19 ms", "sql": "<strong>SELECT</strong> `news`.*, `categories`.`name` as `category_name`, `users`.`full_name` as `author_name`\n<strong>FROM</strong> `news`\n<strong>JOIN</strong> `categories` <strong>ON</strong> `categories`.`id` = `news`.`category_id`\n<strong>JOIN</strong> `users` <strong>ON</strong> `users`.`id` = `news`.`author_id`\n<strong>WHERE</strong> `news`.`featured` = 1\n<strong>AND</strong> `news`.`status` = &#039;published&#039;\n<strong>ORDER</strong> <strong>BY</strong> `news`.`created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 5", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\News.php:118", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Home.php:31", "function": "        App\\Models\\News->getFeaturedNews()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\News.php:118", "qid": "5c5fc7152627e090d7d1e9307bf50c45"}, {"hover": "", "class": "", "duration": "15.14 ms", "sql": "<strong>SELECT</strong> `news`.*, `categories`.`name` as `category_name`, `users`.`full_name` as `author_name`\n<strong>FROM</strong> `news`\n<strong>JOIN</strong> `categories` <strong>ON</strong> `categories`.`id` = `news`.`category_id`\n<strong>JOIN</strong> `users` <strong>ON</strong> `users`.`id` = `news`.`author_id`\n<strong>WHERE</strong> `news`.`status` = &#039;published&#039;\n<strong>ORDER</strong> <strong>BY</strong> `news`.`created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 10", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\News.php:90", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Home.php:32", "function": "        App\\Models\\News->getNewsWithDetails()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\News.php:90", "qid": "727d919d65e72948a42cb53ad7644c88"}, {"hover": "", "class": "", "duration": "0.2 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `categories`\n<strong>WHERE</strong> `status` = &#039;active&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Home.php:33", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\Home.php:33", "qid": "0b42e27dac799e9aff45c1ab19180c6a"}, {"hover": "", "class": "", "duration": "31.56 ms", "sql": "<strong>SELECT</strong> `breaking_news`.*, `users`.`full_name` as `created_by_name`\n<strong>FROM</strong> `breaking_news`\n<strong>JOIN</strong> `users` <strong>ON</strong> `users`.`id` = `breaking_news`.`created_by`\n<strong>WHERE</strong> `breaking_news`.`status` = &#039;active&#039;\n<strong>AND</strong> (breaking_news.start_time <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> breaking_news.start_time &lt;= NOW())\n<strong>AND</strong> (breaking_news.end_time <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> breaking_news.end_time &gt;= NOW())\n<strong>ORDER</strong> <strong>BY</strong> `breaking_news`.`priority` <strong>DESC</strong>, `breaking_news`.`created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 5", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\BreakingNews.php:78", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Home.php:34", "function": "        App\\Models\\BreakingNews->getActiveBreakingNews()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\BreakingNews.php:78", "qid": "58d16be04f6f5c0ce0afc015d6e786e6"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `ads`\n<strong>WHERE</strong> `position` = &#039;header&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n<strong>AND</strong> (start_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> start_date &lt;= NOW())\n<strong>AND</strong> (end_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> end_date &gt;= NOW())", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\Ad.php:58", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Home.php:35", "function": "        App\\Models\\Ad->getActiveAdsByPosition()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\Ad.php:58", "qid": "8950e97315547b12a7bda20de89e9e7b"}, {"hover": "", "class": "", "duration": "0.15 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `ads`\n<strong>WHERE</strong> `position` = &#039;sidebar&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n<strong>AND</strong> (start_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> start_date &lt;= NOW())\n<strong>AND</strong> (end_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> end_date &gt;= NOW())", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\Ad.php:58", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Home.php:36", "function": "        App\\Models\\Ad->getActiveAdsByPosition()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\Ad.php:58", "qid": "570b89262119cbb38659c58643f7c913"}, {"hover": "", "class": "", "duration": "0.12 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `ads`\n<strong>WHERE</strong> `position` = &#039;footer&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n<strong>AND</strong> (start_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> start_date &lt;= NOW())\n<strong>AND</strong> (end_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> end_date &gt;= NOW())", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\Ad.php:58", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Home.php:37", "function": "        App\\Models\\Ad->getActiveAdsByPosition()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\Ad.php:58", "qid": "bb52592db51418158122252f321436b0"}, {"hover": "", "class": "", "duration": "0.12 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `ads`\n<strong>WHERE</strong> `position` = &#039;between_news&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n<strong>AND</strong> (start_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> start_date &lt;= NOW())\n<strong>AND</strong> (end_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> end_date &gt;= NOW())", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\Ad.php:58", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Models\\Ad.php:96", "function": "        App\\Models\\Ad->getActiveAdsByPosition()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Home.php:38", "function": "        App\\Models\\Ad->getRotatingAdsByPosition()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\Ad.php:58", "qid": "d86ec50dd505bdb1f556750cba65fb78"}, {"hover": "", "class": "", "duration": "0.12 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `ads`\n<strong>WHERE</strong> `position` = &#039;floating&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n<strong>AND</strong> (start_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> start_date &lt;= NOW())\n<strong>AND</strong> (end_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> end_date &gt;= NOW())", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\Ad.php:58", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Home.php:39", "function": "        App\\Models\\Ad->getActiveAdsByPosition()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\Ad.php:58", "qid": "a29f3f15204d38e6663313c4c9272f2e"}, {"hover": "", "class": "", "duration": "0.12 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `ads`\n<strong>WHERE</strong> `position` = &#039;sticky_bottom&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n<strong>AND</strong> (start_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> start_date &lt;= NOW())\n<strong>AND</strong> (end_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> end_date &gt;= NOW())", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\Ad.php:58", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Home.php:40", "function": "        App\\Models\\Ad->getActiveAdsByPosition()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\Ad.php:58", "qid": "c3931132911760d6de18f17fb495985b"}]}, "badgeValue": 10, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.614006, "duration": "0.000741"}, {"name": "Query", "component": "Database", "start": **********.615275, "duration": "0.018187", "query": "<strong>SELECT</strong> `news`.*, `categories`.`name` as `category_name`, `users`.`full_name` as `author_name`\n<strong>FROM</strong> `news`\n<strong>JOIN</strong> `categories` <strong>ON</strong> `categories`.`id` = `news`.`category_id`\n<strong>JOIN</strong> `users` <strong>ON</strong> `users`.`id` = `news`.`author_id`\n<strong>WHERE</strong> `news`.`featured` = 1\n<strong>AND</strong> `news`.`status` = &#039;published&#039;\n<strong>ORDER</strong> <strong>BY</strong> `news`.`created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 5"}, {"name": "Query", "component": "Database", "start": **********.634589, "duration": "0.015138", "query": "<strong>SELECT</strong> `news`.*, `categories`.`name` as `category_name`, `users`.`full_name` as `author_name`\n<strong>FROM</strong> `news`\n<strong>JOIN</strong> `categories` <strong>ON</strong> `categories`.`id` = `news`.`category_id`\n<strong>JOIN</strong> `users` <strong>ON</strong> `users`.`id` = `news`.`author_id`\n<strong>WHERE</strong> `news`.`status` = &#039;published&#039;\n<strong>ORDER</strong> <strong>BY</strong> `news`.`created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 10"}, {"name": "Query", "component": "Database", "start": **********.649908, "duration": "0.000203", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `categories`\n<strong>WHERE</strong> `status` = &#039;active&#039;"}, {"name": "Query", "component": "Database", "start": **********.65031, "duration": "0.031557", "query": "<strong>SELECT</strong> `breaking_news`.*, `users`.`full_name` as `created_by_name`\n<strong>FROM</strong> `breaking_news`\n<strong>JOIN</strong> `users` <strong>ON</strong> `users`.`id` = `breaking_news`.`created_by`\n<strong>WHERE</strong> `breaking_news`.`status` = &#039;active&#039;\n<strong>AND</strong> (breaking_news.start_time <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> breaking_news.start_time &lt;= NOW())\n<strong>AND</strong> (breaking_news.end_time <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> breaking_news.end_time &gt;= NOW())\n<strong>ORDER</strong> <strong>BY</strong> `breaking_news`.`priority` <strong>DESC</strong>, `breaking_news`.`created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 5"}, {"name": "Query", "component": "Database", "start": **********.682092, "duration": "0.000292", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `ads`\n<strong>WHERE</strong> `position` = &#039;header&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n<strong>AND</strong> (start_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> start_date &lt;= NOW())\n<strong>AND</strong> (end_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> end_date &gt;= NOW())"}, {"name": "Query", "component": "Database", "start": **********.682531, "duration": "0.000155", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `ads`\n<strong>WHERE</strong> `position` = &#039;sidebar&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n<strong>AND</strong> (start_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> start_date &lt;= NOW())\n<strong>AND</strong> (end_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> end_date &gt;= NOW())"}, {"name": "Query", "component": "Database", "start": **********.682762, "duration": "0.000124", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `ads`\n<strong>WHERE</strong> `position` = &#039;footer&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n<strong>AND</strong> (start_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> start_date &lt;= NOW())\n<strong>AND</strong> (end_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> end_date &gt;= NOW())"}, {"name": "Query", "component": "Database", "start": **********.682957, "duration": "0.000120", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `ads`\n<strong>WHERE</strong> `position` = &#039;between_news&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n<strong>AND</strong> (start_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> start_date &lt;= NOW())\n<strong>AND</strong> (end_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> end_date &gt;= NOW())"}, {"name": "Query", "component": "Database", "start": **********.683151, "duration": "0.000122", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `ads`\n<strong>WHERE</strong> `position` = &#039;floating&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n<strong>AND</strong> (start_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> start_date &lt;= NOW())\n<strong>AND</strong> (end_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> end_date &gt;= NOW())"}, {"name": "Query", "component": "Database", "start": **********.683345, "duration": "0.000122", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `ads`\n<strong>WHERE</strong> `position` = &#039;sticky_bottom&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n<strong>AND</strong> (start_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> start_date &lt;= NOW())\n<strong>AND</strong> (end_date <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> end_date &gt;= NOW())"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": []}, "badgeValue": null, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 4, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: components/ad_display.php", "component": "Views", "start": **********.686094, "duration": 0.000331878662109375}, {"name": "View: components/ad_display.php", "component": "Views", "start": **********.686664, "duration": 0.00023293495178222656}, {"name": "View: frontend/layout.php", "component": "Views", "start": **********.687382, "duration": 0.0008609294891357422}, {"name": "View: frontend/home.php", "component": "Views", "start": **********.685245, "duration": 0.0030760765075683594}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 157 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH\\Database\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH\\Database\\Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\I18n\\Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH\\Controllers\\Home.php", "name": "Home.php"}, {"path": "APPPATH\\Helpers\\hindi_helper.php", "name": "hindi_helper.php"}, {"path": "APPPATH\\Models\\Ad.php", "name": "Ad.php"}, {"path": "APPPATH\\Models\\BreakingNews.php", "name": "BreakingNews.php"}, {"path": "APPPATH\\Models\\Category.php", "name": "Category.php"}, {"path": "APPPATH\\Models\\News.php", "name": "News.php"}, {"path": "APPPATH\\Models\\Poll.php", "name": "Poll.php"}, {"path": "APPPATH\\Views\\components\\ad_display.php", "name": "ad_display.php"}, {"path": "APPPATH\\Views\\frontend\\home.php", "name": "home.php"}, {"path": "APPPATH\\Views\\frontend\\layout.php", "name": "layout.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}, {"path": "VENDORPATH\\autoload.php", "name": "autoload.php"}, {"path": "VENDORPATH\\composer\\ClassLoader.php", "name": "ClassLoader.php"}, {"path": "VENDORPATH\\composer\\InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "VENDORPATH\\composer\\autoload_real.php", "name": "autoload_real.php"}, {"path": "VENDORPATH\\composer\\autoload_static.php", "name": "autoload_static.php"}, {"path": "VENDORPATH\\composer\\installed.php", "name": "installed.php"}, {"path": "VENDORPATH\\composer\\platform_check.php", "name": "platform_check.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\Escaper.php", "name": "Escaper.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "VENDORPATH\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php", "name": "deep_copy.php"}, {"path": "VENDORPATH\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php", "name": "Functions.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LogLevel.php", "name": "LogLevel.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "VENDORPATH\\symfony\\deprecation-contracts\\function.php", "name": "function.php"}]}, "badgeValue": 157, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Home", "method": "index", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "news/(.*)", "handler": "\\App\\Controllers\\Home::news/$1"}, {"method": "GET", "route": "category/(.*)", "handler": "\\App\\Controllers\\Home::category/$1"}, {"method": "GET", "route": "poll/results/([0-9]+)", "handler": "\\App\\Controllers\\PollController::getResults/$1"}, {"method": "GET", "route": "admin/login", "handler": "\\App\\Controllers\\Auth::login"}, {"method": "GET", "route": "admin/logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "admin/dashboard", "handler": "\\App\\Controllers\\Admin\\Dashboard::index"}, {"method": "GET", "route": "admin/users", "handler": "\\App\\Controllers\\Admin\\Users::index"}, {"method": "GET", "route": "admin/users/create", "handler": "\\App\\Controllers\\Admin\\Users::create"}, {"method": "GET", "route": "admin/users/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Users::edit/$1"}, {"method": "GET", "route": "admin/users/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Users::delete/$1"}, {"method": "GET", "route": "admin/news", "handler": "\\App\\Controllers\\Admin\\News::index"}, {"method": "GET", "route": "admin/news/create", "handler": "\\App\\Controllers\\Admin\\News::create"}, {"method": "GET", "route": "admin/news/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\News::edit/$1"}, {"method": "GET", "route": "admin/news/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\News::delete/$1"}, {"method": "GET", "route": "admin/categories", "handler": "\\App\\Controllers\\Admin\\Categories::index"}, {"method": "GET", "route": "admin/categories/create", "handler": "\\App\\Controllers\\Admin\\Categories::create"}, {"method": "GET", "route": "admin/categories/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Categories::edit/$1"}, {"method": "GET", "route": "admin/categories/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Categories::delete/$1"}, {"method": "GET", "route": "admin/tags", "handler": "\\App\\Controllers\\Admin\\Tags::index"}, {"method": "GET", "route": "admin/tags/create", "handler": "\\App\\Controllers\\Admin\\Tags::create"}, {"method": "GET", "route": "admin/tags/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Tags::edit/$1"}, {"method": "GET", "route": "admin/tags/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Tags::delete/$1"}, {"method": "GET", "route": "admin/ads", "handler": "\\App\\Controllers\\Admin\\Ads::index"}, {"method": "GET", "route": "admin/ads/analytics", "handler": "\\App\\Controllers\\Admin\\Ads::analytics"}, {"method": "GET", "route": "admin/ads/create", "handler": "\\App\\Controllers\\Admin\\Ads::create"}, {"method": "GET", "route": "admin/ads/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Ads::edit/$1"}, {"method": "GET", "route": "admin/ads/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Ads::delete/$1"}, {"method": "GET", "route": "admin/breaking-news", "handler": "\\App\\Controllers\\Admin\\BreakingNews::index"}, {"method": "GET", "route": "admin/breaking-news/create", "handler": "\\App\\Controllers\\Admin\\BreakingNews::create"}, {"method": "GET", "route": "admin/breaking-news/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BreakingNews::edit/$1"}, {"method": "GET", "route": "admin/breaking-news/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BreakingNews::delete/$1"}, {"method": "GET", "route": "admin/polls", "handler": "\\App\\Controllers\\Admin\\Polls::index"}, {"method": "GET", "route": "admin/polls/analytics", "handler": "\\App\\Controllers\\Admin\\Polls::analytics"}, {"method": "GET", "route": "admin/polls/results/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Polls::results/$1"}, {"method": "GET", "route": "admin/polls/create", "handler": "\\App\\Controllers\\Admin\\Polls::create"}, {"method": "GET", "route": "admin/polls/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Polls::edit/$1"}, {"method": "GET", "route": "admin/polls/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Polls::delete/$1"}, {"method": "POST", "route": "poll/vote", "handler": "\\App\\Controllers\\PollController::vote"}, {"method": "POST", "route": "api/track-ad-click/([0-9]+)", "handler": "\\App\\Controllers\\Api\\AdTracking::trackClick/$1"}, {"method": "POST", "route": "api/track-ad-impression/([0-9]+)", "handler": "\\App\\Controllers\\Api\\AdTracking::trackImpression/$1"}, {"method": "POST", "route": "admin/authenticate", "handler": "\\App\\Controllers\\Auth::authenticate"}, {"method": "POST", "route": "admin/users/store", "handler": "\\App\\Controllers\\Admin\\Users::store"}, {"method": "POST", "route": "admin/users/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Users::update/$1"}, {"method": "POST", "route": "admin/news/store", "handler": "\\App\\Controllers\\Admin\\News::store"}, {"method": "POST", "route": "admin/news/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\News::update/$1"}, {"method": "POST", "route": "admin/categories/store", "handler": "\\App\\Controllers\\Admin\\Categories::store"}, {"method": "POST", "route": "admin/categories/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Categories::update/$1"}, {"method": "POST", "route": "admin/tags/store", "handler": "\\App\\Controllers\\Admin\\Tags::store"}, {"method": "POST", "route": "admin/tags/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Tags::update/$1"}, {"method": "POST", "route": "admin/ads/store", "handler": "\\App\\Controllers\\Admin\\Ads::store"}, {"method": "POST", "route": "admin/ads/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Ads::update/$1"}, {"method": "POST", "route": "admin/breaking-news/store", "handler": "\\App\\Controllers\\Admin\\BreakingNews::store"}, {"method": "POST", "route": "admin/breaking-news/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BreakingNews::update/$1"}, {"method": "POST", "route": "admin/breaking-news/toggle-status/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BreakingNews::toggleStatus/$1"}, {"method": "POST", "route": "admin/polls/store", "handler": "\\App\\Controllers\\Admin\\Polls::store"}, {"method": "POST", "route": "admin/polls/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Polls::update/$1"}]}, "badgeValue": 39, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "7.56", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "0.15", "count": 10}}}, "badgeValue": 11, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.592198, "duration": 0.007558107376098633}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.633471, "duration": 2.7894973754882812e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.649733, "duration": 2.5987625122070312e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.650114, "duration": 1.3828277587890625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.681873, "duration": 2.7894973754882812e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.682387, "duration": 1.2874603271484375e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.682688, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.682888, "duration": 7.867813110351562e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.683079, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.683274, "duration": 7.867813110351562e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.68347, "duration": 1.0967254638671875e-05}]}], "vars": {"varData": {"View Data": {"title": "BBC News Portal - Latest Hindi News", "featuredNews": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (3)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (3)</li><li>Contents (3)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>title</th><th>slug</th><th>description</th><th>content</th><th>image</th><th>category_id</th><th>author_id</th><th>status</th><th>featured</th><th>views</th><th>published_at</th><th>created_at</th><th>updated_at</th><th>category_name</th><th>author_name</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">1</td><td title=\"UTF-8 string (90)\">&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367; &#2325;&#2366; &#2344;&#2351;&#2366; &#2342;&#2380;&#2352;</td><td title=\"string (41)\">bharata-ma-technology-karata-ka-naya-dara</td><td title=\"UTF-8 string (216)\">&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2361;&#2379; &#2352;&#2361;&#2375; &#2344;&#2357;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2368; &#2360;&#2347;&#2354;&#2340;&#2366; &#2325;&#2368;UTF-8</td><td title=\"UTF-8 string (930)\">&#2349;&#2366;&#2352;&#2340; &#2310;&#2332; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367; &#2325;&#2375; &#2319;&#2325; &#2344;&#2319; &#2342;&#2380;&#2352; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2357;&#2375;&#2358; &#2325;&#2352; &#2352;&#2361;&#2366; &#2361;&#2376;&#2404; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;UTF-8</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">4</td><td title=\"string (1)\">1</td><td title=\"string (9)\">published</td><td title=\"string (1)\">1</td><td title=\"string (4)\">1254</td><td title=\"string (19)\">2025-06-24 06:19:54</td><td title=\"string (19)\">2025-06-24 06:19:54</td><td title=\"string (19)\">2025-06-24 08:43:50</td><td title=\"UTF-8 string (15)\">&#2340;&#2325;&#2344;&#2368;&#2325;</td><td title=\"string (13)\">Administrator</td></tr><tr><th>1</th><td title=\"string (1)\">2</td><td title=\"UTF-8 string (103)\">&#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2332;&#2368;&#2340;</td><td title=\"string (50)\">karakata-vashava-kapa-ma-bharata-ka-shanadara-jata</td><td title=\"UTF-8 string (225)\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;&#2366;&#2348; &#2309;&#2346;&#2344;&#2375;UTF-8</td><td title=\"UTF-8 string (853)\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2310;&#2332; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2376;&#2330; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;UTF-8</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">2</td><td title=\"string (1)\">2</td><td title=\"string (9)\">published</td><td title=\"string (1)\">1</td><td title=\"string (4)\">2104</td><td title=\"string (19)\">2025-06-24 04:19:54</td><td title=\"string (19)\">2025-06-24 04:19:54</td><td title=\"string (19)\">2025-06-24 08:43:50</td><td title=\"UTF-8 string (9)\">&#2326;&#2375;&#2354;</td><td title=\"string (12)\">News Manager</td></tr><tr><th>2</th><td title=\"string (1)\">5</td><td title=\"UTF-8 string (101)\">&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340;</td><td title=\"string (42)\">rajaya-sabha-ma-important-vadhayaka-parata</td><td title=\"UTF-8 string (182)\">&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2310;&#2332; &#2319;&#2325; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2332;&#2379; &#2332;&#2344;&#2340;&#2366; &#2325;&#2375; &#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2361;&#2376;&#2404;</td><td title=\"UTF-8 string (693)\">&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2310;&#2332; &#2319;&#2325; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2361;&#2376; &#2332;&#2379; &#2310;&#2350; &#2332;&#2344;&#2340;&#2366; &#2325;&#2375; &#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2361;&#2376;UTF-8</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"string (9)\">published</td><td title=\"string (1)\">1</td><td title=\"string (4)\">1800</td><td title=\"string (19)\">2025-06-23 22:19:54</td><td title=\"string (19)\">2025-06-23 22:19:54</td><td title=\"string (19)\">2025-06-24 08:43:50</td><td title=\"UTF-8 string (21)\">&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;</td><td title=\"string (13)\">Administrator</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (90) \"&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367; &#2325;&#2366; &#2344;&#2351;&#2366; &#2342;&#2380;&#2352;\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (41) \"bharata-ma-technology-karata-ka-naya-dara\"<div class=\"access-path\">$value[0]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (216) \"&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2361;&#2379; &#2352;&#2361;&#2375; &#2344;&#2357;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2368; &#2360;&#2347;&#2354;&#2340;&#2366; &#2325;&#2368; ...<div class=\"access-path\">$value[0]['description']</div></dt><dd><pre>&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2361;&#2379; &#2352;&#2361;&#2375; &#2344;&#2357;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2368; &#2360;&#2347;&#2354;&#2340;&#2366; &#2325;&#2368; &#2325;&#2361;&#2366;&#2344;&#2368;&#2404;\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>content</dfn> =&gt; <var>UTF-8 string</var> (930) \"&#2349;&#2366;&#2352;&#2340; &#2310;&#2332; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367; &#2325;&#2375; &#2319;&#2325; &#2344;&#2319; &#2342;&#2380;&#2352; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2357;&#2375;&#2358; &#2325;&#2352; &#2352;&#2361;&#2366; &#2361;&#2376;&#2404; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344;...<div class=\"access-path\">$value[0]['content']</div></dt><dd><pre>&#2349;&#2366;&#2352;&#2340; &#2310;&#2332; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367; &#2325;&#2375; &#2319;&#2325; &#2344;&#2319; &#2342;&#2380;&#2352; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2357;&#2375;&#2358; &#2325;&#2352; &#2352;&#2361;&#2366; &#2361;&#2376;&#2404; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2375; &#2340;&#2361;&#2340; &#2342;&#2375;&#2358; &#2349;&#2352; &#2350;&#2375;&#2306; &#2311;&#2306;&#2335;&#2352;&#2344;&#2375;&#2335; &#2325;&#2344;&#2375;&#2325;&#2381;&#2335;&#2367;&#2357;&#2367;&#2335;&#2368;, &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2360;&#2366;&#2325;&#2381;&#2359;&#2352;&#2340;&#2366; &#2324;&#2352; &#2312;-&#2327;&#2357;&#2352;&#2381;&#2344;&#2375;&#2306;&#2360; &#2325;&#2375; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2313;&#2354;&#2381;&#2354;&#2375;&#2326;&#2344;&#2368;&#2351; &#2346;&#2381;&#2352;&#2327;&#2340;&#2367; &#2361;&#2369;&#2312; &#2361;&#2376;&#2404; &#2360;&#2381;&#2335;&#2366;&#2352;&#2381;&#2335;&#2309;&#2346; &#2311;&#2325;&#2379;&#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2342;&#2369;&#2344;&#2367;&#2351;&#2366; &#2325;&#2366; &#2340;&#2368;&#2360;&#2352;&#2366; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2366; &#2342;&#2375;&#2358; &#2348;&#2344; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404; &#2347;&#2367;&#2344;&#2335;&#2375;&#2325;, &#2319;&#2337;&#2335;&#2375;&#2325;, &#2361;&#2375;&#2354;&#2381;&#2341;&#2335;&#2375;&#2325; &#2332;&#2376;&#2360;&#2375; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2379;&#2306; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2306;&#2346;&#2344;&#2367;&#2351;&#2366;&#2306; &#2357;&#2376;&#2358;&#2381;&#2357;&#2367;&#2325; &#2360;&#2381;&#2340;&#2352; &#2346;&#2352; &#2309;&#2346;&#2344;&#2368; &#2346;&#2361;&#2330;&#2366;&#2344; &#2348;&#2344;&#2366; &#2352;&#2361;&#2368; &#2361;&#2376;&#2306;&#2404;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[0]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['author_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (9) \"published\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>views</dfn> =&gt; <var>string</var> (4) \"1254\"<div class=\"access-path\">$value[0]['views']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>published_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 06:19:54\"<div class=\"access-path\">$value[0]['published_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 06:19:54\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 08:43:50\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>UTF-8 string</var> (15) \"&#2340;&#2325;&#2344;&#2368;&#2325;\"<div class=\"access-path\">$value[0]['category_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_name</dfn> =&gt; <var>string</var> (13) \"Administrator\"<div class=\"access-path\">$value[0]['author_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (103) \"&#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2332;&#2368;&#2340;\"<div class=\"access-path\">$value[1]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (50) \"karakata-vashava-kapa-ma-bharata-ka-shanadara-jata\"<div class=\"access-path\">$value[1]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (225) \"&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;&#2366;&#2348; &#2309;&#2346;&#2344;&#2375; ...<div class=\"access-path\">$value[1]['description']</div></dt><dd><pre>&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;&#2366;&#2348; &#2309;&#2346;&#2344;&#2375; &#2344;&#2366;&#2350; &#2325;&#2367;&#2351;&#2366;&#2404;\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>content</dfn> =&gt; <var>UTF-8 string</var> (853) \"&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2310;&#2332; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2376;&#2330; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;&#2366;...<div class=\"access-path\">$value[1]['content']</div></dt><dd><pre>&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2310;&#2332; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2376;&#2330; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;&#2366;&#2348; &#2332;&#2368;&#2340;&#2366; &#2361;&#2376;&#2404; &#2325;&#2346;&#2381;&#2340;&#2366;&#2344; &#2325;&#2368; &#2348;&#2375;&#2361;&#2340;&#2352;&#2368;&#2344; &#2325;&#2346;&#2381;&#2340;&#2366;&#2344;&#2368; &#2324;&#2352; &#2335;&#2368;&#2350; &#2325;&#2375; &#2360;&#2349;&#2368; &#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2367;&#2351;&#2379;&#2306; &#2325;&#2375; &#2313;&#2340;&#2381;&#2325;&#2371;&#2359;&#2381;&#2335; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2360;&#2375; &#2351;&#2361; &#2332;&#2368;&#2340; &#2360;&#2306;&#2349;&#2357; &#2361;&#2369;&#2312;&#2404; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2376;&#2330; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2344;&#2375; 350 &#2352;&#2344; &#2325;&#2366; &#2357;&#2367;&#2358;&#2366;&#2354; &#2360;&#2381;&#2325;&#2379;&#2352; &#2348;&#2344;&#2366;&#2351;&#2366; &#2324;&#2352; &#2357;&#2367;&#2346;&#2325;&#2381;&#2359;&#2368; &#2335;&#2368;&#2350; &#2325;&#2379; 280 &#2352;&#2344; &#2346;&#2352; &#2321;&#2354; &#2310;&#2313;&#2335; &#2325;&#2352; &#2342;&#2367;&#2351;&#2366;&#2404; &#2351;&#2361; &#2332;&#2368;&#2340; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2325;&#2375; &#2311;&#2340;&#2367;&#2361;&#2366;&#2360; &#2350;&#2375;&#2306; &#2319;&#2325; &#2344;&#2351;&#2366; &#2309;&#2343;&#2381;&#2351;&#2366;&#2351; &#2332;&#2379;&#2337;&#2364;&#2340;&#2368; &#2361;&#2376;&#2404;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['author_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (9) \"published\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>views</dfn> =&gt; <var>string</var> (4) \"2104\"<div class=\"access-path\">$value[1]['views']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>published_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 04:19:54\"<div class=\"access-path\">$value[1]['published_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 04:19:54\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 08:43:50\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>UTF-8 string</var> (9) \"&#2326;&#2375;&#2354;\"<div class=\"access-path\">$value[1]['category_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_name</dfn> =&gt; <var>string</var> (12) \"News Manager\"<div class=\"access-path\">$value[1]['author_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (101) \"&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340;\"<div class=\"access-path\">$value[2]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (42) \"rajaya-sabha-ma-important-vadhayaka-parata\"<div class=\"access-path\">$value[2]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (182) \"&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2310;&#2332; &#2319;&#2325; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2332;&#2379; &#2332;&#2344;&#2340;&#2366; &#2325;&#2375; &#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2361;&#2376;&#2404;\"<div class=\"access-path\">$value[2]['description']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>content</dfn> =&gt; <var>UTF-8 string</var> (693) \"&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2310;&#2332; &#2319;&#2325; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2361;&#2376; &#2332;&#2379; &#2310;&#2350; &#2332;&#2344;&#2340;&#2366; &#2325;&#2375; &#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2361;&#2376;&#2404;...<div class=\"access-path\">$value[2]['content']</div></dt><dd><pre>&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2310;&#2332; &#2319;&#2325; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2361;&#2376; &#2332;&#2379; &#2310;&#2350; &#2332;&#2344;&#2340;&#2366; &#2325;&#2375; &#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2361;&#2376;&#2404; &#2311;&#2360; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2360;&#2375; &#2358;&#2367;&#2325;&#2381;&#2359;&#2366;, &#2360;&#2381;&#2357;&#2366;&#2360;&#2381;&#2341;&#2381;&#2351; &#2324;&#2352; &#2352;&#2379;&#2332;&#2327;&#2366;&#2352; &#2325;&#2375; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2360;&#2369;&#2343;&#2366;&#2352; &#2361;&#2379;&#2327;&#2366;&#2404; &#2360;&#2349;&#2368; &#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;&#2325; &#2342;&#2354;&#2379;&#2306; &#2344;&#2375; &#2311;&#2360; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2325;&#2366; &#2360;&#2350;&#2352;&#2381;&#2341;&#2344; &#2325;&#2367;&#2351;&#2366; &#2361;&#2376;&#2404; &#2357;&#2367;&#2346;&#2325;&#2381;&#2359; &#2344;&#2375; &#2349;&#2368; &#2311;&#2360;&#2375; &#2332;&#2344;&#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2348;&#2340;&#2366;&#2351;&#2366; &#2361;&#2376;&#2404; &#2309;&#2348; &#2351;&#2361; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2354;&#2379;&#2325;&#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2349;&#2375;&#2332;&#2366; &#2332;&#2366;&#2319;&#2327;&#2366;&#2404;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['author_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (9) \"published\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>views</dfn> =&gt; <var>string</var> (4) \"1800\"<div class=\"access-path\">$value[2]['views']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>published_at</dfn> =&gt; <var>string</var> (19) \"2025-06-23 22:19:54\"<div class=\"access-path\">$value[2]['published_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-23 22:19:54\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 08:43:50\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>UTF-8 string</var> (21) \"&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;\"<div class=\"access-path\">$value[2]['category_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_name</dfn> =&gt; <var>string</var> (13) \"Administrator\"<div class=\"access-path\">$value[2]['author_name']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "latestNews": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (6)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (6)</li><li>Contents (6)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>title</th><th>slug</th><th>description</th><th>content</th><th>image</th><th>category_id</th><th>author_id</th><th>status</th><th>featured</th><th>views</th><th>published_at</th><th>created_at</th><th>updated_at</th><th>category_name</th><th>author_name</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">6</td><td title=\"UTF-8 string (221)\">&#2346;&#2368;&#2319;&#2350; &#2350;&#2379;&#2342;&#2368; 12 &#2332;&#2370;&#2344; &#2325;&#2379; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2330;&#2306;&#2342;&#2381;&#2352;&#2348;&#2366;&#2348;&#2370; &#2344;&#2366;&#2351;&#2337;&#2370; &#2325;&#2375; &#2358;&#2346;&#2341; &#2327;&#2381;&#2352;&#2361;&#2339; &#2360;&#2350;&#2366;&#2352;&#2379;&#2361; &#2350;&#2375;&#2306; &#2358;UTF-8</td><td title=\"string (101)\">paema-mada-12-jana-ka-tadapa-paramakha-chadarababa-nayada-ka-shapatha-garahUTF-8</td><td title=\"UTF-8 string (818)\">&#2350;&#2344;&#2379;&#2344;&#2368;&#2340; &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2344;&#2352;&#2375;&#2306;&#2342;&#2381;&#2352; &#2350;&#2379;&#2342;&#2368; &#2309;&#2346;&#2344;&#2375; &#2327;&#2336;&#2348;&#2306;&#2343;&#2344; &#2325;&#2375; &#2360;&#2366;&#2341;&#2368; &#2324;&#2352; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2325;&#2375; &#2358;&#2346;UTF-8</td><td title=\"UTF-8 string (4198)\">&#2350;&#2344;&#2379;&#2344;&#2368;&#2340; &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2344;&#2352;&#2375;&#2306;&#2342;&#2381;&#2352; &#2350;&#2379;&#2342;&#2368; &#2309;&#2346;&#2344;&#2375; &#2327;&#2336;&#2348;&#2306;&#2343;&#2344; &#2325;&#2375; &#2360;&#2366;&#2341;&#2368; &#2324;&#2352; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2325;&#2375; &#2358;&#2346;UTF-8</td><td title=\"string (36)\">1750753523_61bb0e8fbeab52801378.jpeg</td><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"string (9)\">published</td><td title=\"string (1)\">0</td><td title=\"string (2)\">14</td><td title=\"string (19)\">2025-06-24 08:25:23</td><td title=\"string (19)\">2025-06-24 08:25:23</td><td title=\"string (19)\">2025-06-24 08:43:50</td><td title=\"UTF-8 string (21)\">&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;</td><td title=\"string (13)\">Administrator</td></tr><tr><th>1</th><td title=\"string (1)\">1</td><td title=\"UTF-8 string (90)\">&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367; &#2325;&#2366; &#2344;&#2351;&#2366; &#2342;&#2380;&#2352;</td><td title=\"string (41)\">bharata-ma-technology-karata-ka-naya-dara</td><td title=\"UTF-8 string (216)\">&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2361;&#2379; &#2352;&#2361;&#2375; &#2344;&#2357;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2368; &#2360;&#2347;&#2354;&#2340;&#2366; &#2325;&#2368;UTF-8</td><td title=\"UTF-8 string (930)\">&#2349;&#2366;&#2352;&#2340; &#2310;&#2332; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367; &#2325;&#2375; &#2319;&#2325; &#2344;&#2319; &#2342;&#2380;&#2352; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2357;&#2375;&#2358; &#2325;&#2352; &#2352;&#2361;&#2366; &#2361;&#2376;&#2404; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;UTF-8</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">4</td><td title=\"string (1)\">1</td><td title=\"string (9)\">published</td><td title=\"string (1)\">1</td><td title=\"string (4)\">1254</td><td title=\"string (19)\">2025-06-24 06:19:54</td><td title=\"string (19)\">2025-06-24 06:19:54</td><td title=\"string (19)\">2025-06-24 08:43:50</td><td title=\"UTF-8 string (15)\">&#2340;&#2325;&#2344;&#2368;&#2325;</td><td title=\"string (13)\">Administrator</td></tr><tr><th>2</th><td title=\"string (1)\">2</td><td title=\"UTF-8 string (103)\">&#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2332;&#2368;&#2340;</td><td title=\"string (50)\">karakata-vashava-kapa-ma-bharata-ka-shanadara-jata</td><td title=\"UTF-8 string (225)\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;&#2366;&#2348; &#2309;&#2346;&#2344;&#2375;UTF-8</td><td title=\"UTF-8 string (853)\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2310;&#2332; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2376;&#2330; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;UTF-8</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">2</td><td title=\"string (1)\">2</td><td title=\"string (9)\">published</td><td title=\"string (1)\">1</td><td title=\"string (4)\">2104</td><td title=\"string (19)\">2025-06-24 04:19:54</td><td title=\"string (19)\">2025-06-24 04:19:54</td><td title=\"string (19)\">2025-06-24 08:43:50</td><td title=\"UTF-8 string (9)\">&#2326;&#2375;&#2354;</td><td title=\"string (12)\">News Manager</td></tr><tr><th>3</th><td title=\"string (1)\">3</td><td title=\"UTF-8 string (121)\">&#2348;&#2377;&#2354;&#2368;&#2357;&#2369;&#2337; &#2350;&#2375;&#2306; &#2344;&#2312; &#2347;&#2367;&#2354;&#2381;&#2350; &#2325;&#2366; &#2343;&#2350;&#2366;&#2325;&#2375;&#2342;&#2366;&#2352; &#2335;&#2381;&#2352;&#2375;&#2354;&#2352; &#2352;&#2367;&#2354;&#2368;&#2332;</td><td title=\"string (55)\">balavada-ma-naee-phalama-ka-dhamakadara-taralara-ralaja</td><td title=\"UTF-8 string (198)\">&#2348;&#2377;&#2354;&#2368;&#2357;&#2369;&#2337; &#2325;&#2368; &#2310;&#2327;&#2366;&#2350;&#2368; &#2348;&#2381;&#2354;&#2377;&#2325;&#2348;&#2360;&#2381;&#2335;&#2352; &#2347;&#2367;&#2354;&#2381;&#2350; &#2325;&#2366; &#2335;&#2381;&#2352;&#2375;&#2354;&#2352; &#2352;&#2367;&#2354;&#2368;&#2332; &#2361;&#2369;&#2310;, &#2342;&#2352;&#2381;&#2358;&#2325;&#2379;&#2306; &#2350;&#2375;&#2306; &#2313;&#2340;&#2381;&#2360;&#2366;&#2361;&#2404;</td><td title=\"UTF-8 string (831)\">&#2348;&#2377;&#2354;&#2368;&#2357;&#2369;&#2337; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2346;&#2381;&#2352;&#2340;&#2368;&#2325;&#2381;&#2359;&#2367;&#2340; &#2347;&#2367;&#2354;&#2381;&#2350; &#2325;&#2366; &#2335;&#2381;&#2352;&#2375;&#2354;&#2352; &#2310;&#2332; &#2352;&#2367;&#2354;&#2368;&#2332; &#2361;&#2369;&#2310; &#2361;&#2376;&#2404; &#2311;&#2360; &#2347;&#2367;&#2354;&#2381;&#2350; &#2350;&#2375;&#2306; &#2348;&#2377;UTF-8</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">3</td><td title=\"string (1)\">1</td><td title=\"string (9)\">published</td><td title=\"string (1)\">0</td><td title=\"string (3)\">890</td><td title=\"string (19)\">2025-06-24 02:19:54</td><td title=\"string (19)\">2025-06-24 02:19:54</td><td title=\"string (19)\">2025-06-24 08:43:50</td><td title=\"UTF-8 string (21)\">&#2350;&#2344;&#2379;&#2352;&#2306;&#2332;&#2344;</td><td title=\"string (13)\">Administrator</td></tr><tr><th>4</th><td title=\"string (1)\">4</td><td title=\"UTF-8 string (113)\">&#2360;&#2352;&#2325;&#2366;&#2352; &#2325;&#2368; &#2344;&#2312; &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2344;&#2368;&#2340;&#2367; &#2360;&#2375; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352; &#2350;&#2375;&#2306; &#2340;&#2375;&#2332;&#2368;</td><td title=\"string (51)\">sarakara-ka-naee-aarathaka-nata-sa-business-ma-taja</td><td title=\"UTF-8 string (235)\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352; &#2360;&#2352;&#2325;&#2366;&#2352; &#2325;&#2368; &#2344;&#2312; &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2344;&#2368;&#2340;&#2367; &#2360;&#2375; &#2331;&#2379;&#2335;&#2375; &#2324;&#2352; &#2350;&#2343;&#2381;&#2351;&#2350; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306; &#2325;&#2379; &#2348;&#2337;&#2364;&#2366; &#2347;&#2366;&#2351;&#2342;&#2366; &#2361;UTF-8</td><td title=\"UTF-8 string (797)\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352; &#2360;&#2352;&#2325;&#2366;&#2352; &#2344;&#2375; &#2310;&#2332; &#2319;&#2325; &#2344;&#2312; &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2344;&#2368;&#2340;&#2367; &#2325;&#2368; &#2328;&#2379;&#2359;&#2339;&#2366; &#2325;&#2368; &#2361;&#2376; &#2332;&#2379; &#2331;&#2379;&#2335;&#2375; &#2324;&#2352; &#2350;&#2343;&#2381;&#2351;&#2350; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;UTF-8</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">5</td><td title=\"string (1)\">2</td><td title=\"string (9)\">published</td><td title=\"string (1)\">0</td><td title=\"string (3)\">651</td><td title=\"string (19)\">2025-06-24 00:19:54</td><td title=\"string (19)\">2025-06-24 00:19:54</td><td title=\"string (19)\">2025-06-24 08:43:50</td><td title=\"UTF-8 string (21)\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;</td><td title=\"string (12)\">News Manager</td></tr><tr><th>5</th><td title=\"string (1)\">5</td><td title=\"UTF-8 string (101)\">&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340;</td><td title=\"string (42)\">rajaya-sabha-ma-important-vadhayaka-parata</td><td title=\"UTF-8 string (182)\">&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2310;&#2332; &#2319;&#2325; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2332;&#2379; &#2332;&#2344;&#2340;&#2366; &#2325;&#2375; &#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2361;&#2376;&#2404;</td><td title=\"UTF-8 string (693)\">&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2310;&#2332; &#2319;&#2325; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2361;&#2376; &#2332;&#2379; &#2310;&#2350; &#2332;&#2344;&#2340;&#2366; &#2325;&#2375; &#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2361;&#2376;UTF-8</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"string (9)\">published</td><td title=\"string (1)\">1</td><td title=\"string (4)\">1800</td><td title=\"string (19)\">2025-06-23 22:19:54</td><td title=\"string (19)\">2025-06-23 22:19:54</td><td title=\"string (19)\">2025-06-24 08:43:50</td><td title=\"UTF-8 string (21)\">&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;</td><td title=\"string (13)\">Administrator</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (221) \"&#2346;&#2368;&#2319;&#2350; &#2350;&#2379;&#2342;&#2368; 12 &#2332;&#2370;&#2344; &#2325;&#2379; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2330;&#2306;&#2342;&#2381;&#2352;&#2348;&#2366;&#2348;&#2370; &#2344;&#2366;&#2351;&#2337;&#2370; &#2325;&#2375; &#2358;&#2346;&#2341; &#2327;&#2381;&#2352;&#2361;&#2339; &#2360;&#2350;&#2366;&#2352;&#2379;&#2361; &#2350;&#2375;&#2306; &#2358;&#2366;...<div class=\"access-path\">$value[0]['title']</div></dt><dd><pre>&#2346;&#2368;&#2319;&#2350; &#2350;&#2379;&#2342;&#2368; 12 &#2332;&#2370;&#2344; &#2325;&#2379; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2330;&#2306;&#2342;&#2381;&#2352;&#2348;&#2366;&#2348;&#2370; &#2344;&#2366;&#2351;&#2337;&#2370; &#2325;&#2375; &#2358;&#2346;&#2341; &#2327;&#2381;&#2352;&#2361;&#2339; &#2360;&#2350;&#2366;&#2352;&#2379;&#2361; &#2350;&#2375;&#2306; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2379;&#2306;&#2327;&#2375;\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>slug</dfn> =&gt; <var>string</var> (101) \"paema-mada-12-jana-ka-tadapa-paramakha-chadarababa-nayada-ka-shapatha-garaha...<div class=\"access-path\">$value[0]['slug']</div></dt><dd><pre>paema-mada-12-jana-ka-tadapa-paramakha-chadarababa-nayada-ka-shapatha-garaha-samaraha-ma-shamala-haga\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (818) \"&#2350;&#2344;&#2379;&#2344;&#2368;&#2340; &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2344;&#2352;&#2375;&#2306;&#2342;&#2381;&#2352; &#2350;&#2379;&#2342;&#2368; &#2309;&#2346;&#2344;&#2375; &#2327;&#2336;&#2348;&#2306;&#2343;&#2344; &#2325;&#2375; &#2360;&#2366;&#2341;&#2368; &#2324;&#2352; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2325;&#2375; &#2358;&#2346;&#2341;...<div class=\"access-path\">$value[0]['description']</div></dt><dd><pre>&#2350;&#2344;&#2379;&#2344;&#2368;&#2340; &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2344;&#2352;&#2375;&#2306;&#2342;&#2381;&#2352; &#2350;&#2379;&#2342;&#2368; &#2309;&#2346;&#2344;&#2375; &#2327;&#2336;&#2348;&#2306;&#2343;&#2344; &#2325;&#2375; &#2360;&#2366;&#2341;&#2368; &#2324;&#2352; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2325;&#2375; &#2358;&#2346;&#2341; &#2327;&#2381;&#2352;&#2361;&#2339; &#2360;&#2350;&#2366;&#2352;&#2379;&#2361; &#2350;&#2375;&#2306; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2379; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2330;&#2306;&#2342;&#2381;&#2352;&#2348;&#2366;&#2348;&#2370; &#2344;&#2366;&#2351;&#2337;&#2370; &#2310;&#2306;&#2343;&#2381;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358; &#2325;&#2375; &#2350;&#2369;&#2326;&#2381;&#2351;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; 12 &#2332;&#2370;&#2344; &#2325;&#2379; &#2358;&#2346;&#2341; &#2354;&#2375;&#2306;&#2327;&#2375;&#2404; &#2348;&#2340;&#2366; &#2342;&#2375;&#2306;, &#2350;&#2344;&#2379;&#2344;&#2368;&#2340; &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2350;&#2379;&#2342;&#2368; &#2310;&#2332; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367; &#2349;&#2357;&#2344; &#2350;&#2375;&#2306; &#2340;&#2368;&#2360;&#2352;&#2368; &#2348;&#2366;&#2352; &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2358;&#2346;&#2341; &#2327;&#2381;&#2352;&#2361;&#2339; &#2325;&#2352;&#2375;&#2306;&#2327;&#2375;&#2404;\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>content</dfn> =&gt; <var>UTF-8 string</var> (4198) \"&#2350;&#2344;&#2379;&#2344;&#2368;&#2340; &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2344;&#2352;&#2375;&#2306;&#2342;&#2381;&#2352; &#2350;&#2379;&#2342;&#2368; &#2309;&#2346;&#2344;&#2375; &#2327;&#2336;&#2348;&#2306;&#2343;&#2344; &#2325;&#2375; &#2360;&#2366;&#2341;&#2368; &#2324;&#2352; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2325;&#2375; &#2358;&#2346;&#2341;...<div class=\"access-path\">$value[0]['content']</div></dt><dd><pre>&#2350;&#2344;&#2379;&#2344;&#2368;&#2340; &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2344;&#2352;&#2375;&#2306;&#2342;&#2381;&#2352; &#2350;&#2379;&#2342;&#2368; &#2309;&#2346;&#2344;&#2375; &#2327;&#2336;&#2348;&#2306;&#2343;&#2344; &#2325;&#2375; &#2360;&#2366;&#2341;&#2368; &#2324;&#2352; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2325;&#2375; &#2358;&#2346;&#2341; &#2327;&#2381;&#2352;&#2361;&#2339; &#2360;&#2350;&#2366;&#2352;&#2379;&#2361; &#2350;&#2375;&#2306; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2379; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2330;&#2306;&#2342;&#2381;&#2352;&#2348;&#2366;&#2348;&#2370; &#2344;&#2366;&#2351;&#2337;&#2370; &#2310;&#2306;&#2343;&#2381;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358; &#2325;&#2375; &#2350;&#2369;&#2326;&#2381;&#2351;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; 12 &#2332;&#2370;&#2344; &#2325;&#2379; &#2358;&#2346;&#2341; &#2354;&#2375;&#2306;&#2327;&#2375;&#2404; &#2348;&#2340;&#2366; &#2342;&#2375;&#2306;, &#2350;&#2344;&#2379;&#2344;&#2368;&#2340; &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2350;&#2379;&#2342;&#2368; &#2310;&#2332; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367; &#2349;&#2357;&#2344; &#2350;&#2375;&#2306; &#2340;&#2368;&#2360;&#2352;&#2368; &#2348;&#2366;&#2352; &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2358;&#2346;&#2341; &#2327;&#2381;&#2352;&#2361;&#2339; &#2325;&#2352;&#2375;&#2306;&#2327;&#2375;&#2404;\r\n\r\n \r\n\r\n\r\n&#2350;&#2369;&#2326;&#2381;&#2351; &#2360;&#2330;&#2367;&#2357; &#2344;&#2368;&#2352;&#2349; &#2325;&#2369;&#2350;&#2366;&#2352; &#2346;&#2381;&#2352;&#2360;&#2366;&#2342; &#2344;&#2375; &#2358;&#2344;&#2367;&#2357;&#2366;&#2352; &#2325;&#2379; &#2348;&#2340;&#2366;&#2351;&#2366; &#2325;&#2367; &#2358;&#2346;&#2341; &#2327;&#2381;&#2352;&#2361;&#2339; &#2360;&#2350;&#2366;&#2352;&#2379;&#2361; &#2350;&#2375;&#2306; &#2346;&#2368;&#2319;&#2350; &#2350;&#2379;&#2342;&#2368;, &#2352;&#2366;&#2332;&#2381;&#2351;&#2346;&#2366;&#2354; &#2319;&#2360; &#2309;&#2348;&#2381;&#2342;&#2369;&#2354; &#2344;&#2332;&#2368;&#2352; &#2324;&#2352; &#2309;&#2344;&#2381;&#2351; &#2327;&#2339;&#2350;&#2366;&#2344;&#2381;&#2351; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2379; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2344;&#2366;&#2351;&#2337;&#2370; &#2348;&#2369;&#2343;&#2357;&#2366;&#2352; &#2325;&#2379; &#2360;&#2369;&#2348;&#2361; 11:27 &#2348;&#2332;&#2375; &#2327;&#2344;&#2381;&#2344;&#2357;&#2352;&#2350; &#2361;&#2357;&#2366;&#2312; &#2309;&#2337;&#2381;&#2337;&#2375; &#2325;&#2375; &#2346;&#2366;&#2360; &#2325;&#2375;&#2360;&#2352;&#2346;&#2354;&#2381;&#2354;&#2368; &#2310;&#2312;&#2335;&#2368; &#2346;&#2366;&#2352;&#2381;&#2325; &#2350;&#2375;&#2306; &#2358;&#2346;&#2341; &#2354;&#2375;&#2306;&#2327;&#2375;&#2404; &#2350;&#2369;&#2326;&#2381;&#2351; &#2360;&#2330;&#2367;&#2357; &#2344;&#2375; &#2360;&#2350;&#2366;&#2352;&#2379;&#2361; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2368; &#2332;&#2366; &#2352;&#2361;&#2368; &#2340;&#2376;&#2351;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306; &#2325;&#2368; &#2360;&#2350;&#2368;&#2325;&#2381;&#2359;&#2366; &#2325;&#2368; &#2324;&#2352; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306; &#2325;&#2379; &#2346;&#2369;&#2326;&#2381;&#2340;&#2366; &#2340;&#2376;&#2351;&#2366;&#2352;&#2367;&#2351;&#2366;&#2306; &#2325;&#2352;&#2344;&#2375; &#2325;&#2366; &#2344;&#2367;&#2352;&#2381;&#2342;&#2375;&#2358; &#2342;&#2367;&#2351;&#2366;&#2404; &#2327;&#2344;&#2381;&#2344;&#2357;&#2352;&#2350; &#2361;&#2357;&#2366;&#2312; &#2309;&#2337;&#2381;&#2337;&#2375; &#2346;&#2352; &#2325;&#2312; &#2357;&#2368;&#2357;&#2368;&#2310;&#2312;&#2346;&#2368; &#2325;&#2375; &#2346;&#2361;&#2369;&#2306;&#2330;&#2344;&#2375; &#2325;&#2368; &#2313;&#2350;&#2381;&#2350;&#2368;&#2342; &#2361;&#2376;&#2404; &#2358;&#2346;&#2341; &#2327;&#2381;&#2352;&#2361;&#2339; &#2360;&#2350;&#2366;&#2352;&#2379;&#2361; &#2325;&#2375; &#2354;&#2367;&#2319; &#2357;&#2352;&#2367;&#2359;&#2381;&#2336; &#2310;&#2312;&#2319;&#2319;&#2360; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352;&#2368; &#2346;&#2368;&#2319;&#2360; &#2346;&#2342;&#2381;&#2351;&#2369;&#2350;&#2381;&#2344; &#2325;&#2379; &#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2350;&#2344;&#2381;&#2357;&#2351;&#2325; &#2344;&#2367;&#2351;&#2369;&#2325;&#2381;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404;\r\n\r\n&#2325;&#2375;&#2306;&#2342;&#2381;&#2352; &#2350;&#2375;&#2306; &#2349;&#2368; &#2350;&#2332;&#2348;&#2370;&#2340; &#2360;&#2381;&#2340;&#2306;&#2349; &#2348;&#2344;&#2325;&#2352; &#2313;&#2349;&#2352;&#2375;\r\n\r\n \r\n\r\n&#2327;&#2380;&#2352;&#2340;&#2354;&#2348; &#2361;&#2376; &#2325;&#2367; &#2344;&#2366;&#2351;&#2337;&#2370; &#2346;&#2352;&#2367;&#2357;&#2366;&#2352; &#2324;&#2352; &#2330;&#2306;&#2342;&#2381;&#2352;&#2348;&#2366;&#2348;&#2370; &#2344;&#2366;&#2351;&#2337;&#2370; &#2325;&#2368; &#2346;&#2340;&#2381;&#2344;&#2368; &#2325;&#2368; &#2360;&#2306;&#2346;&#2340;&#2381;&#2340;&#2367; &#2350;&#2375;&#2306; &#2319;&#2325; &#2340;&#2352;&#2347; &#2351;&#2375; &#2311;&#2332;&#2366;&#2347;&#2366; &#2361;&#2379; &#2352;&#2361;&#2366; &#2361;&#2376;&#2404; &#2357;&#2361;&#2368;&#2306; &#2342;&#2370;&#2360;&#2352;&#2368; &#2323;&#2352; 12 &#2332;&#2370;&#2344; &#2325;&#2379; &#2330;&#2306;&#2342;&#2381;&#2352;&#2348;&#2366;&#2348;&#2370; &#2344;&#2366;&#2351;&#2337;&#2370; &#2310;&#2306;&#2343;&#2381;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358; &#2325;&#2375; &#2344;&#2319; &#2360;&#2368;&#2319;&#2350; &#2348;&#2344;&#2375;&#2306;&#2327;&#2375;&#2404; &#2310;&#2306;&#2343;&#2381;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358; &#2357;&#2367;&#2343;&#2366;&#2344;&#2360;&#2349;&#2366; &#2330;&#2369;&#2344;&#2366;&#2357; &#2346;&#2352;&#2367;&#2339;&#2366;&#2350; &#2350;&#2375;&#2306; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2344;&#2375; &#2360;&#2348;&#2360;&#2375; &#2309;&#2343;&#2367;&#2325; &#2360;&#2368;&#2335;&#2375;&#2306; &#2332;&#2368;&#2340;&#2368; &#2361;&#2376;&#2306;&#2404; 135 &#2360;&#2368;&#2335;&#2375;&#2306; &#2332;&#2368;&#2340; &#2325;&#2352; &#2335;&#2368;&#2337;&#2368;&#2346;&#2368; &#2344;&#2375; &#2348;&#2361;&#2369;&#2350;&#2340; &#2325;&#2375; &#2310;&#2306;&#2325;&#2337;&#2364;&#2375; &#2325;&#2379; &#2346;&#2366;&#2352; &#2325;&#2352; &#2354;&#2367;&#2351;&#2366;&#2404;\r\n\r\n&#2325;&#2367;&#2306;&#2327; &#2350;&#2375;&#2325;&#2352; &#2325;&#2368; &#2349;&#2370;&#2350;&#2367;&#2325;&#2366; &#2350;&#2375;&#2306; &#2361;&#2376;&#2306; &#2330;&#2306;&#2342;&#2381;&#2352;&#2348;&#2366;&#2348;&#2370; &#2344;&#2366;&#2351;&#2337;&#2370;\r\n\r\n&#2330;&#2306;&#2342;&#2381;&#2352;&#2348;&#2366;&#2348;&#2370; &#2344;&#2366;&#2351;&#2337;&#2370; &#2319;&#2344;&#2337;&#2368;&#2319; &#2327;&#2336;&#2348;&#2306;&#2343;&#2344; &#2325;&#2375; &#2354;&#2367;&#2319; &#2319;&#2325; &#2325;&#2367;&#2306;&#2327;&#2350;&#2375;&#2325;&#2352;&#2381;&#2360; &#2350;&#2375;&#2306; &#2360;&#2375; &#2319;&#2325; &#2348;&#2344;&#2325;&#2352; &#2360;&#2366;&#2350;&#2344;&#2375; &#2310;&#2319; &#2361;&#2376;&#2306;&#2404; &#2319;&#2344;&#2337;&#2368;&#2319; &#2344;&#2375; &#2354;&#2327;&#2366;&#2340;&#2366;&#2352; &#2340;&#2368;&#2360;&#2352;&#2368; &#2348;&#2366;&#2352; &#2348;&#2361;&#2369;&#2350;&#2340; &#2346;&#2366;&#2344;&#2375; &#2325;&#2366; &#2352;&#2367;&#2325;&#2377;&#2352;&#2381;&#2337; &#2348;&#2344;&#2351;&#2366; &#2361;&#2376;&#2404; &#2330;&#2306;&#2342;&#2381;&#2352;&#2348;&#2366;&#2348;&#2370; &#2344;&#2366;&#2351;&#2337;&#2370; &#2324;&#2352; &#2344;&#2368;&#2340;&#2368;&#2358; &#2325;&#2369;&#2350;&#2366;&#2352; &#2325;&#2368; &#2346;&#2366;&#2352;&#2381;&#2335;&#2368; &#2325;&#2366; &#2327;&#2336;&#2348;&#2306;&#2343;&#2344; &#2325;&#2368; &#2360;&#2352;&#2325;&#2366;&#2352; &#2348;&#2344;&#2366;&#2344;&#2375; &#2350;&#2375;&#2306; &#2309;&#2361;&#2350; &#2351;&#2379;&#2327;&#2342;&#2366;&#2344; &#2352;&#2361;&#2344;&#2375; &#2357;&#2366;&#2354;&#2366; &#2361;&#2376;&#2404; &#2349;&#2366;&#2332;&#2346;&#2366; &#2311;&#2360; &#2348;&#2366;&#2352; &#2354;&#2379;&#2325;&#2360;&#2349;&#2366; &#2330;&#2369;&#2344;&#2366;&#2357; &#2350;&#2375;&#2306; &#2309;&#2325;&#2375;&#2354;&#2375; &#2342;&#2350; &#2346;&#2352; &#2348;&#2361;&#2369;&#2350;&#2340; &#2354;&#2366;&#2344;&#2375; &#2350;&#2375;&#2306; &#2360;&#2347;&#2354; &#2344;&#2361;&#2368;&#2306; &#2361;&#2379; &#2360;&#2325;&#2368; &#2361;&#2376;&#2404; &#2357;&#2361;&#2368;&#2306; &#2325;&#2366;&#2306;&#2327;&#2381;&#2352;&#2375;&#2360; &#2325;&#2375; &#2344;&#2375;&#2340;&#2371;&#2340;&#2381;&#2357; &#2357;&#2366;&#2354;&#2375; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2327;&#2336;&#2348;&#2306;&#2343;&#2344; &#2344;&#2375; &#2311;&#2360; &#2348;&#2366;&#2352; &#2330;&#2369;&#2344;&#2366;&#2357; &#2350;&#2375;&#2306; &#2348;&#2375;&#2361;&#2340;&#2352;&#2368;&#2344; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2367;&#2351;&#2366; &#2361;&#2376;&#2404;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>string</var> (36) \"1750753523_61bb0e8fbeab52801378.jpeg\"<div class=\"access-path\">$value[0]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['author_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (9) \"published\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>featured</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[0]['featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>views</dfn> =&gt; <var>string</var> (2) \"14\"<div class=\"access-path\">$value[0]['views']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>published_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 08:25:23\"<div class=\"access-path\">$value[0]['published_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 08:25:23\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 08:43:50\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>UTF-8 string</var> (21) \"&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;\"<div class=\"access-path\">$value[0]['category_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_name</dfn> =&gt; <var>string</var> (13) \"Administrator\"<div class=\"access-path\">$value[0]['author_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (90) \"&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367; &#2325;&#2366; &#2344;&#2351;&#2366; &#2342;&#2380;&#2352;\"<div class=\"access-path\">$value[1]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (41) \"bharata-ma-technology-karata-ka-naya-dara\"<div class=\"access-path\">$value[1]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (216) \"&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2361;&#2379; &#2352;&#2361;&#2375; &#2344;&#2357;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2368; &#2360;&#2347;&#2354;&#2340;&#2366; &#2325;&#2368; ...<div class=\"access-path\">$value[1]['description']</div></dt><dd><pre>&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2361;&#2379; &#2352;&#2361;&#2375; &#2344;&#2357;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2368; &#2360;&#2347;&#2354;&#2340;&#2366; &#2325;&#2368; &#2325;&#2361;&#2366;&#2344;&#2368;&#2404;\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>content</dfn> =&gt; <var>UTF-8 string</var> (930) \"&#2349;&#2366;&#2352;&#2340; &#2310;&#2332; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367; &#2325;&#2375; &#2319;&#2325; &#2344;&#2319; &#2342;&#2380;&#2352; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2357;&#2375;&#2358; &#2325;&#2352; &#2352;&#2361;&#2366; &#2361;&#2376;&#2404; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344;...<div class=\"access-path\">$value[1]['content']</div></dt><dd><pre>&#2349;&#2366;&#2352;&#2340; &#2310;&#2332; &#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367; &#2325;&#2375; &#2319;&#2325; &#2344;&#2319; &#2342;&#2380;&#2352; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2357;&#2375;&#2358; &#2325;&#2352; &#2352;&#2361;&#2366; &#2361;&#2376;&#2404; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2375; &#2340;&#2361;&#2340; &#2342;&#2375;&#2358; &#2349;&#2352; &#2350;&#2375;&#2306; &#2311;&#2306;&#2335;&#2352;&#2344;&#2375;&#2335; &#2325;&#2344;&#2375;&#2325;&#2381;&#2335;&#2367;&#2357;&#2367;&#2335;&#2368;, &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2360;&#2366;&#2325;&#2381;&#2359;&#2352;&#2340;&#2366; &#2324;&#2352; &#2312;-&#2327;&#2357;&#2352;&#2381;&#2344;&#2375;&#2306;&#2360; &#2325;&#2375; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2313;&#2354;&#2381;&#2354;&#2375;&#2326;&#2344;&#2368;&#2351; &#2346;&#2381;&#2352;&#2327;&#2340;&#2367; &#2361;&#2369;&#2312; &#2361;&#2376;&#2404; &#2360;&#2381;&#2335;&#2366;&#2352;&#2381;&#2335;&#2309;&#2346; &#2311;&#2325;&#2379;&#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2342;&#2369;&#2344;&#2367;&#2351;&#2366; &#2325;&#2366; &#2340;&#2368;&#2360;&#2352;&#2366; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2366; &#2342;&#2375;&#2358; &#2348;&#2344; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404; &#2347;&#2367;&#2344;&#2335;&#2375;&#2325;, &#2319;&#2337;&#2335;&#2375;&#2325;, &#2361;&#2375;&#2354;&#2381;&#2341;&#2335;&#2375;&#2325; &#2332;&#2376;&#2360;&#2375; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2379;&#2306; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2306;&#2346;&#2344;&#2367;&#2351;&#2366;&#2306; &#2357;&#2376;&#2358;&#2381;&#2357;&#2367;&#2325; &#2360;&#2381;&#2340;&#2352; &#2346;&#2352; &#2309;&#2346;&#2344;&#2368; &#2346;&#2361;&#2330;&#2366;&#2344; &#2348;&#2344;&#2366; &#2352;&#2361;&#2368; &#2361;&#2376;&#2306;&#2404;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[1]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['author_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (9) \"published\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>views</dfn> =&gt; <var>string</var> (4) \"1254\"<div class=\"access-path\">$value[1]['views']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>published_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 06:19:54\"<div class=\"access-path\">$value[1]['published_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 06:19:54\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 08:43:50\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>UTF-8 string</var> (15) \"&#2340;&#2325;&#2344;&#2368;&#2325;\"<div class=\"access-path\">$value[1]['category_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_name</dfn> =&gt; <var>string</var> (13) \"Administrator\"<div class=\"access-path\">$value[1]['author_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (103) \"&#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2332;&#2368;&#2340;\"<div class=\"access-path\">$value[2]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (50) \"karakata-vashava-kapa-ma-bharata-ka-shanadara-jata\"<div class=\"access-path\">$value[2]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (225) \"&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;&#2366;&#2348; &#2309;&#2346;&#2344;&#2375; ...<div class=\"access-path\">$value[2]['description']</div></dt><dd><pre>&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;&#2366;&#2348; &#2309;&#2346;&#2344;&#2375; &#2344;&#2366;&#2350; &#2325;&#2367;&#2351;&#2366;&#2404;\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>content</dfn> =&gt; <var>UTF-8 string</var> (853) \"&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2310;&#2332; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2376;&#2330; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;&#2366;...<div class=\"access-path\">$value[2]['content']</div></dt><dd><pre>&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2310;&#2332; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2376;&#2330; &#2350;&#2375;&#2306; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2352;&#2325;&#2375; &#2326;&#2367;&#2340;&#2366;&#2348; &#2332;&#2368;&#2340;&#2366; &#2361;&#2376;&#2404; &#2325;&#2346;&#2381;&#2340;&#2366;&#2344; &#2325;&#2368; &#2348;&#2375;&#2361;&#2340;&#2352;&#2368;&#2344; &#2325;&#2346;&#2381;&#2340;&#2366;&#2344;&#2368; &#2324;&#2352; &#2335;&#2368;&#2350; &#2325;&#2375; &#2360;&#2349;&#2368; &#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2367;&#2351;&#2379;&#2306; &#2325;&#2375; &#2313;&#2340;&#2381;&#2325;&#2371;&#2359;&#2381;&#2335; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2360;&#2375; &#2351;&#2361; &#2332;&#2368;&#2340; &#2360;&#2306;&#2349;&#2357; &#2361;&#2369;&#2312;&#2404; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2376;&#2330; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2344;&#2375; 350 &#2352;&#2344; &#2325;&#2366; &#2357;&#2367;&#2358;&#2366;&#2354; &#2360;&#2381;&#2325;&#2379;&#2352; &#2348;&#2344;&#2366;&#2351;&#2366; &#2324;&#2352; &#2357;&#2367;&#2346;&#2325;&#2381;&#2359;&#2368; &#2335;&#2368;&#2350; &#2325;&#2379; 280 &#2352;&#2344; &#2346;&#2352; &#2321;&#2354; &#2310;&#2313;&#2335; &#2325;&#2352; &#2342;&#2367;&#2351;&#2366;&#2404; &#2351;&#2361; &#2332;&#2368;&#2340; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2325;&#2375; &#2311;&#2340;&#2367;&#2361;&#2366;&#2360; &#2350;&#2375;&#2306; &#2319;&#2325; &#2344;&#2351;&#2366; &#2309;&#2343;&#2381;&#2351;&#2366;&#2351; &#2332;&#2379;&#2337;&#2364;&#2340;&#2368; &#2361;&#2376;&#2404;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[2]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[2]['author_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (9) \"published\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>views</dfn> =&gt; <var>string</var> (4) \"2104\"<div class=\"access-path\">$value[2]['views']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>published_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 04:19:54\"<div class=\"access-path\">$value[2]['published_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 04:19:54\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 08:43:50\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>UTF-8 string</var> (9) \"&#2326;&#2375;&#2354;\"<div class=\"access-path\">$value[2]['category_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_name</dfn> =&gt; <var>string</var> (12) \"News Manager\"<div class=\"access-path\">$value[2]['author_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (121) \"&#2348;&#2377;&#2354;&#2368;&#2357;&#2369;&#2337; &#2350;&#2375;&#2306; &#2344;&#2312; &#2347;&#2367;&#2354;&#2381;&#2350; &#2325;&#2366; &#2343;&#2350;&#2366;&#2325;&#2375;&#2342;&#2366;&#2352; &#2335;&#2381;&#2352;&#2375;&#2354;&#2352; &#2352;&#2367;&#2354;&#2368;&#2332;\"<div class=\"access-path\">$value[3]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (55) \"balavada-ma-naee-phalama-ka-dhamakadara-taralara-ralaja\"<div class=\"access-path\">$value[3]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (198) \"&#2348;&#2377;&#2354;&#2368;&#2357;&#2369;&#2337; &#2325;&#2368; &#2310;&#2327;&#2366;&#2350;&#2368; &#2348;&#2381;&#2354;&#2377;&#2325;&#2348;&#2360;&#2381;&#2335;&#2352; &#2347;&#2367;&#2354;&#2381;&#2350; &#2325;&#2366; &#2335;&#2381;&#2352;&#2375;&#2354;&#2352; &#2352;&#2367;&#2354;&#2368;&#2332; &#2361;&#2369;&#2310;, &#2342;&#2352;&#2381;&#2358;&#2325;&#2379;&#2306; &#2350;&#2375;&#2306; &#2313;&#2340;&#2381;&#2360;&#2366;&#2361;&#2404;\"<div class=\"access-path\">$value[3]['description']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>content</dfn> =&gt; <var>UTF-8 string</var> (831) \"&#2348;&#2377;&#2354;&#2368;&#2357;&#2369;&#2337; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2346;&#2381;&#2352;&#2340;&#2368;&#2325;&#2381;&#2359;&#2367;&#2340; &#2347;&#2367;&#2354;&#2381;&#2350; &#2325;&#2366; &#2335;&#2381;&#2352;&#2375;&#2354;&#2352; &#2310;&#2332; &#2352;&#2367;&#2354;&#2368;&#2332; &#2361;&#2369;&#2310; &#2361;&#2376;&#2404; &#2311;&#2360; &#2347;&#2367;&#2354;&#2381;&#2350; &#2350;&#2375;&#2306; &#2348;&#2377;&#2354;...<div class=\"access-path\">$value[3]['content']</div></dt><dd><pre>&#2348;&#2377;&#2354;&#2368;&#2357;&#2369;&#2337; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2346;&#2381;&#2352;&#2340;&#2368;&#2325;&#2381;&#2359;&#2367;&#2340; &#2347;&#2367;&#2354;&#2381;&#2350; &#2325;&#2366; &#2335;&#2381;&#2352;&#2375;&#2354;&#2352; &#2310;&#2332; &#2352;&#2367;&#2354;&#2368;&#2332; &#2361;&#2369;&#2310; &#2361;&#2376;&#2404; &#2311;&#2360; &#2347;&#2367;&#2354;&#2381;&#2350; &#2350;&#2375;&#2306; &#2348;&#2377;&#2354;&#2368;&#2357;&#2369;&#2337; &#2325;&#2375; &#2335;&#2377;&#2346; &#2360;&#2381;&#2335;&#2366;&#2352;&#2381;&#2360; &#2344;&#2375; &#2325;&#2366;&#2350; &#2325;&#2367;&#2351;&#2366; &#2361;&#2376;&#2404; &#2335;&#2381;&#2352;&#2375;&#2354;&#2352; &#2350;&#2375;&#2306; &#2342;&#2367;&#2326;&#2366;&#2319; &#2327;&#2319; &#2319;&#2325;&#2381;&#2358;&#2344; &#2360;&#2368;&#2325;&#2381;&#2357;&#2375;&#2306;&#2360; &#2324;&#2352; &#2311;&#2350;&#2379;&#2358;&#2344;&#2354; &#2350;&#2379;&#2350;&#2375;&#2306;&#2335;&#2381;&#2360; &#2344;&#2375; &#2342;&#2352;&#2381;&#2358;&#2325;&#2379;&#2306; &#2325;&#2366; &#2342;&#2367;&#2354; &#2332;&#2368;&#2340; &#2354;&#2367;&#2351;&#2366; &#2361;&#2376;&#2404; &#2347;&#2367;&#2354;&#2381;&#2350; &#2309;&#2327;&#2354;&#2375; &#2350;&#2361;&#2368;&#2344;&#2375; &#2360;&#2367;&#2344;&#2375;&#2350;&#2366;&#2328;&#2352;&#2379;&#2306; &#2350;&#2375;&#2306; &#2352;&#2367;&#2354;&#2368;&#2332; &#2361;&#2379;&#2327;&#2368;&#2404; &#2344;&#2367;&#2352;&#2381;&#2342;&#2375;&#2358;&#2325; &#2344;&#2375; &#2325;&#2361;&#2366; &#2325;&#2367; &#2351;&#2361; &#2347;&#2367;&#2354;&#2381;&#2350; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2367;&#2344;&#2375;&#2350;&#2366; &#2325;&#2375; &#2354;&#2367;&#2319; &#2319;&#2325; &#2350;&#2368;&#2354; &#2325;&#2366; &#2346;&#2340;&#2381;&#2341;&#2352; &#2360;&#2366;&#2348;&#2367;&#2340; &#2361;&#2379;&#2327;&#2368;&#2404;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[3]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]['author_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (9) \"published\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>featured</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[3]['featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>views</dfn> =&gt; <var>string</var> (3) \"890\"<div class=\"access-path\">$value[3]['views']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>published_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 02:19:54\"<div class=\"access-path\">$value[3]['published_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 02:19:54\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 08:43:50\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>UTF-8 string</var> (21) \"&#2350;&#2344;&#2379;&#2352;&#2306;&#2332;&#2344;\"<div class=\"access-path\">$value[3]['category_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_name</dfn> =&gt; <var>string</var> (13) \"Administrator\"<div class=\"access-path\">$value[3]['author_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[4]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (113) \"&#2360;&#2352;&#2325;&#2366;&#2352; &#2325;&#2368; &#2344;&#2312; &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2344;&#2368;&#2340;&#2367; &#2360;&#2375; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352; &#2350;&#2375;&#2306; &#2340;&#2375;&#2332;&#2368;\"<div class=\"access-path\">$value[4]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (51) \"sarakara-ka-naee-aarathaka-nata-sa-business-ma-taja\"<div class=\"access-path\">$value[4]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (235) \"&#2325;&#2375;&#2306;&#2342;&#2381;&#2352; &#2360;&#2352;&#2325;&#2366;&#2352; &#2325;&#2368; &#2344;&#2312; &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2344;&#2368;&#2340;&#2367; &#2360;&#2375; &#2331;&#2379;&#2335;&#2375; &#2324;&#2352; &#2350;&#2343;&#2381;&#2351;&#2350; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306; &#2325;&#2379; &#2348;&#2337;&#2364;&#2366; &#2347;&#2366;&#2351;&#2342;&#2366; &#2361;&#2379;...<div class=\"access-path\">$value[4]['description']</div></dt><dd><pre>&#2325;&#2375;&#2306;&#2342;&#2381;&#2352; &#2360;&#2352;&#2325;&#2366;&#2352; &#2325;&#2368; &#2344;&#2312; &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2344;&#2368;&#2340;&#2367; &#2360;&#2375; &#2331;&#2379;&#2335;&#2375; &#2324;&#2352; &#2350;&#2343;&#2381;&#2351;&#2350; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306; &#2325;&#2379; &#2348;&#2337;&#2364;&#2366; &#2347;&#2366;&#2351;&#2342;&#2366; &#2361;&#2379;&#2344;&#2375; &#2325;&#2368; &#2313;&#2350;&#2381;&#2350;&#2368;&#2342;&#2404;\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>content</dfn> =&gt; <var>UTF-8 string</var> (797) \"&#2325;&#2375;&#2306;&#2342;&#2381;&#2352; &#2360;&#2352;&#2325;&#2366;&#2352; &#2344;&#2375; &#2310;&#2332; &#2319;&#2325; &#2344;&#2312; &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2344;&#2368;&#2340;&#2367; &#2325;&#2368; &#2328;&#2379;&#2359;&#2339;&#2366; &#2325;&#2368; &#2361;&#2376; &#2332;&#2379; &#2331;&#2379;&#2335;&#2375; &#2324;&#2352; &#2350;&#2343;&#2381;&#2351;&#2350; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;...<div class=\"access-path\">$value[4]['content']</div></dt><dd><pre>&#2325;&#2375;&#2306;&#2342;&#2381;&#2352; &#2360;&#2352;&#2325;&#2366;&#2352; &#2344;&#2375; &#2310;&#2332; &#2319;&#2325; &#2344;&#2312; &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2344;&#2368;&#2340;&#2367; &#2325;&#2368; &#2328;&#2379;&#2359;&#2339;&#2366; &#2325;&#2368; &#2361;&#2376; &#2332;&#2379; &#2331;&#2379;&#2335;&#2375; &#2324;&#2352; &#2350;&#2343;&#2381;&#2351;&#2350; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2348;&#2375;&#2361;&#2342; &#2347;&#2366;&#2351;&#2342;&#2375;&#2350;&#2306;&#2342; &#2360;&#2366;&#2348;&#2367;&#2340; &#2361;&#2379;&#2327;&#2368;&#2404; &#2311;&#2360; &#2344;&#2368;&#2340;&#2367; &#2325;&#2375; &#2340;&#2361;&#2340; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306; &#2325;&#2379; &#2325;&#2350; &#2348;&#2381;&#2351;&#2366;&#2332; &#2342;&#2352; &#2346;&#2352; &#2354;&#2379;&#2344; &#2350;&#2367;&#2354;&#2375;&#2327;&#2366; &#2324;&#2352; &#2335;&#2376;&#2325;&#2381;&#2360; &#2350;&#2375;&#2306; &#2349;&#2368; &#2331;&#2370;&#2335; &#2342;&#2368; &#2332;&#2366;&#2319;&#2327;&#2368;&#2404; &#2309;&#2352;&#2381;&#2341;&#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352;&#2367;&#2351;&#2379;&#2306; &#2325;&#2366; &#2350;&#2366;&#2344;&#2344;&#2366; &#2361;&#2376; &#2325;&#2367; &#2351;&#2361; &#2344;&#2368;&#2340;&#2367; &#2342;&#2375;&#2358; &#2325;&#2368; &#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366; &#2325;&#2379; &#2344;&#2312; &#2342;&#2367;&#2358;&#2366; &#2342;&#2375;&#2327;&#2368; &#2324;&#2352; &#2352;&#2379;&#2332;&#2327;&#2366;&#2352; &#2325;&#2375; &#2344;&#2319; &#2309;&#2357;&#2360;&#2352; &#2346;&#2376;&#2342;&#2366; &#2325;&#2352;&#2375;&#2327;&#2368;&#2404;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[4]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[4]['author_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (9) \"published\"<div class=\"access-path\">$value[4]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>featured</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[4]['featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>views</dfn> =&gt; <var>string</var> (3) \"651\"<div class=\"access-path\">$value[4]['views']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>published_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 00:19:54\"<div class=\"access-path\">$value[4]['published_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 00:19:54\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 08:43:50\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>UTF-8 string</var> (21) \"&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;\"<div class=\"access-path\">$value[4]['category_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_name</dfn> =&gt; <var>string</var> (12) \"News Manager\"<div class=\"access-path\">$value[4]['author_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>5</dfn> =&gt; <var>array</var> (16)<div class=\"access-path\">$value[5]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[5]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (101) \"&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340;\"<div class=\"access-path\">$value[5]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (42) \"rajaya-sabha-ma-important-vadhayaka-parata\"<div class=\"access-path\">$value[5]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (182) \"&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2310;&#2332; &#2319;&#2325; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2332;&#2379; &#2332;&#2344;&#2340;&#2366; &#2325;&#2375; &#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2361;&#2376;&#2404;\"<div class=\"access-path\">$value[5]['description']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>content</dfn> =&gt; <var>UTF-8 string</var> (693) \"&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2310;&#2332; &#2319;&#2325; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2361;&#2376; &#2332;&#2379; &#2310;&#2350; &#2332;&#2344;&#2340;&#2366; &#2325;&#2375; &#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2361;&#2376;&#2404;...<div class=\"access-path\">$value[5]['content']</div></dt><dd><pre>&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2310;&#2332; &#2319;&#2325; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2346;&#2366;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2361;&#2376; &#2332;&#2379; &#2310;&#2350; &#2332;&#2344;&#2340;&#2366; &#2325;&#2375; &#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2361;&#2376;&#2404; &#2311;&#2360; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2360;&#2375; &#2358;&#2367;&#2325;&#2381;&#2359;&#2366;, &#2360;&#2381;&#2357;&#2366;&#2360;&#2381;&#2341;&#2381;&#2351; &#2324;&#2352; &#2352;&#2379;&#2332;&#2327;&#2366;&#2352; &#2325;&#2375; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2360;&#2369;&#2343;&#2366;&#2352; &#2361;&#2379;&#2327;&#2366;&#2404; &#2360;&#2349;&#2368; &#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;&#2325; &#2342;&#2354;&#2379;&#2306; &#2344;&#2375; &#2311;&#2360; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2325;&#2366; &#2360;&#2350;&#2352;&#2381;&#2341;&#2344; &#2325;&#2367;&#2351;&#2366; &#2361;&#2376;&#2404; &#2357;&#2367;&#2346;&#2325;&#2381;&#2359; &#2344;&#2375; &#2349;&#2368; &#2311;&#2360;&#2375; &#2332;&#2344;&#2361;&#2367;&#2340; &#2350;&#2375;&#2306; &#2348;&#2340;&#2366;&#2351;&#2366; &#2361;&#2376;&#2404; &#2309;&#2348; &#2351;&#2361; &#2357;&#2367;&#2343;&#2375;&#2351;&#2325; &#2354;&#2379;&#2325;&#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2349;&#2375;&#2332;&#2366; &#2332;&#2366;&#2319;&#2327;&#2366;&#2404;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[5]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]['category_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]['author_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (9) \"published\"<div class=\"access-path\">$value[5]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>featured</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]['featured']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>views</dfn> =&gt; <var>string</var> (4) \"1800\"<div class=\"access-path\">$value[5]['views']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>published_at</dfn> =&gt; <var>string</var> (19) \"2025-06-23 22:19:54\"<div class=\"access-path\">$value[5]['published_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-23 22:19:54\"<div class=\"access-path\">$value[5]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 08:43:50\"<div class=\"access-path\">$value[5]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>category_name</dfn> =&gt; <var>UTF-8 string</var> (21) \"&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;\"<div class=\"access-path\">$value[5]['category_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>author_name</dfn> =&gt; <var>string</var> (13) \"Administrator\"<div class=\"access-path\">$value[5]['author_name']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "categories": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (7)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (7)</li><li>Contents (7)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>name</th><th>slug</th><th>description</th><th>status</th><th>created_at</th><th>updated_at</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">1</td><td title=\"UTF-8 string (21)\">&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;</td><td title=\"string (8)\">politics</td><td title=\"UTF-8 string (66)\">&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;&#2325; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2309;&#2346;&#2337;&#2375;&#2335;</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-06-24 07:33:11</td><td title=\"string (19)\">2025-06-24 07:33:11</td></tr><tr><th>1</th><td title=\"string (1)\">2</td><td title=\"UTF-8 string (9)\">&#2326;&#2375;&#2354;</td><td title=\"string (6)\">sports</td><td title=\"UTF-8 string (55)\">&#2326;&#2375;&#2354; &#2332;&#2327;&#2340; &#2325;&#2368; &#2340;&#2366;&#2332;&#2366; &#2326;&#2348;&#2352;&#2375;&#2306;</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-06-24 07:33:11</td><td title=\"string (19)\">2025-06-24 07:33:11</td></tr><tr><th>2</th><td title=\"string (1)\">3</td><td title=\"UTF-8 string (21)\">&#2350;&#2344;&#2379;&#2352;&#2306;&#2332;&#2344;</td><td title=\"string (13)\">entertainment</td><td title=\"UTF-8 string (76)\">&#2348;&#2377;&#2354;&#2368;&#2357;&#2369;&#2337; &#2324;&#2352; &#2350;&#2344;&#2379;&#2352;&#2306;&#2332;&#2344; &#2325;&#2368; &#2342;&#2369;&#2344;&#2367;&#2351;&#2366;</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-06-24 07:33:11</td><td title=\"string (19)\">2025-06-24 07:33:11</td></tr><tr><th>3</th><td title=\"string (1)\">4</td><td title=\"UTF-8 string (15)\">&#2340;&#2325;&#2344;&#2368;&#2325;</td><td title=\"string (10)\">technology</td><td title=\"UTF-8 string (63)\">&#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2344;&#2357;&#2366;&#2330;&#2366;&#2352;</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-06-24 07:33:11</td><td title=\"string (19)\">2025-06-24 07:33:11</td></tr><tr><th>4</th><td title=\"string (1)\">5</td><td title=\"UTF-8 string (21)\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;</td><td title=\"string (8)\">business</td><td title=\"UTF-8 string (88)\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352; &#2324;&#2352; &#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366; &#2325;&#2368; &#2326;&#2348;&#2352;&#2375;&#2306;</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-06-24 07:33:11</td><td title=\"string (19)\">2025-06-24 07:33:11</td></tr><tr><th>5</th><td title=\"string (1)\">6</td><td title=\"UTF-8 string (16)\">&#2309;&#2346;&#2352;&#2366;&#2343; </td><td title=\"string (6)\">-crime</td><td title=\"UTF-8 string (34)\">&#2309;&#2346;&#2352;&#2366;&#2343; &#2344;&#2381;&#2351;&#2370;&#2332;&#2364;</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-06-24 09:21:36</td><td title=\"string (19)\">2025-06-24 09:21:36</td></tr><tr><th>6</th><td title=\"string (1)\">7</td><td title=\"UTF-8 string (18)\">&#2358;&#2367;&#2325;&#2381;&#2359;&#2366;</td><td title=\"string (9)\">education</td><td title=\"UTF-8 string (23)\">&#2358;&#2367;&#2325;&#2381;&#2359;&#2366; news</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-06-24 09:34:13</td><td title=\"string (19)\">2025-06-24 09:34:13</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>UTF-8 string</var> (21) \"&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;\"<div class=\"access-path\">$value[0]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (8) \"politics\"<div class=\"access-path\">$value[0]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (66) \"&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;&#2325; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2309;&#2346;&#2337;&#2375;&#2335;\"<div class=\"access-path\">$value[0]['description']</div></dt><dd><pre>&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;&#2325; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2309;&#2346;&#2337;&#2375;&#2335;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 07:33:11\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 07:33:11\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>UTF-8 string</var> (9) \"&#2326;&#2375;&#2354;\"<div class=\"access-path\">$value[1]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (6) \"sports\"<div class=\"access-path\">$value[1]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (55) \"&#2326;&#2375;&#2354; &#2332;&#2327;&#2340; &#2325;&#2368; &#2340;&#2366;&#2332;&#2366; &#2326;&#2348;&#2352;&#2375;&#2306;\"<div class=\"access-path\">$value[1]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 07:33:11\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 07:33:11\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>UTF-8 string</var> (21) \"&#2350;&#2344;&#2379;&#2352;&#2306;&#2332;&#2344;\"<div class=\"access-path\">$value[2]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (13) \"entertainment\"<div class=\"access-path\">$value[2]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (76) \"&#2348;&#2377;&#2354;&#2368;&#2357;&#2369;&#2337; &#2324;&#2352; &#2350;&#2344;&#2379;&#2352;&#2306;&#2332;&#2344; &#2325;&#2368; &#2342;&#2369;&#2344;&#2367;&#2351;&#2366;\"<div class=\"access-path\">$value[2]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 07:33:11\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 07:33:11\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>UTF-8 string</var> (15) \"&#2340;&#2325;&#2344;&#2368;&#2325;\"<div class=\"access-path\">$value[3]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (10) \"technology\"<div class=\"access-path\">$value[3]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (63) \"&#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352; &#2324;&#2352; &#2344;&#2357;&#2366;&#2330;&#2366;&#2352;\"<div class=\"access-path\">$value[3]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 07:33:11\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 07:33:11\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[4]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>UTF-8 string</var> (21) \"&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;\"<div class=\"access-path\">$value[4]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (8) \"business\"<div class=\"access-path\">$value[4]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (88) \"&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352; &#2324;&#2352; &#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366; &#2325;&#2368; &#2326;&#2348;&#2352;&#2375;&#2306;\"<div class=\"access-path\">$value[4]['description']</div></dt><dd><pre>&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352; &#2324;&#2352; &#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366; &#2325;&#2368; &#2326;&#2348;&#2352;&#2375;&#2306;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[4]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 07:33:11\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 07:33:11\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>5</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[5]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[5]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>name</dfn> =&gt; <var>UTF-8 string</var> (16) \"&#2309;&#2346;&#2352;&#2366;&#2343; \"<div class=\"access-path\">$value[5]['name']</div></dt><dd><pre>&#2309;&#2346;&#2352;&#2366;&#2343; \n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (6) \"-crime\"<div class=\"access-path\">$value[5]['slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (34) \"&#2309;&#2346;&#2352;&#2366;&#2343; &#2344;&#2381;&#2351;&#2370;&#2332;&#2364;\"<div class=\"access-path\">$value[5]['description']</div></dt><dd><pre>&#2309;&#2346;&#2352;&#2366;&#2343; &#2344;&#2381;&#2351;&#2370;&#2332;&#2364;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[5]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:21:36\"<div class=\"access-path\">$value[5]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:21:36\"<div class=\"access-path\">$value[5]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>6</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[6]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[6]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>UTF-8 string</var> (18) \"&#2358;&#2367;&#2325;&#2381;&#2359;&#2366;\"<div class=\"access-path\">$value[6]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (9) \"education\"<div class=\"access-path\">$value[6]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (23) \"&#2358;&#2367;&#2325;&#2381;&#2359;&#2366; news\"<div class=\"access-path\">$value[6]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[6]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:34:13\"<div class=\"access-path\">$value[6]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:34:13\"<div class=\"access-path\">$value[6]['updated_at']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "breakingNews": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (4)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (4)</li><li>Contents (4)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>title</th><th>content</th><th>link_url</th><th>priority</th><th>status</th><th>start_time</th><th>end_time</th><th>created_by</th><th>created_at</th><th>updated_at</th><th>created_by_name</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">1</td><td title=\"UTF-8 string (132)\">&#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2350;&#2379;&#2342;&#2368; &#2344;&#2375; &#2310;&#2332; &#2344;&#2312; &#2358;&#2367;&#2325;&#2381;&#2359;&#2366; &#2344;&#2368;&#2340;&#2367; &#2325;&#2368; &#2328;&#2379;&#2359;&#2339;&#2366; &#2325;&#2368;</td><td title=\"UTF-8 string (182)\">&#2344;&#2312; &#2358;&#2367;&#2325;&#2381;&#2359;&#2366; &#2344;&#2368;&#2340;&#2367; &#2360;&#2375; &#2342;&#2375;&#2358; &#2325;&#2368; &#2358;&#2367;&#2325;&#2381;&#2359;&#2366; &#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366; &#2350;&#2375;&#2306; &#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367;&#2325;&#2366;&#2352;&#2368; &#2348;&#2342;&#2354;&#2366;&#2357; &#2310;&#2319;&#2327;&#2366;&#2404;</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">4</td><td title=\"string (6)\">active</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">1</td><td title=\"string (19)\">2025-06-24 09:09:54</td><td title=\"string (19)\">2025-06-24 09:09:54</td><td title=\"string (13)\">Administrator</td></tr><tr><th>1</th><td title=\"string (1)\">2</td><td title=\"UTF-8 string (151)\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2332;&#2368;&#2340;&#2366; - &#2342;&#2375;&#2358; &#2350;&#2375;&#2306; &#2332;&#2358;&#2381;&#2344; &#2325;&#2366; &#2350;&#2366;&#2361;&#2380;&#2354;</td><td title=\"UTF-8 string (159)\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2325;&#2368; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2332;&#2368;&#2340; &#2360;&#2375; &#2346;&#2370;&#2352;&#2375; &#2342;&#2375;&#2358; &#2350;&#2375;&#2306; &#2326;&#2369;&#2358;&#2368; &#2325;&#2368; &#2354;&#2361;&#2352;&#2404;</td><td title=\"string (88)\">http://localhost/bbc_news/public/news/karakata-vashava-kapa-ma-bharata-ka-sUTF-8</td><td title=\"string (1)\">3</td><td title=\"string (6)\">active</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">1</td><td title=\"string (19)\">2025-06-24 09:09:54</td><td title=\"string (19)\">2025-06-24 09:09:54</td><td title=\"string (13)\">Administrator</td></tr><tr><th>2</th><td title=\"string (1)\">3</td><td title=\"UTF-8 string (114)\">&#2342;&#2367;&#2354;&#2381;&#2354;&#2368; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2368; &#2348;&#2366;&#2352;&#2367;&#2358; - &#2325;&#2312; &#2311;&#2354;&#2366;&#2325;&#2379;&#2306; &#2350;&#2375;&#2306; &#2332;&#2354;&#2349;&#2352;&#2366;&#2357;</td><td title=\"UTF-8 string (172)\">&#2350;&#2380;&#2360;&#2350; &#2357;&#2367;&#2349;&#2366;&#2327; &#2344;&#2375; &#2309;&#2327;&#2354;&#2375; 24 &#2328;&#2306;&#2335;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2349;&#2366;&#2352;&#2368; &#2348;&#2366;&#2352;&#2367;&#2358; &#2325;&#2368; &#2330;&#2375;&#2340;&#2366;&#2357;&#2344;&#2368; &#2332;&#2366;&#2352;&#2368; &#2325;&#2368; &#2361;&#2376;&#2404;</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">2</td><td title=\"string (6)\">active</td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-06-26 09:09:54</td><td title=\"string (1)\">1</td><td title=\"string (19)\">2025-06-24 09:09:54</td><td title=\"string (19)\">2025-06-24 09:09:54</td><td title=\"string (13)\">Administrator</td></tr><tr><th>3</th><td title=\"string (1)\">4</td><td title=\"UTF-8 string (102)\">&#2358;&#2375;&#2351;&#2352; &#2348;&#2366;&#2332;&#2366;&#2352; &#2350;&#2375;&#2306; &#2340;&#2375;&#2332;&#2368; - &#2360;&#2375;&#2306;&#2360;&#2375;&#2325;&#2381;&#2360; 500 &#2309;&#2306;&#2325; &#2314;&#2346;&#2352;</td><td title=\"UTF-8 string (171)\">&#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2360;&#2369;&#2343;&#2366;&#2352;&#2379;&#2306; &#2325;&#2368; &#2313;&#2350;&#2381;&#2350;&#2368;&#2342;&#2379;&#2306; &#2360;&#2375; &#2358;&#2375;&#2351;&#2352; &#2348;&#2366;&#2332;&#2366;&#2352; &#2350;&#2375;&#2306; &#2340;&#2375;&#2332;&#2368; &#2342;&#2375;&#2326;&#2368; &#2332;&#2366; &#2352;&#2361;&#2368; &#2361;&#2376;&#2404;</td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">1</td><td title=\"string (6)\">active</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-06-24 09:09:54</td><td title=\"string (19)\">2025-06-24 09:09:54</td><td title=\"string (12)\">News Manager</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (132) \"&#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2350;&#2379;&#2342;&#2368; &#2344;&#2375; &#2310;&#2332; &#2344;&#2312; &#2358;&#2367;&#2325;&#2381;&#2359;&#2366; &#2344;&#2368;&#2340;&#2367; &#2325;&#2368; &#2328;&#2379;&#2359;&#2339;&#2366; &#2325;&#2368;\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>content</dfn> =&gt; <var>UTF-8 string</var> (182) \"&#2344;&#2312; &#2358;&#2367;&#2325;&#2381;&#2359;&#2366; &#2344;&#2368;&#2340;&#2367; &#2360;&#2375; &#2342;&#2375;&#2358; &#2325;&#2368; &#2358;&#2367;&#2325;&#2381;&#2359;&#2366; &#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366; &#2350;&#2375;&#2306; &#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367;&#2325;&#2366;&#2352;&#2368; &#2348;&#2342;&#2354;&#2366;&#2357; &#2310;&#2319;&#2327;&#2366;&#2404;\"<div class=\"access-path\">$value[0]['content']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>link_url</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['link_url']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>priority</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[0]['priority']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_by</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['created_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:09:54\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:09:54\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_by_name</dfn> =&gt; <var>string</var> (13) \"Administrator\"<div class=\"access-path\">$value[0]['created_by_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (151) \"&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2344;&#2375; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2332;&#2368;&#2340;&#2366; - &#2342;&#2375;&#2358; &#2350;&#2375;&#2306; &#2332;&#2358;&#2381;&#2344; &#2325;&#2366; &#2350;&#2366;&#2361;&#2380;&#2354;\"<div class=\"access-path\">$value[1]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>content</dfn> =&gt; <var>UTF-8 string</var> (159) \"&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2335;&#2368;&#2350; &#2325;&#2368; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2332;&#2368;&#2340; &#2360;&#2375; &#2346;&#2370;&#2352;&#2375; &#2342;&#2375;&#2358; &#2350;&#2375;&#2306; &#2326;&#2369;&#2358;&#2368; &#2325;&#2368; &#2354;&#2361;&#2352;&#2404;\"<div class=\"access-path\">$value[1]['content']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>link_url</dfn> =&gt; <var>string</var> (88) \"http://localhost/bbc_news/public/news/karakata-vashava-kapa-ma-bharata-ka-sh...<div class=\"access-path\">$value[1]['link_url']</div></dt><dd><pre>http://localhost/bbc_news/public/news/karakata-vashava-kapa-ma-bharata-ka-shanadara-jata\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>priority</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[1]['priority']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_by</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['created_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:09:54\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:09:54\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_by_name</dfn> =&gt; <var>string</var> (13) \"Administrator\"<div class=\"access-path\">$value[1]['created_by_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (114) \"&#2342;&#2367;&#2354;&#2381;&#2354;&#2368; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2368; &#2348;&#2366;&#2352;&#2367;&#2358; - &#2325;&#2312; &#2311;&#2354;&#2366;&#2325;&#2379;&#2306; &#2350;&#2375;&#2306; &#2332;&#2354;&#2349;&#2352;&#2366;&#2357;\"<div class=\"access-path\">$value[2]['title']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>content</dfn> =&gt; <var>UTF-8 string</var> (172) \"&#2350;&#2380;&#2360;&#2350; &#2357;&#2367;&#2349;&#2366;&#2327; &#2344;&#2375; &#2309;&#2327;&#2354;&#2375; 24 &#2328;&#2306;&#2335;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2349;&#2366;&#2352;&#2368; &#2348;&#2366;&#2352;&#2367;&#2358; &#2325;&#2368; &#2330;&#2375;&#2340;&#2366;&#2357;&#2344;&#2368; &#2332;&#2366;&#2352;&#2368; &#2325;&#2368; &#2361;&#2376;&#2404;\"<div class=\"access-path\">$value[2]['content']</div></dt><dd><pre>&#2350;&#2380;&#2360;&#2350; &#2357;&#2367;&#2349;&#2366;&#2327; &#2344;&#2375; &#2309;&#2327;&#2354;&#2375; 24 &#2328;&#2306;&#2335;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2349;&#2366;&#2352;&#2368; &#2348;&#2366;&#2352;&#2367;&#2358; &#2325;&#2368; &#2330;&#2375;&#2340;&#2366;&#2357;&#2344;&#2368; &#2332;&#2366;&#2352;&#2368; &#2325;&#2368; &#2361;&#2376;&#2404;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>link_url</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['link_url']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>priority</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[2]['priority']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>string</var> (19) \"2025-06-26 09:09:54\"<div class=\"access-path\">$value[2]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_by</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['created_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:09:54\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:09:54\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_by_name</dfn> =&gt; <var>string</var> (13) \"Administrator\"<div class=\"access-path\">$value[2]['created_by_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (102) \"&#2358;&#2375;&#2351;&#2352; &#2348;&#2366;&#2332;&#2366;&#2352; &#2350;&#2375;&#2306; &#2340;&#2375;&#2332;&#2368; - &#2360;&#2375;&#2306;&#2360;&#2375;&#2325;&#2381;&#2360; 500 &#2309;&#2306;&#2325; &#2314;&#2346;&#2352;\"<div class=\"access-path\">$value[3]['title']</div></dt><dd><pre>&#2358;&#2375;&#2351;&#2352; &#2348;&#2366;&#2332;&#2366;&#2352; &#2350;&#2375;&#2306; &#2340;&#2375;&#2332;&#2368; - &#2360;&#2375;&#2306;&#2360;&#2375;&#2325;&#2381;&#2360; 500 &#2309;&#2306;&#2325; &#2314;&#2346;&#2352;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>content</dfn> =&gt; <var>UTF-8 string</var> (171) \"&#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2360;&#2369;&#2343;&#2366;&#2352;&#2379;&#2306; &#2325;&#2368; &#2313;&#2350;&#2381;&#2350;&#2368;&#2342;&#2379;&#2306; &#2360;&#2375; &#2358;&#2375;&#2351;&#2352; &#2348;&#2366;&#2332;&#2366;&#2352; &#2350;&#2375;&#2306; &#2340;&#2375;&#2332;&#2368; &#2342;&#2375;&#2326;&#2368; &#2332;&#2366; &#2352;&#2361;&#2368; &#2361;&#2376;&#2404;\"<div class=\"access-path\">$value[3]['content']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>link_url</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['link_url']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>priority</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]['priority']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_time</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['start_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_time</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['end_time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_by</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[3]['created_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:09:54\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:09:54\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_by_name</dfn> =&gt; <var>string</var> (12) \"News Manager\"<div class=\"access-path\">$value[3]['created_by_name']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "headerAds": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (85) \"&#2346;&#2381;&#2352;&#2368;&#2350;&#2367;&#2351;&#2350; &#2344;&#2381;&#2351;&#2370;&#2332;&#2364; &#2320;&#2346; &#2337;&#2366;&#2313;&#2344;&#2354;&#2379;&#2337; &#2325;&#2352;&#2375;&#2306;\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (190) \"&#2360;&#2348;&#2360;&#2375; &#2340;&#2375;&#2332;&#2364; &#2324;&#2352; &#2357;&#2367;&#2358;&#2381;&#2357;&#2360;&#2344;&#2368;&#2351; &#2344;&#2381;&#2351;&#2370;&#2332;&#2364; &#2320;&#2346;&#2404; &#2309;&#2349;&#2368; &#2337;&#2366;&#2313;&#2344;&#2354;&#2379;&#2337; &#2325;&#2352;&#2375;&#2306; &#2324;&#2352; &#8377;100 &#2325;&#2366; &#2325;&#2376;&#2358;&#2348;&#2376;&#2325; &#2346;&#2366;&#2319;&#2306;&#2404;\"<div class=\"access-path\">$value[0]['description']</div></dt><dd><pre>&#2360;&#2348;&#2360;&#2375; &#2340;&#2375;&#2332;&#2364; &#2324;&#2352; &#2357;&#2367;&#2358;&#2381;&#2357;&#2360;&#2344;&#2368;&#2351; &#2344;&#2381;&#2351;&#2370;&#2332;&#2364; &#2320;&#2346;&#2404; &#2309;&#2349;&#2368; &#2337;&#2366;&#2313;&#2344;&#2354;&#2379;&#2337; &#2325;&#2352;&#2375;&#2306; &#2324;&#2352; &#8377;100 &#2325;&#2366; &#2325;&#2376;&#2358;&#2348;&#2376;&#2325; &#2346;&#2366;&#2319;&#2306;&#2404;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>link_url</dfn> =&gt; <var>string</var> (29) \"https://play.google.com/store\"<div class=\"access-path\">$value[0]['link_url']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>position</dfn> =&gt; <var>string</var> (6) \"header\"<div class=\"access-path\">$value[0]['position']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (4) \"text\"<div class=\"access-path\">$value[0]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>clicks</dfn> =&gt; <var>string</var> (2) \"45\"<div class=\"access-path\">$value[0]['clicks']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>impressions</dfn> =&gt; <var>string</var> (4) \"1200\"<div class=\"access-path\">$value[0]['impressions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:15:40\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:15:40\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl></dd></dl></dd></dl></div>", "sidebarAds": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (68) \"&#2321;&#2344;&#2354;&#2366;&#2311;&#2344; &#2358;&#2367;&#2325;&#2381;&#2359;&#2366; &#2346;&#2381;&#2354;&#2375;&#2335;&#2347;&#2377;&#2352;&#2381;&#2350;\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (128) \"&#2328;&#2352; &#2348;&#2376;&#2336;&#2375; &#2360;&#2368;&#2326;&#2375;&#2306;&#2404; 1000+ &#2325;&#2379;&#2352;&#2381;&#2360; &#2313;&#2346;&#2354;&#2348;&#2381;&#2343;&#2404; &#2346;&#2361;&#2354;&#2375; &#2350;&#2361;&#2368;&#2344;&#2375; 50% &#2331;&#2370;&#2335;&#2404;\"<div class=\"access-path\">$value[0]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>link_url</dfn> =&gt; <var>string</var> (29) \"https://example.com/education\"<div class=\"access-path\">$value[0]['link_url']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>position</dfn> =&gt; <var>string</var> (7) \"sidebar\"<div class=\"access-path\">$value[0]['position']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (4) \"text\"<div class=\"access-path\">$value[0]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>clicks</dfn> =&gt; <var>string</var> (2) \"23\"<div class=\"access-path\">$value[0]['clicks']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>impressions</dfn> =&gt; <var>string</var> (3) \"958\"<div class=\"access-path\">$value[0]['impressions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:15:40\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:15:40\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl></dd></dl></dd></dl></div>", "footerAds": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (47) \"&#2321;&#2344;&#2354;&#2366;&#2311;&#2344; &#2358;&#2377;&#2346;&#2367;&#2306;&#2327; &#2360;&#2375;&#2354;\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (148) \"&#2350;&#2375;&#2327;&#2366; &#2360;&#2375;&#2354;! 70% &#2340;&#2325; &#2325;&#2368; &#2331;&#2370;&#2335;&#2404; &#2347;&#2381;&#2352;&#2368; &#2361;&#2379;&#2350; &#2337;&#2367;&#2354;&#2368;&#2357;&#2352;&#2368;&#2404; &#2310;&#2332; &#2361;&#2368; &#2321;&#2352;&#2381;&#2337;&#2352; &#2325;&#2352;&#2375;&#2306;&#2404;\"<div class=\"access-path\">$value[0]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>link_url</dfn> =&gt; <var>string</var> (28) \"https://example.com/shopping\"<div class=\"access-path\">$value[0]['link_url']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>position</dfn> =&gt; <var>string</var> (6) \"footer\"<div class=\"access-path\">$value[0]['position']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (4) \"text\"<div class=\"access-path\">$value[0]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>clicks</dfn> =&gt; <var>string</var> (2) \"89\"<div class=\"access-path\">$value[0]['clicks']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>impressions</dfn> =&gt; <var>string</var> (4) \"2100\"<div class=\"access-path\">$value[0]['impressions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:15:40\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:15:40\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl></dd></dl></dd></dl></div>", "betweenNewsAds": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (65) \"&#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2350;&#2366;&#2352;&#2381;&#2325;&#2375;&#2335;&#2367;&#2306;&#2327; &#2325;&#2379;&#2352;&#2381;&#2360;\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (183) \"&#2360;&#2367;&#2352;&#2381;&#2347; 3 &#2350;&#2361;&#2368;&#2344;&#2375; &#2350;&#2375;&#2306; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2350;&#2366;&#2352;&#2381;&#2325;&#2375;&#2335;&#2367;&#2306;&#2327; &#2319;&#2325;&#2381;&#2360;&#2346;&#2352;&#2381;&#2335; &#2348;&#2344;&#2375;&#2306;&#2404; &#2332;&#2377;&#2348; &#2327;&#2366;&#2352;&#2306;&#2335;&#2368; &#2325;&#2375; &#2360;&#2366;&#2341;&#2404;\"<div class=\"access-path\">$value[0]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>link_url</dfn> =&gt; <var>string</var> (37) \"https://example.com/digital-marketing\"<div class=\"access-path\">$value[0]['link_url']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>position</dfn> =&gt; <var>string</var> (12) \"between_news\"<div class=\"access-path\">$value[0]['position']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (4) \"text\"<div class=\"access-path\">$value[0]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>clicks</dfn> =&gt; <var>string</var> (2) \"34\"<div class=\"access-path\">$value[0]['clicks']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>impressions</dfn> =&gt; <var>string</var> (3) \"804\"<div class=\"access-path\">$value[0]['impressions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:15:40\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:15:40\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl></dd></dl></dd></dl></div>", "floatingAds": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>array</var> (0)</dt></dl></div>", "stickyBottomAds": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>array</var> (0)</dt></dl></div>", "ads": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (68) \"&#2321;&#2344;&#2354;&#2366;&#2311;&#2344; &#2358;&#2367;&#2325;&#2381;&#2359;&#2366; &#2346;&#2381;&#2354;&#2375;&#2335;&#2347;&#2377;&#2352;&#2381;&#2350;\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (128) \"&#2328;&#2352; &#2348;&#2376;&#2336;&#2375; &#2360;&#2368;&#2326;&#2375;&#2306;&#2404; 1000+ &#2325;&#2379;&#2352;&#2381;&#2360; &#2313;&#2346;&#2354;&#2348;&#2381;&#2343;&#2404; &#2346;&#2361;&#2354;&#2375; &#2350;&#2361;&#2368;&#2344;&#2375; 50% &#2331;&#2370;&#2335;&#2404;\"<div class=\"access-path\">$value[0]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>image</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['image']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>link_url</dfn> =&gt; <var>string</var> (29) \"https://example.com/education\"<div class=\"access-path\">$value[0]['link_url']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>position</dfn> =&gt; <var>string</var> (7) \"sidebar\"<div class=\"access-path\">$value[0]['position']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (4) \"text\"<div class=\"access-path\">$value[0]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>clicks</dfn> =&gt; <var>string</var> (2) \"23\"<div class=\"access-path\">$value[0]['clicks']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>impressions</dfn> =&gt; <var>string</var> (3) \"958\"<div class=\"access-path\">$value[0]['impressions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:15:40\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:15:40\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl></dd></dl></dd></dl></div>", "position": "sidebar", "showLabel": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>boolean</var> true</dt></dl></div>"}}, "headers": {"Host": "localhost", "Connection": "keep-alive", "Sec-Ch-Ua": "&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Referer": "http://localhost/bbc_news/public/category/politics", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "en-US,en;q=0.9,eu;q=0.8,hi;q=0.7,bho;q=0.6,ne;q=0.5,nl;q=0.4,eo;q=0.3,haw;q=0.2", "Cookie": "csrf_cookie_name=d68cc39e23f3ee587320962fafa53a84; ci_session=be9cfbc2fc9ec9a35787cd2cab7a11e1"}, "cookies": {"csrf_cookie_name": "d68cc39e23f3ee587320962fafa53a84", "ci_session": "be9cfbc2fc9ec9a35787cd2cab7a11e1"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Cache-Control": "no-store, max-age=0, no-cache", "Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.12", "phpSAPI": "apache2handler", "environment": "development", "baseURL": "http://localhost/bbc_news/public/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}