<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\Poll;
use App\Models\PollOption;
use App\Models\PollVote;

class PollController extends BaseController
{
    protected $pollModel;
    protected $pollOptionModel;
    protected $pollVoteModel;

    public function __construct()
    {
        $this->pollModel = new Poll();
        $this->pollOptionModel = new PollOption();
        $this->pollVoteModel = new PollVote();
    }

    public function vote()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->to('/');
        }

        $pollId = $this->request->getPost('poll_id');
        $optionIds = $this->request->getPost('option_ids');

        if (!$pollId || !$optionIds) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid poll data'
            ]);
        }

        // Check if poll is active
        if (!$this->pollModel->isPollActive($pollId)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'This poll is not currently active'
            ]);
        }

        $voterIp = $this->request->getIPAddress();
        $voterSession = session()->session_id;
        $userAgent = $this->request->getUserAgent()->getAgentString();

        // Check if user has already voted
        if ($this->pollVoteModel->hasUserVoted($pollId, $voterIp, $voterSession)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'You have already voted in this poll'
            ]);
        }

        // Get poll details
        $poll = $this->pollModel->find($pollId);
        if (!$poll) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Poll not found'
            ]);
        }

        // Validate option IDs
        if (!is_array($optionIds)) {
            $optionIds = [$optionIds];
        }

        // For single choice polls, only allow one option
        if (!$poll['multiple_choice'] && count($optionIds) > 1) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'This poll allows only one choice'
            ]);
        }

        // Record votes
        $votedOptions = [];
        foreach ($optionIds as $optionId) {
            if ($this->pollVoteModel->recordVote($pollId, $optionId, $voterIp, $voterSession, $userAgent)) {
                $this->pollOptionModel->incrementVoteCount($optionId);
                $votedOptions[] = $optionId;
            }
        }

        if (empty($votedOptions)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to record vote'
            ]);
        }

        // Update total votes count
        $this->pollModel->updateTotalVotes($pollId);

        // Get updated results if allowed
        $showResults = false;
        $results = [];

        if ($poll['show_results'] === 'always' || $poll['show_results'] === 'after_vote') {
            $showResults = true;
            $results = $this->pollOptionModel->getOptionsWithPercentages($pollId);
        }

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Vote recorded successfully!',
            'show_results' => $showResults,
            'results' => $results
        ]);
    }

    public function getResults($pollId)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->to('/');
        }

        $poll = $this->pollModel->find($pollId);
        if (!$poll) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Poll not found'
            ]);
        }

        if ($poll['show_results'] === 'never') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Results are not available for this poll'
            ]);
        }

        $results = $this->pollOptionModel->getOptionsWithPercentages($pollId);

        return $this->response->setJSON([
            'success' => true,
            'results' => $results,
            'total_votes' => $poll['total_votes']
        ]);
    }
}
