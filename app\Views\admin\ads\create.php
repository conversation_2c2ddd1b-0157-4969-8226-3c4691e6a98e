<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">Add New Advertisement</h4>
    <a href="<?= base_url('admin/ads') ?>" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to Ads
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <?php if (session()->getFlashdata('errors')): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <strong>Please fix the following errors:</strong>
                        <ul class="mb-0 mt-2">
                            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                <li><?= $error ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <form action="<?= base_url('admin/ads/store') ?>" method="post" enctype="multipart/form-data" id="adForm">
                    <?= csrf_field() ?>
                    
                    <div class="mb-3">
                        <label for="title" class="form-label">
                            <i class="fas fa-heading me-2"></i>Advertisement Title <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="title" name="title" 
                               value="<?= old('title') ?>" required 
                               placeholder="Enter advertisement title">
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">
                            <i class="fas fa-align-left me-2"></i>Description
                        </label>
                        <textarea class="form-control" id="description" name="description" rows="3" 
                                  placeholder="Advertisement description..."><?= old('description') ?></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="type" class="form-label">
                                <i class="fas fa-layer-group me-2"></i>Advertisement Type <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="type" name="type" required onchange="toggleImageField()">
                                <option value="">Select Type</option>
                                <option value="banner" <?= old('type') === 'banner' ? 'selected' : '' ?>>Banner (Image)</option>
                                <option value="text" <?= old('type') === 'text' ? 'selected' : '' ?>>Text Only</option>
                                <option value="video" <?= old('type') === 'video' ? 'selected' : '' ?>>Video</option>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="position" class="form-label">
                                <i class="fas fa-map-marker-alt me-2"></i>Position <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="position" name="position" required>
                                <option value="">Select Position</option>
                                <option value="header" <?= old('position') === 'header' ? 'selected' : '' ?>>Header</option>
                                <option value="sidebar" <?= old('position') === 'sidebar' ? 'selected' : '' ?>>Sidebar</option>
                                <option value="footer" <?= old('position') === 'footer' ? 'selected' : '' ?>>Footer</option>
                                <option value="content_top" <?= old('position') === 'content_top' ? 'selected' : '' ?>>Content Top</option>
                                <option value="content_bottom" <?= old('position') === 'content_bottom' ? 'selected' : '' ?>>Content Bottom</option>
                                <option value="between_news" <?= old('position') === 'between_news' ? 'selected' : '' ?>>Between News</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3" id="imageField" style="display: none;">
                        <label for="image" class="form-label">
                            <i class="fas fa-image me-2"></i>Advertisement Image <span class="text-danger">*</span>
                        </label>
                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                        <div class="form-text">Upload banner image (Max: 2MB, JPG/PNG)</div>
                    </div>

                    <div class="mb-3">
                        <label for="link_url" class="form-label">
                            <i class="fas fa-link me-2"></i>Link URL
                        </label>
                        <input type="url" class="form-control" id="link_url" name="link_url" 
                               value="<?= old('link_url') ?>" 
                               placeholder="https://example.com">
                        <div class="form-text">Optional: URL to redirect when ad is clicked</div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="status" class="form-label">
                                <i class="fas fa-toggle-on me-2"></i>Status <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="">Select Status</option>
                                <option value="active" <?= old('status') === 'active' ? 'selected' : '' ?>>Active</option>
                                <option value="inactive" <?= old('status') === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                            </select>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="start_date" class="form-label">
                                <i class="fas fa-calendar-alt me-2"></i>Start Date
                            </label>
                            <input type="datetime-local" class="form-control" id="start_date" name="start_date" 
                                   value="<?= old('start_date') ?>">
                            <div class="form-text">Optional: When to start showing the ad</div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="end_date" class="form-label">
                                <i class="fas fa-calendar-times me-2"></i>End Date
                            </label>
                            <input type="datetime-local" class="form-control" id="end_date" name="end_date" 
                                   value="<?= old('end_date') ?>">
                            <div class="form-text">Optional: When to stop showing the ad</div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <a href="<?= base_url('admin/ads') ?>" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Create Advertisement
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Advertisement Guidelines</h6>
            </div>
            <div class="card-body">
                <h6>Ad Types:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-image text-primary me-2"></i><strong>Banner:</strong> Image-based ads</li>
                    <li><i class="fas fa-font text-info me-2"></i><strong>Text:</strong> Text-only ads</li>
                    <li><i class="fas fa-video text-success me-2"></i><strong>Video:</strong> Video advertisements</li>
                </ul>

                <hr>
                <h6>Positions:</h6>
                <ul class="list-unstyled small">
                    <li><i class="fas fa-arrow-up text-primary me-2"></i><strong>Header:</strong> Top of page</li>
                    <li><i class="fas fa-arrows-alt-h text-info me-2"></i><strong>Sidebar:</strong> Side panel</li>
                    <li><i class="fas fa-arrow-down text-secondary me-2"></i><strong>Footer:</strong> Bottom of page</li>
                    <li><i class="fas fa-arrow-up text-warning me-2"></i><strong>Content Top:</strong> Above content</li>
                    <li><i class="fas fa-arrow-down text-warning me-2"></i><strong>Content Bottom:</strong> Below content</li>
                    <li><i class="fas fa-grip-lines text-muted me-2"></i><strong>Between News:</strong> Between articles</li>
                </ul>

                <hr>
                <h6>Best Practices:</h6>
                <ul class="list-unstyled small">
                    <li><i class="fas fa-check text-success me-2"></i>Use high-quality images</li>
                    <li><i class="fas fa-check text-success me-2"></i>Keep text concise</li>
                    <li><i class="fas fa-check text-success me-2"></i>Test different positions</li>
                    <li><i class="fas fa-check text-success me-2"></i>Monitor performance</li>
                </ul>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-line me-2"></i>Performance Tracking</h6>
            </div>
            <div class="card-body">
                <p class="small">
                    The system automatically tracks:
                </p>
                <ul class="list-unstyled small">
                    <li><i class="fas fa-eye text-info me-2"></i>Impressions (views)</li>
                    <li><i class="fas fa-mouse-pointer text-primary me-2"></i>Clicks</li>
                    <li><i class="fas fa-percentage text-success me-2"></i>Click-through rate (CTR)</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    function toggleImageField() {
        const type = document.getElementById('type').value;
        const imageField = document.getElementById('imageField');
        const imageInput = document.getElementById('image');
        
        if (type === 'banner') {
            imageField.style.display = 'block';
            imageInput.required = true;
        } else {
            imageField.style.display = 'none';
            imageInput.required = false;
        }
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        toggleImageField();
    });

    // Image preview
    document.getElementById('image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                // You can add image preview logic here
            };
            reader.readAsDataURL(file);
        }
    });
</script>
<?= $this->endSection() ?>
