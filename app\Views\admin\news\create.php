<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">Add New Article</h4>
    <a href="<?= base_url('admin/news') ?>" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to News
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <?php if (session()->getFlashdata('errors')): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <strong>Please fix the following errors:</strong>
                        <ul class="mb-0 mt-2">
                            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                <li><?= $error ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <form action="<?= base_url('admin/news/store') ?>" method="post" enctype="multipart/form-data">
                    <?= csrf_field() ?>

                    <div class="mb-3">
                        <label for="title" class="form-label">
                            <i class="fas fa-heading me-2"></i>Title (शीर्षक) <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="title" name="title"
                            value="<?= old('title') ?>" required
                            placeholder="समाचार का शीर्षक यहाँ लिखें...">
                        <div class="form-text">Enter the news headline in Hindi</div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">
                            <i class="fas fa-align-left me-2"></i>Short Description (संक्षिप्त विवरण) <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control" id="description" name="description" rows="3" required
                            placeholder="समाचार का संक्षिप्त विवरण..."><?= old('description') ?></textarea>
                        <div class="form-text">Brief summary of the news article</div>
                    </div>

                    <div class="mb-3">
                        <label for="content" class="form-label">
                            <i class="fas fa-file-alt me-2"></i>Full Content (पूरी सामग्री) <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control" id="content" name="content" rows="10" required
                            placeholder="समाचार की पूरी सामग्री यहाँ लिखें..."><?= old('content') ?></textarea>
                        <div class="form-text">Complete news article content in Hindi</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="category_id" class="form-label">
                                <i class="fas fa-tags me-2"></i>Category (श्रेणी) <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="category_id" name="category_id" required>
                                <option value="">Select Category</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= $category['id'] ?>"
                                        <?= old('category_id') == $category['id'] ? 'selected' : '' ?>>
                                        <?= esc($category['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">
                                <i class="fas fa-toggle-on me-2"></i>Status <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="">Select Status</option>
                                <option value="draft" <?= old('status') === 'draft' ? 'selected' : '' ?>>Draft</option>
                                <option value="published" <?= old('status') === 'published' ? 'selected' : '' ?>>Published</option>
                                <option value="archived" <?= old('status') === 'archived' ? 'selected' : '' ?>>Archived</option>
                            </select>
                        </div>
                    </div>

                    <!-- Media Type Selection -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-photo-video me-2"></i>Media Type (मीडिया प्रकार) <span class="text-danger">*</span>
                        </label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="media_type" id="media_image" value="image"
                                        <?= old('media_type', 'image') === 'image' ? 'checked' : '' ?> required>
                                    <label class="form-check-label" for="media_image">
                                        <i class="fas fa-image me-1"></i>Image (चित्र)
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="media_type" id="media_video" value="video"
                                        <?= old('media_type') === 'video' ? 'checked' : '' ?> required>
                                    <label class="form-check-label" for="media_video">
                                        <i class="fas fa-video me-1"></i>Video (वीडियो)
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="form-text">Choose whether this article will feature an image or video</div>
                    </div>

                    <!-- Image Upload Section -->
                    <div class="mb-3" id="image-section">
                        <label for="image" class="form-label">
                            <i class="fas fa-image me-2"></i>Featured Image (मुख्य चित्र) <span class="text-danger">*</span>
                        </label>
                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                        <div class="form-text">Upload an image for the news article (Max: 2MB, JPG/PNG)</div>
                    </div>

                    <!-- YouTube Video Section -->
                    <div class="mb-3" id="video-section" style="display: none;">
                        <label for="youtube_url" class="form-label">
                            <i class="fab fa-youtube me-2"></i>YouTube Video URL (यूट्यूब वीडियो लिंक) <span class="text-danger">*</span>
                        </label>
                        <input type="url" class="form-control" id="youtube_url" name="youtube_url"
                            value="<?= old('youtube_url') ?>"
                            placeholder="https://www.youtube.com/watch?v=VIDEO_ID or https://youtu.be/VIDEO_ID">
                        <div class="form-text">
                            <strong>Supported YouTube URL formats:</strong><br>
                            • https://www.youtube.com/watch?v=VIDEO_ID<br>
                            • https://youtu.be/VIDEO_ID<br>
                            • https://www.youtube.com/embed/VIDEO_ID
                        </div>
                        <div id="youtube-preview" class="mt-3" style="display: none;">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fab fa-youtube me-2"></i>Video Preview</h6>
                                </div>
                                <div class="card-body">
                                    <div class="ratio ratio-16x9">
                                        <iframe id="youtube-iframe" src="" frameborder="0" allowfullscreen></iframe>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="featured" name="featured" value="1"
                                <?= old('featured') ? 'checked' : '' ?>>
                            <label class="form-check-label" for="featured">
                                <i class="fas fa-star me-2"></i>Mark as Featured Article
                            </label>
                            <div class="form-text">Featured articles will be highlighted on the homepage</div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <a href="<?= base_url('admin/news') ?>" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Create Article
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Article Guidelines</h6>
            </div>
            <div class="card-body">
                <h6>Content Tips:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Write in clear Hindi</li>
                    <li><i class="fas fa-check text-success me-2"></i>Use engaging headlines</li>
                    <li><i class="fas fa-check text-success me-2"></i>Include relevant images</li>
                    <li><i class="fas fa-check text-success me-2"></i>Verify facts before publishing</li>
                </ul>

                <hr>
                <h6>Status Guide:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-edit text-warning me-2"></i><strong>Draft:</strong> Work in progress</li>
                    <li><i class="fas fa-check text-success me-2"></i><strong>Published:</strong> Live on website</li>
                    <li><i class="fas fa-archive text-secondary me-2"></i><strong>Archived:</strong> Hidden from public</li>
                </ul>

                <hr>
                <h6>Media Requirements:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-image text-info me-2"></i><strong>Images:</strong> JPG, PNG (Max: 2MB, 800x600px)</li>
                    <li><i class="fab fa-youtube text-danger me-2"></i><strong>Videos:</strong> YouTube links only</li>
                    <li><i class="fas fa-link text-info me-2"></i>Supported: youtube.com, youtu.be</li>
                </ul>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-language me-2"></i>Hindi Writing Tips</h6>
            </div>
            <div class="card-body">
                <p class="small">
                    <strong>Font Support:</strong> This system supports Devanagari script.
                    You can write directly in Hindi using your keyboard or input method.
                </p>
                <p class="small">
                    <strong>Example Headlines:</strong><br>
                    • राजनीति में नया मोड़<br>
                    • खेल जगत की ताजा खबर<br>
                    • तकनीकी क्रांति का असर
                </p>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Media type switching
    document.addEventListener('DOMContentLoaded', function() {
        const imageRadio = document.getElementById('media_image');
        const videoRadio = document.getElementById('media_video');
        const imageSection = document.getElementById('image-section');
        const videoSection = document.getElementById('video-section');
        const imageInput = document.getElementById('image');
        const youtubeInput = document.getElementById('youtube_url');

        function toggleMediaSections() {
            if (imageRadio.checked) {
                imageSection.style.display = 'block';
                videoSection.style.display = 'none';
                imageInput.required = true;
                youtubeInput.required = false;
                youtubeInput.value = '';
                hideYouTubePreview();
            } else if (videoRadio.checked) {
                imageSection.style.display = 'none';
                videoSection.style.display = 'block';
                imageInput.required = false;
                youtubeInput.required = true;
                imageInput.value = '';
            }
        }

        imageRadio.addEventListener('change', toggleMediaSections);
        videoRadio.addEventListener('change', toggleMediaSections);

        // Initialize on page load
        toggleMediaSections();
    });

    // YouTube URL validation and preview
    document.getElementById('youtube_url').addEventListener('input', function() {
        const url = this.value.trim();
        const preview = document.getElementById('youtube-preview');
        const iframe = document.getElementById('youtube-iframe');

        if (url) {
            const videoId = extractYouTubeId(url);
            if (videoId) {
                // Show preview
                iframe.src = `https://www.youtube.com/embed/${videoId}`;
                preview.style.display = 'block';
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                // Invalid URL
                hideYouTubePreview();
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        } else {
            hideYouTubePreview();
            this.classList.remove('is-valid', 'is-invalid');
        }
    });

    function extractYouTubeId(url) {
        const patterns = [
            /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})/,
            /youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})/,
            /youtu\.be\/([a-zA-Z0-9_-]{11})/,
            /youtube\.com\/embed\/([a-zA-Z0-9_-]{11})/
        ];

        for (let pattern of patterns) {
            const match = url.match(pattern);
            if (match) {
                return match[1];
            }
        }
        return null;
    }

    function hideYouTubePreview() {
        const preview = document.getElementById('youtube-preview');
        const iframe = document.getElementById('youtube-iframe');
        preview.style.display = 'none';
        iframe.src = '';
    }

    // Image preview
    document.getElementById('image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                // You can add image preview logic here
            };
            reader.readAsDataURL(file);
        }
    });

    // Character count for description
    document.getElementById('description').addEventListener('input', function() {
        const maxLength = 500;
        const currentLength = this.value.length;
        // You can add character counter here
    });
</script>
<?= $this->endSection() ?>