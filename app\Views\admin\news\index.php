<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">News Management</h4>
    <a href="<?= base_url('admin/news/create') ?>" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Add New Article
    </a>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-left-primary">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Articles
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= count($news) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-newspaper fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card border-left-success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Published
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= count(array_filter($news, function ($n) {
                                return $n['status'] === 'published';
                            })) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card border-left-warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Drafts
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= count(array_filter($news, function ($n) {
                                return $n['status'] === 'draft';
                            })) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-edit fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card border-left-info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Featured
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= count(array_filter($news, function ($n) {
                                return $n['featured'] == 1;
                            })) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-star fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <?php if (empty($news)): ?>
            <div class="text-center py-5">
                <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No News Articles Found</h5>
                <p class="text-muted">Start by creating your first news article.</p>
                <a href="<?= base_url('admin/news/create') ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Create First Article
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Image</th>
                            <th>Title</th>
                            <th>Category</th>
                            <th>Author</th>
                            <th>Status</th>
                            <th>Views</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($news as $article): ?>
                            <tr>
                                <td>
                                    <?php if ($article['image']): ?>
                                        <img src="<?= base_url('uploads/news/' . $article['image']) ?>"
                                            alt="<?= esc($article['title']) ?>"
                                            class="img-thumbnail" style="width: 60px; height: 40px; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="bg-light d-flex align-items-center justify-content-center"
                                            style="width: 60px; height: 40px; border-radius: 4px;">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div>
                                        <strong><?= esc($article['title']) ?></strong>
                                        <?php if ($article['featured']): ?>
                                            <i class="fas fa-star text-warning ms-1" title="Featured"></i>
                                        <?php endif; ?>
                                    </div>
                                    <small class="text-muted"><?= esc(substr($article['description'], 0, 80)) ?>...</small>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?= esc($article['category_name'] ?? 'No Category') ?></span>
                                </td>
                                <td><?= esc($article['author_name'] ?? 'Unknown') ?></td>
                                <td>
                                    <?php
                                    $statusClass = [
                                        'published' => 'bg-success',
                                        'draft' => 'bg-warning',
                                        'archived' => 'bg-secondary'
                                    ];
                                    ?>
                                    <span class="badge <?= $statusClass[$article['status']] ?? 'bg-secondary' ?>">
                                        <?= ucfirst($article['status']) ?>
                                    </span>
                                </td>
                                <td>
                                    <i class="fas fa-eye me-1"></i><?= number_format($article['views']) ?>
                                </td>
                                <td><?= date('M d, Y', strtotime($article['created_at'])) ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= base_url('admin/news/edit/' . $article['id']) ?>"
                                            class="btn btn-sm btn-outline-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="<?= base_url('admin/news/delete/' . $article['id']) ?>"
                                            class="btn btn-sm btn-outline-danger"
                                            onclick="return confirm('Are you sure you want to delete this article?')"
                                            title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<style>
    .border-left-primary {
        border-left: 4px solid #667eea !important;
    }

    .border-left-success {
        border-left: 4px solid #28a745 !important;
    }

    .border-left-info {
        border-left: 4px solid #17a2b8 !important;
    }

    .border-left-warning {
        border-left: 4px solid #ffc107 !important;
    }
</style>
<?= $this->endSection() ?>