<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="row">
    <!-- Welcome Card -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h4 class="card-title mb-1">Welcome back, <?= session()->get('full_name') ?>!</h4>
                        <p class="text-muted mb-0">Here's what's happening with your news portal today.</p>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-primary fs-6"><?= ucfirst(session()->get('role')) ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Statistics Cards -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Users
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $totalUsers ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Total News
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $totalNews ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-newspaper fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Categories
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $totalCategories ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-tags fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Advertisements
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $totalAds ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-ad fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Quick Actions -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <a href="<?= base_url('admin/news/create') ?>" class="btn btn-primary w-100">
                            <i class="fas fa-plus me-2"></i>Add News
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="<?= base_url('admin/categories/create') ?>" class="btn btn-success w-100">
                            <i class="fas fa-plus me-2"></i>Add Category
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="<?= base_url('admin/tags/create') ?>" class="btn btn-info w-100">
                            <i class="fas fa-plus me-2"></i>Add Tag
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="<?= base_url('admin/ads/create') ?>" class="btn btn-warning w-100">
                            <i class="fas fa-plus me-2"></i>Add Advertisement
                        </a>
                    </div>
                    <?php if (session()->get('role') === 'admin'): ?>
                    <div class="col-md-12">
                        <a href="<?= base_url('admin/users/create') ?>" class="btn btn-secondary w-100">
                            <i class="fas fa-user-plus me-2"></i>Add User
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">Recent Activity</h6>
            </div>
            <div class="card-body">
                <div class="text-center py-4">
                    <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No recent activity to display.</p>
                    <small class="text-muted">Activity will appear here once you start managing content.</small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- System Information -->
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">System Information</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>CodeIgniter Version:</strong><br>
                        <span class="text-muted">4.6.1</span>
                    </div>
                    <div class="col-md-3">
                        <strong>PHP Version:</strong><br>
                        <span class="text-muted"><?= phpversion() ?></span>
                    </div>
                    <div class="col-md-3">
                        <strong>Server Time:</strong><br>
                        <span class="text-muted"><?= date('Y-m-d H:i:s') ?></span>
                    </div>
                    <div class="col-md-3">
                        <strong>Environment:</strong><br>
                        <span class="text-muted"><?= ENVIRONMENT ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<style>
    .border-left-primary {
        border-left: 4px solid #667eea !important;
    }
    .border-left-success {
        border-left: 4px solid #28a745 !important;
    }
    .border-left-info {
        border-left: 4px solid #17a2b8 !important;
    }
    .border-left-warning {
        border-left: 4px solid #ffc107 !important;
    }
    .text-primary {
        color: #667eea !important;
    }
</style>
<?= $this->endSection() ?>
