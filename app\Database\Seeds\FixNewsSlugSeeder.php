<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class FixNewsSlugSeeder extends Seeder
{
    public function run()
    {
        // Load the helper
        helper('hindi');

        // Get all news records
        $newsModel = new \App\Models\News();
        $allNews = $newsModel->findAll();

        foreach ($allNews as $news) {
            // Generate proper English slug
            $slug = transliterate_to_english($news['title']);

            // If transliteration didn't work well, use a simple approach
            if (empty($slug) || strlen($slug) < 3) {
                $slug = 'news-' . $news['id'] . '-' . time();
            }

            // Update the news record
            $newsModel->update($news['id'], ['slug' => $slug]);

            echo "Updated news ID {$news['id']}: {$news['title']} -> {$slug}\n";
        }

        echo "All news slugs have been updated!\n";
    }
}
