<?php

if (!function_exists('formatBytes')) {
    /**
     * Format bytes to human readable format
     *
     * @param int $size Size in bytes
     * @param int $precision Number of decimal places
     * @return string Formatted size
     */
    function formatBytes($size, $precision = 2)
    {
        if ($size == 0) {
            return '0 B';
        }

        $base = log($size, 1024);
        $suffixes = array('B', 'KB', 'MB', 'GB', 'TB');

        return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
    }
}

if (!function_exists('timeAgo')) {
    /**
     * Get time ago string
     *
     * @param string $datetime
     * @return string
     */
    function timeAgo($datetime)
    {
        $time = time() - strtotime($datetime);

        if ($time < 60) return 'just now';
        if ($time < 3600) return floor($time / 60) . ' minutes ago';
        if ($time < 86400) return floor($time / 3600) . ' hours ago';
        if ($time < 2592000) return floor($time / 86400) . ' days ago';
        if ($time < 31536000) return floor($time / 2592000) . ' months ago';

        return floor($time / 31536000) . ' years ago';
    }
}

if (!function_exists('truncateText')) {
    /**
     * Truncate text to specified length
     *
     * @param string $text
     * @param int $length
     * @param string $suffix
     * @return string
     */
    function truncateText($text, $length = 100, $suffix = '...')
    {
        if (strlen($text) <= $length) {
            return $text;
        }

        return substr($text, 0, $length) . $suffix;
    }
}

if (!function_exists('generateSlug')) {
    /**
     * Generate URL-friendly slug
     *
     * @param string $text
     * @return string
     */
    function generateSlug($text)
    {
        // Convert to lowercase
        $text = strtolower($text);

        // Replace spaces and special characters with hyphens
        $text = preg_replace('/[^a-z0-9]+/', '-', $text);

        // Remove leading and trailing hyphens
        $text = trim($text, '-');

        return $text;
    }
}

if (!function_exists('isValidDate')) {
    /**
     * Check if date is valid
     *
     * @param string $date
     * @param string $format
     * @return bool
     */
    function isValidDate($date, $format = 'Y-m-d')
    {
        $d = DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }
}

if (!function_exists('getFileExtension')) {
    /**
     * Get file extension from filename
     *
     * @param string $filename
     * @return string
     */
    function getFileExtension($filename)
    {
        return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    }
}

if (!function_exists('isImageFile')) {
    /**
     * Check if file is an image
     *
     * @param string $filename
     * @return bool
     */
    function isImageFile($filename)
    {
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
        $extension = getFileExtension($filename);

        return in_array($extension, $imageExtensions);
    }
}

if (!function_exists('isPdfFile')) {
    /**
     * Check if file is a PDF
     *
     * @param string $filename
     * @return bool
     */
    function isPdfFile($filename)
    {
        return getFileExtension($filename) === 'pdf';
    }
}

if (!function_exists('sanitizeFilename')) {
    /**
     * Sanitize filename for safe storage
     *
     * @param string $filename
     * @return string
     */
    function sanitizeFilename($filename)
    {
        // Remove path information and dots around the filename
        $filename = basename($filename);

        // Replace spaces with underscores
        $filename = str_replace(' ', '_', $filename);

        // Remove any character that isn't alphanumeric, underscore, hyphen, or dot
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);

        return $filename;
    }
}

if (!function_exists('getMonthName')) {
    /**
     * Get month name in Hindi
     *
     * @param int $month
     * @return string
     */
    function getMonthName($month)
    {
        $months = [
            1 => 'जनवरी',
            2 => 'फरवरी',
            3 => 'मार्च',
            4 => 'अप्रैल',
            5 => 'मई',
            6 => 'जून',
            7 => 'जुलाई',
            8 => 'अगस्त',
            9 => 'सितंबर',
            10 => 'अक्टूबर',
            11 => 'नवंबर',
            12 => 'दिसंबर'
        ];

        return $months[$month] ?? '';
    }
}

if (!function_exists('formatDateHindi')) {
    /**
     * Format date in Hindi
     *
     * @param string $date
     * @return string
     */
    function formatDateHindi($date)
    {
        $timestamp = strtotime($date);
        $day = date('d', $timestamp);
        $month = date('n', $timestamp);
        $year = date('Y', $timestamp);

        return $day . ' ' . getMonthName($month) . ' ' . $year;
    }
}

if (!function_exists('getSetting')) {
    /**
     * Get setting value by key
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function getSetting($key, $default = null)
    {
        static $settingModel = null;

        if ($settingModel === null) {
            $settingModel = new \App\Models\Setting();
        }

        return $settingModel->getSetting($key, $default);
    }
}
