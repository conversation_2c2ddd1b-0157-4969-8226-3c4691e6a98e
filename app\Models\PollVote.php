<?php

namespace App\Models;

use CodeIgniter\Model;

class PollVote extends Model
{
    protected $table            = 'poll_votes';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'poll_id',
        'option_id',
        'voter_ip',
        'voter_session',
        'user_agent',
        'voted_at'
    ];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules = [
        'poll_id'   => 'required|integer',
        'option_id' => 'required|integer',
        'voter_ip'  => 'required|valid_ip',
        'voted_at'  => 'required',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Check if user has already voted
     */
    public function hasUserVoted($pollId, $voterIp, $voterSession = null)
    {
        $builder = $this->where('poll_id', $pollId)
            ->where('voter_ip', $voterIp);

        if ($voterSession) {
            $builder->where('voter_session', $voterSession);
        }

        return $builder->countAllResults() > 0;
    }

    /**
     * Record a vote
     */
    public function recordVote($pollId, $optionId, $voterIp, $voterSession = null, $userAgent = null)
    {
        $data = [
            'poll_id'       => $pollId,
            'option_id'     => $optionId,
            'voter_ip'      => $voterIp,
            'voter_session' => $voterSession,
            'user_agent'    => $userAgent,
            'voted_at'      => date('Y-m-d H:i:s'),
        ];

        return $this->save($data);
    }

    /**
     * Get vote statistics for a poll
     */
    public function getPollVoteStats($pollId)
    {
        $votes = $this->select('option_id, COUNT(*) as vote_count')
            ->where('poll_id', $pollId)
            ->groupBy('option_id')
            ->findAll();

        $stats = [];
        foreach ($votes as $vote) {
            $stats[$vote['option_id']] = $vote['vote_count'];
        }

        return $stats;
    }
}
