<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-address-book me-2"></i>Contact Settings</h2>
                <a href="<?= base_url('admin/settings') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Settings
                </a>
            </div>

            <!-- Success/Error Messages -->
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('errors')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <h6>Please fix the following errors:</h6>
                    <ul class="mb-0">
                        <?php foreach (session()->getFlashdata('errors') as $error): ?>
                            <li><?= esc($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-phone me-2"></i>Contact Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="<?= base_url('admin/settings/contact/update') ?>" method="post">
                                <?= csrf_field() ?>
                                
                                <!-- Contact Email -->
                                <div class="mb-3">
                                    <label for="contact_email" class="form-label">
                                        <i class="fas fa-envelope me-2"></i>Contact Email
                                    </label>
                                    <input type="email" class="form-control" id="contact_email" name="contact_email" 
                                           value="<?= old('contact_email', getSetting('contact_email')) ?>" 
                                           placeholder="<EMAIL>">
                                    <div class="form-text">Primary email address for contact inquiries</div>
                                </div>

                                <!-- Contact Phone -->
                                <div class="mb-3">
                                    <label for="contact_phone" class="form-label">
                                        <i class="fas fa-phone me-2"></i>Contact Phone
                                    </label>
                                    <input type="text" class="form-control" id="contact_phone" name="contact_phone" 
                                           value="<?= old('contact_phone', getSetting('contact_phone')) ?>" 
                                           placeholder="+91-9876543210">
                                    <div class="form-text">Primary phone number for contact</div>
                                </div>

                                <!-- WhatsApp Number -->
                                <div class="mb-3">
                                    <label for="contact_whatsapp" class="form-label">
                                        <i class="fab fa-whatsapp me-2"></i>WhatsApp Number
                                    </label>
                                    <input type="text" class="form-control" id="contact_whatsapp" name="contact_whatsapp" 
                                           value="<?= old('contact_whatsapp', getSetting('contact_whatsapp')) ?>" 
                                           placeholder="+91-9876543210">
                                    <div class="form-text">WhatsApp number for quick contact</div>
                                </div>

                                <!-- Contact Address -->
                                <div class="mb-3">
                                    <label for="contact_address" class="form-label">
                                        <i class="fas fa-map-marker-alt me-2"></i>Contact Address
                                    </label>
                                    <textarea class="form-control" id="contact_address" name="contact_address" rows="3" 
                                              placeholder="Enter your complete address"><?= old('contact_address', getSetting('contact_address')) ?></textarea>
                                    <div class="form-text">Complete business address</div>
                                </div>

                                <!-- Submit Button -->
                                <div class="d-flex justify-content-between">
                                    <a href="<?= base_url('admin/settings') ?>" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Update Contact Settings
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Contact Preview -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-eye me-2"></i>Contact Preview
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="contact-preview">
                                <h6 class="mb-3">Contact Information</h6>
                                
                                <div class="mb-2">
                                    <i class="fas fa-envelope text-primary me-2"></i>
                                    <span id="preview-email"><?= getSetting('contact_email', '<EMAIL>') ?></span>
                                </div>
                                
                                <div class="mb-2">
                                    <i class="fas fa-phone text-success me-2"></i>
                                    <span id="preview-phone"><?= getSetting('contact_phone', '+91-9876543210') ?></span>
                                </div>
                                
                                <div class="mb-2">
                                    <i class="fab fa-whatsapp text-success me-2"></i>
                                    <span id="preview-whatsapp"><?= getSetting('contact_whatsapp', '+91-9876543210') ?></span>
                                </div>
                                
                                <div class="mb-2">
                                    <i class="fas fa-map-marker-alt text-danger me-2"></i>
                                    <span id="preview-address"><?= getSetting('contact_address', 'New Delhi, India') ?></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Guidelines -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>Guidelines
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled small mb-0">
                                <li><i class="fas fa-check text-success me-2"></i>Use valid email format</li>
                                <li><i class="fas fa-check text-success me-2"></i>Include country code for phone</li>
                                <li><i class="fas fa-check text-success me-2"></i>WhatsApp number should be active</li>
                                <li><i class="fas fa-check text-success me-2"></i>Provide complete address</li>
                                <li><i class="fas fa-check text-success me-2"></i>Keep information up-to-date</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>Quick Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="<?= base_url('admin/settings/social') ?>" class="btn btn-outline-primary btn-sm">
                                    <i class="fab fa-facebook me-2"></i>Social Media
                                </a>
                                <a href="<?= base_url('admin/settings/footer') ?>" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-copyright me-2"></i>Footer Settings
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live preview updates
    const emailInput = document.getElementById('contact_email');
    const phoneInput = document.getElementById('contact_phone');
    const whatsappInput = document.getElementById('contact_whatsapp');
    const addressInput = document.getElementById('contact_address');
    
    const previewEmail = document.getElementById('preview-email');
    const previewPhone = document.getElementById('preview-phone');
    const previewWhatsapp = document.getElementById('preview-whatsapp');
    const previewAddress = document.getElementById('preview-address');
    
    emailInput.addEventListener('input', function() {
        previewEmail.textContent = this.value || '<EMAIL>';
    });
    
    phoneInput.addEventListener('input', function() {
        previewPhone.textContent = this.value || '+91-9876543210';
    });
    
    whatsappInput.addEventListener('input', function() {
        previewWhatsapp.textContent = this.value || '+91-9876543210';
    });
    
    addressInput.addEventListener('input', function() {
        previewAddress.textContent = this.value || 'New Delhi, India';
    });
});
</script>
<?= $this->endSection() ?>
