<?php

namespace App\Models;

use CodeIgniter\Model;

class News extends Model
{
    protected $table            = 'news';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['title', 'slug', 'description', 'content', 'image', 'category_id', 'author_id', 'status', 'featured', 'published_at'];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'title'       => 'required|min_length[5]|max_length[500]',
        'slug'        => 'required|min_length[5]|max_length[500]',
        'description' => 'required|min_length[10]',
        'content'     => 'required|min_length[50]',
        'category_id' => 'required|integer|is_not_unique[categories.id]',
        'author_id'   => 'required|integer|is_not_unique[users.id]',
        'status'      => 'required|in_list[draft,published,archived]',
        'featured'    => 'in_list[0,1]',
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['generateSlug'];
    protected $afterInsert    = [];
    protected $beforeUpdate   = ['generateSlug'];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    protected function generateSlug(array $data)
    {
        // Only generate slug if it's not provided or is empty
        if (isset($data['data']['title']) && (!isset($data['data']['slug']) || empty($data['data']['slug']))) {
            // Use transliteration for Hindi titles
            $slug = transliterate_to_english($data['data']['title']);

            // If transliteration didn't work well, use a simple approach
            if (empty($slug) || strlen($slug) < 3) {
                // Generate a simple slug based on timestamp
                $slug = 'news-' . time() . '-' . rand(100, 999);
            }

            // Ensure slug is unique
            $originalSlug = $slug;
            $counter = 1;

            // Get the ID if this is an update operation
            $excludeId = isset($data['id']) ? $data['id'] : null;

            while ($this->slugExists($slug, $excludeId)) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            $data['data']['slug'] = $slug;
        }
        return $data;
    }

    /**
     * Check if slug exists in database
     */
    private function slugExists($slug, $excludeId = null)
    {
        $builder = $this->where('slug', $slug);

        if ($excludeId) {
            $builder->where('id !=', $excludeId);
        }

        return $builder->countAllResults() > 0;
    }

    public function getNewsWithDetails($limit = null, $status = 'published')
    {
        $builder = $this->select('news.*, categories.name as category_name, users.full_name as author_name')
            ->join('categories', 'categories.id = news.category_id')
            ->join('users', 'users.id = news.author_id');

        // Only filter by status if status is provided
        if ($status !== null) {
            $builder->where('news.status', $status);
        }

        $builder->orderBy('news.created_at', 'DESC');

        if ($limit) {
            $builder->limit($limit);
        }

        return $builder->findAll();
    }

    public function getNewsByCategory($categoryId, $limit = null)
    {
        $builder = $this->select('news.*, categories.name as category_name, users.full_name as author_name')
            ->join('categories', 'categories.id = news.category_id')
            ->join('users', 'users.id = news.author_id')
            ->where('news.category_id', $categoryId)
            ->where('news.status', 'published')
            ->orderBy('news.created_at', 'DESC');

        if ($limit) {
            $builder->limit($limit);
        }

        return $builder->findAll();
    }

    public function getFeaturedNews($limit = 5)
    {
        return $this->select('news.*, categories.name as category_name, users.full_name as author_name')
            ->join('categories', 'categories.id = news.category_id')
            ->join('users', 'users.id = news.author_id')
            ->where('news.featured', 1)
            ->where('news.status', 'published')
            ->orderBy('news.created_at', 'DESC')
            ->limit($limit)
            ->findAll();
    }

    public function incrementViews($id)
    {
        $builder = $this->db->table($this->table);
        $builder->set('views', 'views + 1', false);
        $builder->where('id', $id);
        return $builder->update();
    }
}
