<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="mb-0">Add New Tag</h4>
    <a href="<?= base_url('admin/tags') ?>" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to Tags
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <?php if (session()->getFlashdata('errors')): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <strong>Please fix the following errors:</strong>
                        <ul class="mb-0 mt-2">
                            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                <li><?= $error ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <form action="<?= base_url('admin/tags/store') ?>" method="post">
                    <?= csrf_field() ?>
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">
                            <i class="fas fa-hashtag me-2"></i>Tag Name (टैग नाम) <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="name" name="name" 
                               value="<?= old('name') ?>" required 
                               placeholder="e.g., ब्रेकिंग न्यूज़, ट्रेंडिंग, महत्वपूर्ण">
                        <div class="form-text">Enter the tag name in Hindi</div>
                    </div>

                    <div class="mb-3">
                        <label for="slug" class="form-label">
                            <i class="fas fa-link me-2"></i>Slug (URL Identifier) <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="slug" name="slug" 
                               value="<?= old('slug') ?>" required 
                               placeholder="e.g., breaking-news, trending, important">
                        <div class="form-text">URL-friendly identifier (lowercase, no spaces, use hyphens)</div>
                    </div>

                    <div class="mb-3">
                        <label for="status" class="form-label">
                            <i class="fas fa-toggle-on me-2"></i>Status <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="">Select Status</option>
                            <option value="active" <?= old('status') === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="inactive" <?= old('status') === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                        </select>
                        <div class="form-text">Active tags will be available for news articles</div>
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <a href="<?= base_url('admin/tags') ?>" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Create Tag
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Tag Guidelines</h6>
            </div>
            <div class="card-body">
                <h6>Tag Examples:</h6>
                <div class="mb-3">
                    <span class="badge bg-info me-1">#ब्रेकिंग न्यूज़</span>
                    <span class="badge bg-info me-1">#ट्रेंडिंग</span>
                    <span class="badge bg-info me-1">#महत्वपूर्ण</span>
                    <span class="badge bg-info me-1">#लाइव अपडेट</span>
                    <span class="badge bg-info me-1">#विशेष रिपोर्ट</span>
                </div>

                <hr>
                <h6>Best Practices:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Keep tags short and specific</li>
                    <li><i class="fas fa-check text-success me-2"></i>Use relevant keywords</li>
                    <li><i class="fas fa-check text-success me-2"></i>Avoid duplicate meanings</li>
                    <li><i class="fas fa-check text-success me-2"></i>Think about searchability</li>
                </ul>

                <hr>
                <h6>Common Tag Types:</h6>
                <ul class="list-unstyled small">
                    <li><i class="fas fa-tag text-primary me-2"></i><strong>Priority:</strong> ब्रेकिंग, महत्वपूर्ण</li>
                    <li><i class="fas fa-tag text-success me-2"></i><strong>Type:</strong> विशेष रिपोर्ट, इंटरव्यू</li>
                    <li><i class="fas fa-tag text-info me-2"></i><strong>Status:</strong> लाइव, अपडेट</li>
                    <li><i class="fas fa-tag text-warning me-2"></i><strong>Region:</strong> दिल्ली, मुंबई</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Auto-generate slug from name
    document.getElementById('name').addEventListener('input', function() {
        const name = this.value;
        
        // Simple transliteration for common Hindi words
        const transliterations = {
            'ब्रेकिंग न्यूज़': 'breaking-news',
            'ट्रेंडिंग': 'trending',
            'महत्वपूर्ण': 'important',
            'लाइव अपडेट': 'live-update',
            'विशेष रिपोर्ट': 'special-report',
            'इंटरव्यू': 'interview',
            'दिल्ली': 'delhi',
            'मुंबई': 'mumbai',
            'राष्ट्रीय': 'national',
            'अंतर्राष्ट्रीय': 'international'
        };
        
        const slug = transliterations[name] || name.toLowerCase()
                                                  .replace(/[^\w\s-]/g, '')
                                                  .replace(/\s+/g, '-')
                                                  .replace(/-+/g, '-')
                                                  .trim();
        
        document.getElementById('slug').value = slug;
    });

    // Validate slug format
    document.getElementById('slug').addEventListener('input', function() {
        const slug = this.value;
        const validSlug = slug.toLowerCase()
                             .replace(/[^a-z0-9-]/g, '')
                             .replace(/-+/g, '-');
        
        if (slug !== validSlug) {
            this.value = validSlug;
        }
    });
</script>
<?= $this->endSection() ?>
