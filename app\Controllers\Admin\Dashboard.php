<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\User;

class Dashboard extends BaseController
{
    protected $userModel;

    public function __construct()
    {
        $this->userModel = new User();
    }

    public function index()
    {
        $data = [
            'title' => 'Dashboard',
            'totalUsers' => $this->userModel->countAll(),
            'totalNews' => 0, // Will be updated when News model is created
            'totalCategories' => 0, // Will be updated when Category model is created
            'totalAds' => 0, // Will be updated when Ads model is created
        ];

        return view('admin/dashboard', $data);
    }
}
