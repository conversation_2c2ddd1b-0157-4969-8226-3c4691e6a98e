<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\User;
use App\Models\News;
use App\Models\Category;
use App\Models\Ad;

class Dashboard extends BaseController
{
    protected $userModel;
    protected $newsModel;
    protected $categoryModel;
    protected $adModel;

    public function __construct()
    {
        $this->userModel = new User();
        $this->newsModel = new News();
        $this->categoryModel = new Category();
        $this->adModel = new Ad();
    }

    public function index()
    {
        $data = [
            'title' => 'Dashboard',
            'totalUsers' => $this->userModel->countAll(),
            'totalNews' => $this->newsModel->countAll(),
            'totalCategories' => $this->categoryModel->countAll(),
            'totalAds' => $this->adModel->countAll(),
        ];

        return view('admin/dashboard', $data);
    }
}
