<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\User;
use App\Models\News;
use App\Models\Category;

class Dashboard extends BaseController
{
    protected $userModel;
    protected $newsModel;
    protected $categoryModel;

    public function __construct()
    {
        $this->userModel = new User();
        $this->newsModel = new News();
        $this->categoryModel = new Category();
    }

    public function index()
    {
        $data = [
            'title' => 'Dashboard',
            'totalUsers' => $this->userModel->countAll(),
            'totalNews' => $this->newsModel->countAll(),
            'totalCategories' => $this->categoryModel->countAll(),
            'totalAds' => 0, // Will be updated when Ads model is created
        ];

        return view('admin/dashboard', $data);
    }
}
