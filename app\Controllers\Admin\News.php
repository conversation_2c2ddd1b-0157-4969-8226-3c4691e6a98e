<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\News as NewsModel;
use App\Models\Category;

class News extends BaseController
{
    protected $newsModel;
    protected $categoryModel;

    public function __construct()
    {
        $this->newsModel = new NewsModel();
        $this->categoryModel = new Category();
    }

    public function index()
    {
        $data = [
            'title' => 'News Management',
            'news' => $this->newsModel->getNewsWithDetails(null, null) // Get all news regardless of status
        ];

        return view('admin/news/index', $data);
    }

    public function create()
    {
        $data = [
            'title' => 'Add New Article',
            'categories' => $this->categoryModel->where('status', 'active')->findAll()
        ];

        return view('admin/news/create', $data);
    }

    public function store()
    {
        $rules = [
            'title'       => 'required|min_length[5]|max_length[500]',
            'description' => 'required|min_length[10]',
            'content'     => 'required|min_length[50]',
            'category_id' => 'required|integer',
            'status'      => 'required|in_list[draft,published,archived]',
            'image'       => 'uploaded[image]|is_image[image]|max_size[image,2048]',
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Handle image upload
        $image = $this->request->getFile('image');
        $imageName = null;

        if ($image && $image->isValid() && !$image->hasMoved()) {
            $imageName = $image->getRandomName();
            $image->move(FCPATH . 'uploads/news/', $imageName);
        }

        $data = [
            'title'        => $this->request->getPost('title'),
            'slug'         => url_title($this->request->getPost('title'), '-', true),
            'description'  => $this->request->getPost('description'),
            'content'      => $this->request->getPost('content'),
            'image'        => $imageName,
            'category_id'  => $this->request->getPost('category_id'),
            'author_id'    => session()->get('user_id'),
            'status'       => $this->request->getPost('status'),
            'featured'     => $this->request->getPost('featured') ? 1 : 0,
            'published_at' => $this->request->getPost('status') === 'published' ? date('Y-m-d H:i:s') : null,
        ];

        if ($this->newsModel->save($data)) {
            return redirect()->to('/admin/news')->with('success', 'News article created successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create news article');
        }
    }

    public function edit($id)
    {
        $news = $this->newsModel->find($id);

        if (!$news) {
            return redirect()->to('/admin/news')->with('error', 'News article not found');
        }

        $data = [
            'title' => 'Edit Article',
            'news' => $news,
            'categories' => $this->categoryModel->where('status', 'active')->findAll()
        ];

        return view('admin/news/edit', $data);
    }

    public function update($id)
    {
        $news = $this->newsModel->find($id);

        if (!$news) {
            return redirect()->to('/admin/news')->with('error', 'News article not found');
        }

        $rules = [
            'title'       => 'required|min_length[5]|max_length[500]',
            'description' => 'required|min_length[10]',
            'content'     => 'required|min_length[50]',
            'category_id' => 'required|integer',
            'status'      => 'required|in_list[draft,published,archived]',
        ];

        // Only validate image if uploaded
        if ($this->request->getFile('image')->isValid()) {
            $rules['image'] = 'is_image[image]|max_size[image,2048]';
        }

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'title'       => $this->request->getPost('title'),
            'slug'        => url_title($this->request->getPost('title'), '-', true),
            'description' => $this->request->getPost('description'),
            'content'     => $this->request->getPost('content'),
            'category_id' => $this->request->getPost('category_id'),
            'status'      => $this->request->getPost('status'),
            'featured'    => $this->request->getPost('featured') ? 1 : 0,
        ];

        // Handle image upload if new image is provided
        $image = $this->request->getFile('image');
        if ($image && $image->isValid() && !$image->hasMoved()) {
            // Delete old image if exists
            if ($news['image'] && file_exists(FCPATH . 'uploads/news/' . $news['image'])) {
                unlink(FCPATH . 'uploads/news/' . $news['image']);
            }

            $imageName = $image->getRandomName();
            $image->move(FCPATH . 'uploads/news/', $imageName);
            $data['image'] = $imageName;
        }

        // Set published_at if status is published and it wasn't published before
        if ($this->request->getPost('status') === 'published' && $news['status'] !== 'published') {
            $data['published_at'] = date('Y-m-d H:i:s');
        }

        if ($this->newsModel->update($id, $data)) {
            return redirect()->to('/admin/news')->with('success', 'News article updated successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update news article');
        }
    }

    public function delete($id)
    {
        $news = $this->newsModel->find($id);

        if (!$news) {
            return redirect()->to('/admin/news')->with('error', 'News article not found');
        }

        // Delete associated image
        if ($news['image'] && file_exists(FCPATH . 'uploads/news/' . $news['image'])) {
            unlink(FCPATH . 'uploads/news/' . $news['image']);
        }

        if ($this->newsModel->delete($id)) {
            return redirect()->to('/admin/news')->with('success', 'News article deleted successfully');
        } else {
            return redirect()->to('/admin/news')->with('error', 'Failed to delete news article');
        }
    }
}
