<?php

namespace App\Controllers\Api;

use App\Controllers\BaseController;
use App\Models\Ad;

class AdTracking extends BaseController
{
    protected $adModel;

    public function __construct()
    {
        $this->adModel = new Ad();
    }

    public function trackClick($adId)
    {
        $ad = $this->adModel->find($adId);

        if (!$ad) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Ad not found']);
        }

        $this->adModel->incrementClicks($adId);

        return $this->response->setJSON(['status' => 'success', 'message' => 'Click tracked']);
    }

    public function trackImpression($adId)
    {
        $ad = $this->adModel->find($adId);

        if (!$ad) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Ad not found']);
        }

        $this->adModel->incrementImpressions($adId);

        return $this->response->setJSON(['status' => 'success', 'message' => 'Impression tracked']);
    }
}
