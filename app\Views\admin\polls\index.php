<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-poll me-2"></i>Poll Management</h2>
                <div>
                    <a href="<?= base_url('admin/polls/analytics') ?>" class="btn btn-info me-2">
                        <i class="fas fa-chart-line me-2"></i>Analytics
                    </a>
                    <a href="<?= base_url('admin/polls/create') ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create New Poll
                    </a>
                </div>
            </div>

            <!-- Success/Error Messages -->
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Polls Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>All Polls
                        <span class="badge bg-primary ms-2"><?= count($polls) ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($polls)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-poll fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No polls found</h5>
                            <p class="text-muted">Create your first poll to get started.</p>
                            <a href="<?= base_url('admin/polls/create') ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Create New Poll
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Title</th>
                                        <th>Status</th>
                                        <th>Type</th>
                                        <th>Total Votes</th>
                                        <th>Schedule</th>
                                        <th>Created By</th>
                                        <th>Created At</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($polls as $poll): ?>
                                        <tr>
                                            <td><?= $poll['id'] ?></td>
                                            <td>
                                                <div class="fw-bold"><?= esc($poll['title']) ?></div>
                                                <?php if ($poll['description']): ?>
                                                    <small class="text-muted">
                                                        <?= esc(substr($poll['description'], 0, 100)) ?>...
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $statusClass = match ($poll['status']) {
                                                    'active' => 'bg-success',
                                                    'inactive' => 'bg-secondary',
                                                    'closed' => 'bg-danger',
                                                    default => 'bg-secondary'
                                                };
                                                ?>
                                                <span class="badge <?= $statusClass ?>"><?= ucfirst($poll['status']) ?></span>
                                            </td>
                                            <td>
                                                <span class="badge <?= $poll['multiple_choice'] ? 'bg-info' : 'bg-primary' ?>">
                                                    <?= $poll['multiple_choice'] ? 'Multiple Choice' : 'Single Choice' ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-warning text-dark">
                                                    <i class="fas fa-vote-yea me-1"></i><?= number_format($poll['total_votes']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($poll['start_date'] || $poll['end_date']): ?>
                                                    <small>
                                                        <?php if ($poll['start_date']): ?>
                                                            <div><strong>Start:</strong> <?= date('d M Y, H:i', strtotime($poll['start_date'])) ?></div>
                                                        <?php endif; ?>
                                                        <?php if ($poll['end_date']): ?>
                                                            <div><strong>End:</strong> <?= date('d M Y, H:i', strtotime($poll['end_date'])) ?></div>
                                                        <?php endif; ?>
                                                    </small>
                                                <?php else: ?>
                                                    <span class="text-muted">No schedule</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= esc($poll['created_by_name']) ?></td>
                                            <td>
                                                <small><?= date('d M Y, H:i', strtotime($poll['created_at'])) ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('admin/polls/results/' . $poll['id']) ?>"
                                                        class="btn btn-sm btn-outline-info" title="View Results">
                                                        <i class="fas fa-chart-bar"></i>
                                                    </a>
                                                    <a href="<?= base_url('admin/polls/edit/' . $poll['id']) ?>"
                                                        class="btn btn-sm btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button class="btn btn-sm btn-outline-danger"
                                                        onclick="confirmDelete(<?= $poll['id'] ?>)" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this poll? This action cannot be undone and will also delete all votes.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="#" id="confirmDeleteBtn" class="btn btn-danger">Delete</a>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    function confirmDelete(id) {
        const deleteUrl = '<?= base_url('admin/polls/delete/') ?>' + id;
        document.getElementById('confirmDeleteBtn').href = deleteUrl;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }
</script>
<?= $this->endSection() ?>