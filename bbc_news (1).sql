-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 25, 2025 at 08:12 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `bbc_news`
--

-- --------------------------------------------------------

--
-- Table structure for table `ads`
--

CREATE TABLE `ads` (
  `id` int(11) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `link_url` varchar(500) DEFAULT NULL,
  `position` enum('header','sidebar','footer','content_top','content_bottom','between_news') NOT NULL DEFAULT 'sidebar',
  `type` enum('banner','text','video') NOT NULL DEFAULT 'banner',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `clicks` int(11) NOT NULL DEFAULT 0,
  `impressions` int(11) NOT NULL DEFAULT 0,
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `ads`
--

INSERT INTO `ads` (`id`, `title`, `description`, `image`, `link_url`, `position`, `type`, `status`, `clicks`, `impressions`, `start_date`, `end_date`, `created_at`, `updated_at`) VALUES
(1, 'प्रीमियम न्यूज़ ऐप डाउनलोड करें', 'सबसे तेज़ और विश्वसनीय न्यूज़ ऐप। अभी डाउनलोड करें और ₹100 का कैशबैक पाएं।', NULL, 'https://play.google.com/store', 'header', 'text', 'active', 45, 1335, NULL, NULL, '2025-06-24 09:15:40', '2025-06-24 09:15:40'),
(2, 'ऑनलाइन शिक्षा प्लेटफॉर्म', 'घर बैठे सीखें। 1000+ कोर्स उपलब्ध। पहले महीने 50% छूट।', NULL, 'https://example.com/education', 'sidebar', 'text', 'active', 23, 1550, NULL, NULL, '2025-06-24 09:15:40', '2025-06-24 09:15:40'),
(3, 'स्वास्थ्य बीमा योजना', 'पूरे परिवार के लिए ₹5 लाख तक का कवरेज। कम प्रीमियम, ज्यादा फायदे।', NULL, 'https://example.com/health-insurance', 'content_top', 'text', 'active', 67, 1490, NULL, NULL, '2025-06-24 09:15:40', '2025-06-24 09:15:40'),
(4, 'ऑनलाइन शॉपिंग सेल', 'मेगा सेल! 70% तक की छूट। फ्री होम डिलीवरी। आज ही ऑर्डर करें।', NULL, 'https://example.com/shopping', 'footer', 'text', 'active', 89, 2162, NULL, NULL, '2025-06-24 09:15:40', '2025-06-24 09:15:40'),
(5, 'डिजिटल मार्केटिंग कोर्स', 'सिर्फ 3 महीने में डिजिटल मार्केटिंग एक्सपर्ट बनें। जॉब गारंटी के साथ।', NULL, 'https://example.com/digital-marketing', 'between_news', 'text', 'active', 34, 1010, NULL, NULL, '2025-06-24 09:15:40', '2025-06-25 05:35:58'),
(6, 'होम लोन स्पेशल ऑफर', '6.5% की दर से होम लोन। तुरंत अप्रूवल। न्यूनतम डॉक्यूमेंट्स।', NULL, 'https://example.com/home-loan', 'content_bottom', 'text', 'active', 57, 1342, NULL, NULL, '2025-06-24 09:15:40', '2025-06-24 09:15:40'),
(7, 'फिटनेस ऐप प्रीमियम', 'घर बैठे फिट रहें। पर्सनल ट्रेनर, डाइट प्लान। पहले महीने फ्री।', NULL, 'https://example.com/fitness', '', 'text', 'active', 12, 450, NULL, NULL, '2025-06-24 09:15:40', '2025-06-24 09:15:40'),
(8, 'न्यूज़ अलर्ट सब्स्क्रिप्शन', 'ब्रेकिंग न्यूज़ सबसे पहले पाएं। SMS और ईमेल अलर्ट फ्री।', NULL, 'https://example.com/news-alerts', '', 'text', 'inactive', 0, 0, NULL, NULL, '2025-06-24 09:15:40', '2025-06-24 09:15:40');

-- --------------------------------------------------------

--
-- Table structure for table `breaking_news`
--

CREATE TABLE `breaking_news` (
  `id` int(11) UNSIGNED NOT NULL,
  `title` varchar(500) NOT NULL,
  `content` text DEFAULT NULL,
  `link_url` varchar(500) DEFAULT NULL,
  `priority` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1=Low, 2=Medium, 3=High, 4=Critical',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `start_time` datetime DEFAULT NULL,
  `end_time` datetime DEFAULT NULL,
  `created_by` int(11) UNSIGNED NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `breaking_news`
--

INSERT INTO `breaking_news` (`id`, `title`, `content`, `link_url`, `priority`, `status`, `start_time`, `end_time`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 'प्रधानमंत्री मोदी ने आज नई शिक्षा नीति की घोषणा की', 'नई शिक्षा नीति से देश की शिक्षा व्यवस्था में क्रांतिकारी बदलाव आएगा।', NULL, 4, 'active', NULL, NULL, 1, '2025-06-24 09:09:54', '2025-06-24 09:09:54'),
(2, 'भारतीय क्रिकेट टीम ने विश्व कप जीता - देश में जश्न का माहौल', 'भारतीय क्रिकेट टीम की शानदार जीत से पूरे देश में खुशी की लहर।', 'http://localhost/bbc_news/public/news/karakata-vashava-kapa-ma-bharata-ka-shanadara-jata', 3, 'active', NULL, NULL, 1, '2025-06-24 09:09:54', '2025-06-24 09:09:54'),
(3, 'दिल्ली में भारी बारिश - कई इलाकों में जलभराव', 'मौसम विभाग ने अगले 24 घंटों के लिए भारी बारिश की चेतावनी जारी की है।', NULL, 2, 'active', NULL, '2025-06-26 09:09:54', 1, '2025-06-24 09:09:54', '2025-06-24 09:09:54'),
(4, 'शेयर बाजार में तेजी - सेंसेक्स 500 अंक ऊपर', 'आर्थिक सुधारों की उम्मीदों से शेयर बाजार में तेजी देखी जा रही है।', NULL, 1, 'active', NULL, NULL, 2, '2025-06-24 09:09:54', '2025-06-24 09:09:54');

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `id` int(11) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`id`, `name`, `slug`, `description`, `status`, `created_at`, `updated_at`) VALUES
(1, 'राजनीति', 'politics', 'राजनीतिक समाचार और अपडेट', 'active', '2025-06-24 07:33:11', '2025-06-24 07:33:11'),
(2, 'खेल', 'sports', 'खेल जगत की ताजा खबरें', 'active', '2025-06-24 07:33:11', '2025-06-24 07:33:11'),
(3, 'अंतरराष्ट्रीय', 'international', 'अंतरराष्ट्रीय / दुनिया', 'active', '2025-06-24 07:33:11', '2025-06-25 05:37:38'),
(4, 'राष्ट्रीय', 'nationanl', 'राष्ट्रीय समाचार और नवाचार', 'active', '2025-06-24 07:33:11', '2025-06-25 05:38:00'),
(5, 'राज्य', 'State', 'राज्य और अर्थव्यवस्था की खबरें', 'active', '2025-06-24 07:33:11', '2025-06-25 05:38:57'),
(6, 'अपराध ', '-crime', 'अपराध न्यूज़', 'active', '2025-06-24 09:21:36', '2025-06-24 09:21:36'),
(7, 'बिजनेस', 'business', 'बिजनेस न्यूज़', 'active', '2025-06-24 09:34:13', '2025-06-25 05:39:24'),
(8, 'फिल्म', 'movies', 'फिल्म ', 'active', '2025-06-25 05:39:54', '2025-06-25 05:39:54'),
(9, 'सोशल', 'social', 'सोशल', 'active', '2025-06-25 05:40:08', '2025-06-25 05:40:08'),
(10, 'मेडिकल', 'medical', 'मेडिकल', 'active', '2025-06-25 05:40:28', '2025-06-25 05:40:28'),
(11, 'कृषि ', '-agriculture', 'कृषि ', 'active', '2025-06-25 05:40:45', '2025-06-25 05:40:45'),
(12, 'अन्य', 'others', 'अन्य', 'active', '2025-06-25 05:40:57', '2025-06-25 05:40:57');

-- --------------------------------------------------------

--
-- Table structure for table `epapers`
--

CREATE TABLE `epapers` (
  `id` int(11) UNSIGNED NOT NULL,
  `title` varchar(500) NOT NULL,
  `description` text DEFAULT NULL,
  `publication_date` date NOT NULL,
  `edition` varchar(100) NOT NULL DEFAULT 'Main Edition',
  `language` varchar(50) NOT NULL DEFAULT 'Hindi',
  `pdf_file` varchar(255) NOT NULL,
  `thumbnail` varchar(255) DEFAULT NULL,
  `file_size` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'File size in bytes',
  `page_count` int(11) UNSIGNED NOT NULL DEFAULT 0,
  `download_count` int(11) UNSIGNED NOT NULL DEFAULT 0,
  `view_count` int(11) UNSIGNED NOT NULL DEFAULT 0,
  `status` enum('active','inactive','archived') NOT NULL DEFAULT 'active',
  `featured` tinyint(1) NOT NULL DEFAULT 0,
  `uploaded_by` int(11) UNSIGNED NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `epapers`
--

INSERT INTO `epapers` (`id`, `title`, `description`, `publication_date`, `edition`, `language`, `pdf_file`, `thumbnail`, `file_size`, `page_count`, `download_count`, `view_count`, `status`, `featured`, `uploaded_by`, `created_at`, `updated_at`) VALUES
(1, 'BBC News - 24 जून 2025', 'आज के मुख्य समाचार: राष्ट्रीय और अंतर्राष्ट्रीय घटनाओं की विस्तृत कवरेज', '2025-06-24', 'Main Edition', 'Hindi', '2025-06-24_1750764312_1750764312_7d54ac4cbf5c9f6cc396.pdf', 'thumb_1750764312_1750764312_ff7b2262bbfbb6394754.png', 15080402, 16, 245, 1258, 'active', 1, 1, '2025-06-24 11:01:22', '2025-06-24 11:25:12'),
(2, 'BBC News - 23 जून 2025', 'कल के प्रमुख समाचार और विश्लेषण', '2025-06-23', 'Main Edition', 'Hindi', 'sample_epaper_2.pdf', 'thumb_epaper_2.jpg', 18874368, 20, 189, 890, 'active', 0, 1, '2025-06-23 11:01:22', '2025-06-23 11:01:22'),
(3, 'BBC News - 22 जून 2025', 'सप्ताहांत विशेष संस्करण', '2025-06-22', 'Weekend Edition', 'Hindi', 'sample_epaper_3.pdf', 'thumb_epaper_3.jpg', 22020096, 24, 156, 678, 'active', 0, 2, '2025-06-22 11:01:22', '2025-06-22 11:01:22'),
(4, 'BBC News - 21 जून 2025', 'शुक्रवार के मुख्य समाचार', '2025-06-21', 'Main Edition', 'Hindi', 'sample_epaper_4.pdf', NULL, 14680064, 14, 134, 567, 'active', 0, 1, '2025-06-21 11:01:22', '2025-06-21 11:01:22'),
(5, 'BBC News - 20 जून 2025', 'गुरुवार के प्रमुख समाचार और विशेष रिपोर्ट', '2025-06-20', 'Main Edition', 'Hindi', 'sample_epaper_5.pdf', 'thumb_epaper_5.jpg', 16777216, 18, 98, 445, 'archived', 0, 2, '2025-06-20 11:01:22', '2025-06-20 11:01:22');

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `version` varchar(255) NOT NULL,
  `class` varchar(255) NOT NULL,
  `group` varchar(255) NOT NULL,
  `namespace` varchar(255) NOT NULL,
  `time` int(11) NOT NULL,
  `batch` int(11) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `version`, `class`, `group`, `namespace`, `time`, `batch`) VALUES
(1, '2025-06-24-072331', 'App\\Database\\Migrations\\CreateUsersTable', 'default', 'App', 1750750307, 1),
(2, '2025-06-24-072428', 'App\\Database\\Migrations\\CreateCategoriesTable', 'default', 'App', 1750750307, 1),
(3, '2025-06-24-072721', 'App\\Database\\Migrations\\CreateTagsTable', 'default', 'App', 1750750307, 1),
(4, '2025-06-24-072741', 'App\\Database\\Migrations\\CreateNewsTable', 'default', 'App', 1750750307, 1),
(5, '2025-06-24-072756', 'App\\Database\\Migrations\\CreateNewsTagsTable', 'default', 'App', 1750750307, 1),
(6, '2025-06-24-072808', 'App\\Database\\Migrations\\CreateAdsTable', 'default', 'App', 1750750307, 1),
(7, '2025-06-24-085519', 'App\\Database\\Migrations\\CreateBreakingNewsTable', 'default', 'App', 1750755369, 2),
(8, '2025-06-24-093236', 'App\\Database\\Migrations\\CreatePollsTable', 'default', 'App', 1750757789, 3),
(9, '2025-06-24-093438', 'App\\Database\\Migrations\\CreatePollOptionsTable', 'default', 'App', 1750757789, 3),
(10, '2025-06-24-093557', 'App\\Database\\Migrations\\CreatePollVotesTable', 'default', 'App', 1750757789, 3),
(11, '2025-06-24-104138', 'App\\Database\\Migrations\\CreateEpapersTable', 'default', 'App', 1750762035, 4),
(12, '2025-06-25-043033', 'App\\Database\\Migrations\\CreateSettingsTable', 'default', 'App', 1750825964, 5),
(13, '2025-06-25-044438', 'App\\Database\\Migrations\\AddPhoneToUsersTable', 'default', 'App', 1750826720, 6),
(14, '2025-06-25-055429', 'App\\Database\\Migrations\\AddVideoFieldsToNewsTable', 'default', 'App', 1750830897, 7);

-- --------------------------------------------------------

--
-- Table structure for table `news`
--

CREATE TABLE `news` (
  `id` int(11) UNSIGNED NOT NULL,
  `title` varchar(500) NOT NULL,
  `slug` varchar(500) NOT NULL,
  `description` text NOT NULL,
  `content` longtext NOT NULL,
  `media_type` enum('image','video') NOT NULL DEFAULT 'image',
  `youtube_url` varchar(500) DEFAULT NULL,
  `youtube_id` varchar(50) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `category_id` int(11) UNSIGNED NOT NULL,
  `author_id` int(11) UNSIGNED NOT NULL,
  `status` enum('draft','published','archived') NOT NULL DEFAULT 'draft',
  `featured` tinyint(1) NOT NULL DEFAULT 0,
  `views` int(11) NOT NULL DEFAULT 0,
  `published_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `news`
--

INSERT INTO `news` (`id`, `title`, `slug`, `description`, `content`, `media_type`, `youtube_url`, `youtube_id`, `image`, `category_id`, `author_id`, `status`, `featured`, `views`, `published_at`, `created_at`, `updated_at`) VALUES
(1, 'भारत में तकनीकी क्रांति का नया दौर', 'bharata-ma-technology-karata-ka-naya-dara', 'भारत में तकनीकी क्षेत्र में हो रहे नवाचार और डिजिटल इंडिया मिशन की सफलता की कहानी।', 'भारत आज तकनीकी क्रांति के एक नए दौर में प्रवेश कर रहा है। डिजिटल इंडिया मिशन के तहत देश भर में इंटरनेट कनेक्टिविटी, डिजिटल साक्षरता और ई-गवर्नेंस के क्षेत्र में उल्लेखनीय प्रगति हुई है। स्टार्टअप इकोसिस्टम में भारत दुनिया का तीसरा सबसे बड़ा देश बन गया है। फिनटेक, एडटेक, हेल्थटेक जैसे क्षेत्रों में भारतीय कंपनियां वैश्विक स्तर पर अपनी पहचान बना रही हैं।', 'image', NULL, NULL, '1750759523_337cf6996a872e2ecefe.jpg', 4, 1, 'published', 1, 1260, '2025-06-24 06:19:54', '2025-06-24 06:19:54', '2025-06-24 10:05:23'),
(2, 'क्रिकेट विश्व कप में भारत की शानदार जीत', 'karakata-vashava-kapa-ma-bharata-ka-shanadara-jata', 'भारतीय क्रिकेट टीम ने विश्व कप के फाइनल में शानदार प्रदर्शन करके खिताब अपने नाम किया।', 'भारतीय क्रिकेट टीम ने आज विश्व कप के फाइनल मैच में शानदार प्रदर्शन करके खिताब जीता है। कप्तान की बेहतरीन कप्तानी और टीम के सभी खिलाड़ियों के उत्कृष्ट प्रदर्शन से यह जीत संभव हुई। फाइनल मैच में भारत ने 350 रन का विशाल स्कोर बनाया और विपक्षी टीम को 280 रन पर ऑल आउट कर दिया। यह जीत भारतीय क्रिकेट के इतिहास में एक नया अध्याय जोड़ती है।', 'image', NULL, NULL, NULL, 2, 2, 'published', 1, 2106, '2025-06-24 04:19:54', '2025-06-24 04:19:54', '2025-06-24 08:43:50'),
(3, 'बॉलीवुड में नई फिल्म का धमाकेदार ट्रेलर रिलीज', 'balavada-ma-naee-phalama-ka-dhamakadara-taralara-ralaja', 'बॉलीवुड की आगामी ब्लॉकबस्टर फिल्म का ट्रेलर रिलीज हुआ, दर्शकों में उत्साह।', 'बॉलीवुड की सबसे प्रतीक्षित फिल्म का ट्रेलर आज रिलीज हुआ है। इस फिल्म में बॉलीवुड के टॉप स्टार्स ने काम किया है। ट्रेलर में दिखाए गए एक्शन सीक्वेंस और इमोशनल मोमेंट्स ने दर्शकों का दिल जीत लिया है। फिल्म अगले महीने सिनेमाघरों में रिलीज होगी। निर्देशक ने कहा कि यह फिल्म भारतीय सिनेमा के लिए एक मील का पत्थर साबित होगी।', 'image', NULL, NULL, NULL, 3, 1, 'published', 0, 890, '2025-06-24 02:19:54', '2025-06-24 02:19:54', '2025-06-24 08:43:50'),
(4, 'सरकार की नई आर्थिक नीति से व्यापार में तेजी', 'sarakara-ka-naee-aarathaka-nata-sa-business-ma-taja', 'केंद्र सरकार की नई आर्थिक नीति से छोटे और मध्यम व्यापारियों को बड़ा फायदा होने की उम्मीद।', 'केंद्र सरकार ने आज एक नई आर्थिक नीति की घोषणा की है जो छोटे और मध्यम व्यापारियों के लिए बेहद फायदेमंद साबित होगी। इस नीति के तहत व्यापारियों को कम ब्याज दर पर लोन मिलेगा और टैक्स में भी छूट दी जाएगी। अर्थशास्त्रियों का मानना है कि यह नीति देश की अर्थव्यवस्था को नई दिशा देगी और रोजगार के नए अवसर पैदा करेगी।', 'image', NULL, NULL, '1750760273_8205c0c02644ded1bd54.webp', 5, 2, 'published', 0, 653, '2025-06-24 00:19:54', '2025-06-24 00:19:54', '2025-06-24 10:17:53'),
(5, 'राज्य सभा में महत्वपूर्ण विधेयक पारित', 'rajaya-sabha-ma-important-vadhayaka-parata', 'राज्य सभा में आज एक महत्वपूर्ण विधेयक पारित हुआ जो जनता के हित में है।', 'राज्य सभा में आज एक महत्वपूर्ण विधेयक पारित हुआ है जो आम जनता के हित में है। इस विधेयक से शिक्षा, स्वास्थ्य और रोजगार के क्षेत्र में सुधार होगा। सभी राजनीतिक दलों ने इस विधेयक का समर्थन किया है। विपक्ष ने भी इसे जनहित में बताया है। अब यह विधेयक लोकसभा में भेजा जाएगा।', 'image', NULL, NULL, NULL, 1, 1, 'published', 1, 1800, '2025-06-23 22:19:54', '2025-06-23 22:19:54', '2025-06-24 08:43:50'),
(6, 'पीएम मोदी 12 जून को टीडीपी प्रमुख चंद्रबाबू नायडू के शपथ ग्रहण समारोह में शामिल होंगे', 'paema-mada-12-jana-ka-tadapa-paramakha-chadarababa-nayada-ka-shapatha-garaha-samaraha-ma-shamala-haga', 'मनोनीत प्रधानमंत्री नरेंद्र मोदी अपने गठबंधन के साथी और टीडीपी प्रमुख के शपथ ग्रहण समारोह में शामिल हो सकते हैं। टीडीपी प्रमुख चंद्रबाबू नायडू आंध्र प्रदेश के मुख्यमंत्री के रूप में 12 जून को शपथ लेंगे। बता दें, मनोनीत प्रधानमंत्री मोदी आज राष्ट्रपति भवन में तीसरी बार प्रधानमंत्री के रूप में शपथ ग्रहण करेंगे।', 'मनोनीत प्रधानमंत्री नरेंद्र मोदी अपने गठबंधन के साथी और टीडीपी प्रमुख के शपथ ग्रहण समारोह में शामिल हो सकते हैं। टीडीपी प्रमुख चंद्रबाबू नायडू आंध्र प्रदेश के मुख्यमंत्री के रूप में 12 जून को शपथ लेंगे। बता दें, मनोनीत प्रधानमंत्री मोदी आज राष्ट्रपति भवन में तीसरी बार प्रधानमंत्री के रूप में शपथ ग्रहण करेंगे।\r\n\r\nमुख्य सचिव नीरभ कुमार प्रसाद ने शनिवार को बताया कि शपथ ग्रहण समारोह में पीएम मोदी, राज्यपाल एस अब्दुल नजीर और अन्य गणमान्य शामिल हो सकते हैं। नायडू बुधवार को सुबह 11:27 बजे गन्नवरम हवाई अड्डे के पास केसरपल्ली आईटी पार्क में शपथ लेंगे। मुख्य सचिव ने समारोह के लिए की जा रही तैयारियों की समीक्षा की और अधिकारियों को पुख्ता तैयारियां करने का निर्देश दिया। गन्नवरम हवाई अड्डे पर कई वीवीआईपी के पहुंचने की उम्मीद है। शपथ ग्रहण समारोह के लिए वरिष्ठ आईएएस अधिकारी पीएस पद्युम्न को राज्य समन्वयक नियुक्त किया गया है।\r\n\r\nकेंद्र में भी मजबूत स्तंभ बनकर उभरे\r\n\r\nगौरतलब है कि नायडू परिवार और चंद्रबाबू नायडू की पत्नी की संपत्ति में एक तरफ ये इजाफा हो रहा है। वहीं दूसरी ओर 12 जून को चंद्रबाबू नायडू आंध्र प्रदेश के नए सीएम बनेंगे। आंध्र प्रदेश विधानसभा चुनाव परिणाम में टीडीपी ने सबसे अधिक सीटें जीती हैं। 135 सीटें जीत कर टीडीपी ने बहुमत के आंकड़े को पार कर लिया।\r\n\r\nकिंग मेकर की भूमिका में हैं चंद्रबाबू नायडू\r\n\r\nचंद्रबाबू नायडू एनडीए गठबंधन के लिए एक किंगमेकर्स में से एक बनकर सामने आए हैं। एनडीए ने लगातार तीसरी बार बहुमत पाने का रिकॉर्ड बनया है। चंद्रबाबू नायडू और नीतीश कुमार की पार्टी का गठबंधन की सरकार बनाने में अहम योगदान रहने वाला है। भाजपा इस बार लोकसभा चुनाव में अकेले दम पर बहुमत लाने में सफल नहीं हो सकी है। वहीं कांग्रेस के नेतृत्व वाले इंडिया गठबंधन ने इस बार चुनाव में बेहतरीन प्रदर्शन किया है।', 'image', NULL, NULL, '1750753523_61bb0e8fbeab52801378.jpeg', 1, 1, 'published', 0, 14, '2025-06-24 08:25:23', '2025-06-24 08:25:23', '2025-06-25 06:01:56'),
(7, 'यमुना प्राधिकरण के खिलाफ किसानों का फूटा गुस्सा, दिया अल्टीमेटम', 'yamana-paradhakara-ka-khalapha-kasana-ka-phata-gasasa-daya-alatamatama', 'यमुना प्राधिकरण के खिलाफ किसानों का फूटा गुस्सा, दिया अल्टीमेटम. यमुना प्राधिकरण के खिलाफ किसानों का फूटा गुस्सा, दिया अल्टीमेटम यमुना प्राधिकरण के खिलाफ किसानों ..\r\n\r\n', 'यमुना प्राधिकरण के खिलाफ किसानों का फूटा गुस्सा, दिया अल्टीमेटम  यमुना प्राधिकरण के खिलाफ किसानों का फूटा गुस्सा, दिया अल्टीमेटम यमुना प्राधिकरण के खिलाफ किसानों का फूटा गुस्सा, दिया अल्टीमेटम यमुना प्राधिकरण के खिलाफ किसानों का फूटा गुस्सा, दिया अल्टीमेटम यमुना प्राधिकरण के खिलाफ किसानों का फूटा गुस्सा, दिया अल्टीमेटम\r\n', 'video', 'https://www.youtube.com/watch?v=QsMs0MPOFkw', 'QsMs0MPOFkw', NULL, 11, 1, 'published', 1, 3, '2025-06-25 06:04:35', '2025-06-25 06:04:35', '2025-06-25 06:04:35');

-- --------------------------------------------------------

--
-- Table structure for table `news_tags`
--

CREATE TABLE `news_tags` (
  `id` int(11) UNSIGNED NOT NULL,
  `news_id` int(11) UNSIGNED NOT NULL,
  `tag_id` int(11) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `polls`
--

CREATE TABLE `polls` (
  `id` int(11) UNSIGNED NOT NULL,
  `title` varchar(500) NOT NULL,
  `description` text DEFAULT NULL,
  `status` enum('active','inactive','closed') NOT NULL DEFAULT 'active',
  `multiple_choice` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=Single choice, 1=Multiple choice',
  `show_results` enum('after_vote','always','never') NOT NULL DEFAULT 'after_vote',
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `total_votes` int(11) UNSIGNED NOT NULL DEFAULT 0,
  `created_by` int(11) UNSIGNED NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `polls`
--

INSERT INTO `polls` (`id`, `title`, `description`, `status`, `multiple_choice`, `show_results`, `start_date`, `end_date`, `total_votes`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 'भारत की सबसे बड़ी समस्या क्या है?', 'आपके अनुसार वर्तमान में भारत की सबसे बड़ी समस्या कौन सी है?', 'inactive', 0, 'after_vote', NULL, NULL, 125, 1, '2025-06-24 09:45:20', '2025-06-24 09:58:17'),
(2, 'आपको कौन से समाचार विषय सबसे ज्यादा पसंद हैं?', 'हमारी वेबसाइट पर आप किस प्रकार के समाचार सबसे ज्यादा पढ़ना पसंद करते हैं?', 'inactive', 1, 'after_vote', NULL, NULL, 150, 1, '2025-06-24 09:45:20', '2025-06-24 09:58:27'),
(3, 'डिजिटल इंडिया मिशन कितना सफल है?', 'आपके अनुसार डिजिटल इंडिया मिशन की सफलता का स्तर क्या है?', 'active', 0, 'always', NULL, '2025-07-24 09:45:20', 1, 2, '2025-06-24 09:45:20', '2025-06-24 09:58:57');

-- --------------------------------------------------------

--
-- Table structure for table `poll_options`
--

CREATE TABLE `poll_options` (
  `id` int(11) UNSIGNED NOT NULL,
  `poll_id` int(11) UNSIGNED NOT NULL,
  `option_text` varchar(500) NOT NULL,
  `vote_count` int(11) UNSIGNED NOT NULL DEFAULT 0,
  `sort_order` int(11) UNSIGNED NOT NULL DEFAULT 0,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `poll_options`
--

INSERT INTO `poll_options` (`id`, `poll_id`, `option_text`, `vote_count`, `sort_order`, `created_at`, `updated_at`) VALUES
(12, 3, 'बहुत सफल', 13, 1, '2025-06-24 09:45:21', '2025-06-24 09:45:21'),
(13, 3, 'सफल', 45, 2, '2025-06-24 09:45:21', '2025-06-24 09:45:21'),
(14, 3, 'औसत', 24, 3, '2025-06-24 09:45:21', '2025-06-24 09:45:21'),
(15, 3, 'असफल', 50, 4, '2025-06-24 09:45:21', '2025-06-24 09:45:21'),
(16, 3, 'बहुत असफल', 40, 5, '2025-06-24 09:45:21', '2025-06-24 09:45:21'),
(17, 1, 'बेरोजगारी', 0, 1, '2025-06-24 09:58:17', '2025-06-24 09:58:17'),
(18, 1, 'भ्रष्टाचार', 0, 2, '2025-06-24 09:58:17', '2025-06-24 09:58:17'),
(19, 1, 'गरीबी', 0, 3, '2025-06-24 09:58:17', '2025-06-24 09:58:17'),
(20, 1, 'शिक्षा की कमी', 0, 4, '2025-06-24 09:58:17', '2025-06-24 09:58:17'),
(21, 1, 'स्वास्थ्य सेवाओं की कमी', 0, 5, '2025-06-24 09:58:17', '2025-06-24 09:58:17'),
(22, 2, 'राजनीति', 0, 1, '2025-06-24 09:58:27', '2025-06-24 09:58:27'),
(23, 2, 'खेल', 0, 2, '2025-06-24 09:58:27', '2025-06-24 09:58:27'),
(24, 2, 'मनोरंजन', 0, 3, '2025-06-24 09:58:27', '2025-06-24 09:58:27'),
(25, 2, 'तकनीक', 0, 4, '2025-06-24 09:58:27', '2025-06-24 09:58:27'),
(26, 2, 'व्यापार', 0, 5, '2025-06-24 09:58:27', '2025-06-24 09:58:27'),
(27, 2, 'अंतर्राष्ट्रीय समाचार', 0, 6, '2025-06-24 09:58:27', '2025-06-24 09:58:27');

-- --------------------------------------------------------

--
-- Table structure for table `poll_votes`
--

CREATE TABLE `poll_votes` (
  `id` int(11) UNSIGNED NOT NULL,
  `poll_id` int(11) UNSIGNED NOT NULL,
  `option_id` int(11) UNSIGNED NOT NULL,
  `voter_ip` varchar(45) NOT NULL,
  `voter_session` varchar(128) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `voted_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `poll_votes`
--

INSERT INTO `poll_votes` (`id`, `poll_id`, `option_id`, `voter_ip`, `voter_session`, `user_agent`, `voted_at`) VALUES
(1, 3, 13, '::1', 'f34c8b78a79d8bcb3591d3ea6b38f930', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-24 09:58:57');

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) UNSIGNED NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_type` enum('text','textarea','image','file','json','boolean') NOT NULL DEFAULT 'text',
  `category` varchar(50) NOT NULL DEFAULT 'general',
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `setting_key`, `setting_value`, `setting_type`, `category`, `description`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'site_name', 'शपथ टाइम्स हिंदी न्यूज पोर्टल', 'text', 'site', 'Website name', 1, '2025-06-25 04:32:43', '2025-06-25 05:14:54'),
(2, 'site_tagline', 'नवीनतम हिंदी समाचार पोर्टल', 'text', 'site', 'Website tagline', 1, '2025-06-25 04:32:43', '2025-06-25 05:14:54'),
(3, 'site_logo', 'logo_1750826628.png', 'image', 'site', 'Website logo', 1, '2025-06-25 04:32:43', '2025-06-25 04:43:48'),
(4, 'site_favicon', '', 'image', 'site', 'Website favicon', 1, '2025-06-25 04:32:43', '2025-06-25 04:32:43'),
(5, 'contact_email', '<EMAIL>', 'text', 'contact', 'Contact email address', 1, '2025-06-25 04:32:43', '2025-06-25 04:47:28'),
(6, 'contact_phone', '+91-9876543210', 'text', 'contact', 'Contact phone number', 1, '2025-06-25 04:32:43', '2025-06-25 04:47:28'),
(7, 'contact_address', 'New Delhi, India', 'textarea', 'contact', 'Contact address', 1, '2025-06-25 04:32:43', '2025-06-25 04:47:29'),
(8, 'contact_whatsapp', '+91-9876543210', 'text', 'contact', 'WhatsApp number', 1, '2025-06-25 04:32:43', '2025-06-25 04:47:29'),
(9, 'facebook_url', 'https://facebook.com/shapathtimes', 'text', 'social', 'Facebook page URL', 1, '2025-06-25 04:32:43', '2025-06-25 06:07:46'),
(10, 'twitter_url', '', 'text', 'social', 'Twitter profile URL', 1, '2025-06-25 04:32:43', '2025-06-25 06:07:46'),
(11, 'instagram_url', '', 'text', 'social', 'Instagram profile URL', 1, '2025-06-25 04:32:44', '2025-06-25 06:07:46'),
(12, 'youtube_url', 'https://www.youtube.com/@Shapathtimes', 'text', 'social', 'YouTube channel URL', 1, '2025-06-25 04:32:44', '2025-06-25 06:07:46'),
(13, 'linkedin_url', '', 'text', 'social', 'LinkedIn profile URL', 1, '2025-06-25 04:32:44', '2025-06-25 06:07:46'),
(14, 'telegram_url', '', 'text', 'social', 'Telegram channel URL', 1, '2025-06-25 04:32:44', '2025-06-25 06:07:46'),
(15, 'footer_copyright', '© 2025 Shapath Times News Portal. All rights reserved.', 'text', 'footer', 'Footer copyright text', 1, '2025-06-25 04:32:44', '2025-06-25 04:48:12'),
(16, 'footer_description', 'Your trusted source for latest Hindi news and updates.', 'textarea', 'footer', 'Footer description', 1, '2025-06-25 04:32:44', '2025-06-25 04:48:12'),
(17, 'google_analytics_id', '', 'text', 'analytics', 'Google Analytics tracking ID', 1, '2025-06-25 04:32:44', '2025-06-25 04:32:44'),
(18, 'google_tag_manager_id', '', 'text', 'analytics', 'Google Tag Manager ID', 1, '2025-06-25 04:32:44', '2025-06-25 04:32:44'),
(19, 'facebook_pixel_id', '', 'text', 'analytics', 'Facebook Pixel ID', 1, '2025-06-25 04:32:44', '2025-06-25 04:32:44'),
(20, 'meta_description', 'Latest Hindi news, breaking news, politics, sports, entertainment, technology and business news', 'textarea', 'seo', 'Default meta description', 1, '2025-06-25 04:32:44', '2025-06-25 04:32:44'),
(21, 'meta_keywords', 'Hindi news, breaking news, राजनीति, खेल, मनोरंजन, तकनीक', 'textarea', 'seo', 'Default meta keywords', 1, '2025-06-25 04:32:44', '2025-06-25 04:32:44');

-- --------------------------------------------------------

--
-- Table structure for table `tags`
--

CREATE TABLE `tags` (
  `id` int(11) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `tags`
--

INSERT INTO `tags` (`id`, `name`, `slug`, `status`, `created_at`, `updated_at`) VALUES
(1, 'शिक्षा', 'education', 'active', '2025-06-25 05:24:51', '2025-06-25 05:24:51');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) UNSIGNED NOT NULL,
  `username` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `profile_image` varchar(255) DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `role` enum('admin','manager') NOT NULL DEFAULT 'manager',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `email`, `phone`, `profile_image`, `password`, `full_name`, `role`, `status`, `created_at`, `updated_at`) VALUES
(1, 'admin', '<EMAIL>', '', NULL, '$2y$10$MIHHlulvGAvsJZXXKvT4nOk.SdC3HElFEeRJwD0J4VCUpogCZ/glG', 'Administrator', 'admin', 'active', '2025-06-24 07:33:04', '2025-06-25 04:46:35'),
(2, 'manager1', '<EMAIL>', NULL, NULL, '$2y$10$N9ri9tLEA7IOTanjM65oeuAYx.7uXhyGZPYM81f.3l0hjcCSQdNIy', 'News Manager', 'manager', 'active', '2025-06-24 07:33:04', '2025-06-24 07:33:04');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `ads`
--
ALTER TABLE `ads`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `breaking_news`
--
ALTER TABLE `breaking_news`
  ADD PRIMARY KEY (`id`),
  ADD KEY `breaking_news_created_by_foreign` (`created_by`),
  ADD KEY `status` (`status`),
  ADD KEY `priority` (`priority`),
  ADD KEY `start_time_end_time` (`start_time`,`end_time`);

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`);

--
-- Indexes for table `epapers`
--
ALTER TABLE `epapers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `epapers_uploaded_by_foreign` (`uploaded_by`),
  ADD KEY `publication_date` (`publication_date`),
  ADD KEY `status` (`status`),
  ADD KEY `featured` (`featured`),
  ADD KEY `publication_date_edition` (`publication_date`,`edition`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `news`
--
ALTER TABLE `news`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD KEY `news_category_id_foreign` (`category_id`),
  ADD KEY `news_author_id_foreign` (`author_id`);

--
-- Indexes for table `news_tags`
--
ALTER TABLE `news_tags`
  ADD PRIMARY KEY (`id`),
  ADD KEY `news_tags_news_id_foreign` (`news_id`),
  ADD KEY `news_tags_tag_id_foreign` (`tag_id`);

--
-- Indexes for table `polls`
--
ALTER TABLE `polls`
  ADD PRIMARY KEY (`id`),
  ADD KEY `polls_created_by_foreign` (`created_by`),
  ADD KEY `status` (`status`),
  ADD KEY `start_date_end_date` (`start_date`,`end_date`);

--
-- Indexes for table `poll_options`
--
ALTER TABLE `poll_options`
  ADD PRIMARY KEY (`id`),
  ADD KEY `poll_id` (`poll_id`),
  ADD KEY `sort_order` (`sort_order`);

--
-- Indexes for table `poll_votes`
--
ALTER TABLE `poll_votes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `poll_id` (`poll_id`),
  ADD KEY `option_id` (`option_id`),
  ADD KEY `voter_ip` (`voter_ip`),
  ADD KEY `voter_session` (`voter_session`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`),
  ADD KEY `category` (`category`);

--
-- Indexes for table `tags`
--
ALTER TABLE `tags`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `ads`
--
ALTER TABLE `ads`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `breaking_news`
--
ALTER TABLE `breaking_news`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `epapers`
--
ALTER TABLE `epapers`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `news`
--
ALTER TABLE `news`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `news_tags`
--
ALTER TABLE `news_tags`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `polls`
--
ALTER TABLE `polls`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `poll_options`
--
ALTER TABLE `poll_options`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `poll_votes`
--
ALTER TABLE `poll_votes`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `tags`
--
ALTER TABLE `tags`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `breaking_news`
--
ALTER TABLE `breaking_news`
  ADD CONSTRAINT `breaking_news_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `epapers`
--
ALTER TABLE `epapers`
  ADD CONSTRAINT `epapers_uploaded_by_foreign` FOREIGN KEY (`uploaded_by`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `news`
--
ALTER TABLE `news`
  ADD CONSTRAINT `news_author_id_foreign` FOREIGN KEY (`author_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `news_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `news_tags`
--
ALTER TABLE `news_tags`
  ADD CONSTRAINT `news_tags_news_id_foreign` FOREIGN KEY (`news_id`) REFERENCES `news` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `news_tags_tag_id_foreign` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `polls`
--
ALTER TABLE `polls`
  ADD CONSTRAINT `polls_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `poll_options`
--
ALTER TABLE `poll_options`
  ADD CONSTRAINT `poll_options_poll_id_foreign` FOREIGN KEY (`poll_id`) REFERENCES `polls` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `poll_votes`
--
ALTER TABLE `poll_votes`
  ADD CONSTRAINT `poll_votes_option_id_foreign` FOREIGN KEY (`option_id`) REFERENCES `poll_options` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `poll_votes_poll_id_foreign` FOREIGN KEY (`poll_id`) REFERENCES `polls` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
