<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\Epaper;
use App\Models\Category;
use App\Models\BreakingNews;
use App\Models\Ad;

class EpaperController extends BaseController
{
    protected $epaperModel;
    protected $categoryModel;
    protected $breakingNewsModel;
    protected $adModel;

    public function __construct()
    {
        $this->epaperModel = new Epaper();
        $this->categoryModel = new Category();
        $this->breakingNewsModel = new BreakingNews();
        $this->adModel = new Ad();
    }

    public function index()
    {
        $latestEpaper = $this->epaperModel->getLatestEpaper();
        $featuredEpapers = $this->epaperModel->getFeaturedEpapers(3);
        $recentEpapers = $this->epaperModel->getActiveEpapers(12);
        $archiveYears = $this->epaperModel->getArchiveYears();

        // Get site settings for dynamic title
        $siteSettings = getSiteSettings();

        $data = [
            'title' => 'E-Paper Archive - ' . $siteSettings['site_name'],
            'latestEpaper' => $latestEpaper,
            'featuredEpapers' => $featuredEpapers,
            'recentEpapers' => $recentEpapers,
            'archiveYears' => $archiveYears,
            'categories' => $this->categoryModel->where('status', 'active')->findAll(),
            'breakingNews' => $this->breakingNewsModel->getActiveBreakingNews(5),
            'headerAds' => $this->adModel->getActiveAdsByPosition('header'),
            'sidebarAds' => $this->adModel->getActiveAdsByPosition('sidebar'),
            'footerAds' => $this->adModel->getActiveAdsByPosition('footer'),
        ];

        return view('frontend/epaper/index', $data);
    }

    public function view($id)
    {
        $epaper = $this->epaperModel->find($id);

        if (!$epaper || $epaper['status'] !== 'active') {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Epaper not found');
        }

        // Increment view count
        $this->epaperModel->incrementViewCount($id);

        // Get related epapers
        $relatedEpapers = $this->epaperModel->getActiveEpapers(5);

        // Get site settings for dynamic title
        $siteSettings = getSiteSettings();

        $data = [
            'title' => $epaper['title'] . ' - E-Paper - ' . $siteSettings['site_name'],
            'epaper' => $epaper,
            'relatedEpapers' => $relatedEpapers,
            'categories' => $this->categoryModel->where('status', 'active')->findAll(),
            'breakingNews' => $this->breakingNewsModel->getActiveBreakingNews(5),
            'headerAds' => $this->adModel->getActiveAdsByPosition('header'),
            'sidebarAds' => $this->adModel->getActiveAdsByPosition('sidebar'),
            'footerAds' => $this->adModel->getActiveAdsByPosition('footer'),
        ];

        return view('frontend/epaper/view', $data);
    }

    public function download($id)
    {
        $epaper = $this->epaperModel->find($id);

        if (!$epaper || $epaper['status'] !== 'active') {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Epaper not found');
        }

        $filePath = FCPATH . 'uploads/epapers/' . $epaper['pdf_file'];

        if (!file_exists($filePath)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('PDF file not found');
        }

        // Increment download count
        $this->epaperModel->incrementDownloadCount($id);

        // Force download
        return $this->response->download($filePath, null);
    }

    public function archive($year = null, $month = null)
    {
        if (!$year) {
            $year = date('Y');
        }

        $archiveYears = $this->epaperModel->getArchiveYears();
        $archiveMonths = $this->epaperModel->getArchiveMonths($year);

        if ($month) {
            $epapers = $this->epaperModel->getEpapersByMonth($year, $month);
            $pageTitle = getMonthName($month) . ' ' . $year . ' - E-Paper Archive';
        } else {
            $epapers = $this->epaperModel->getEpapersByDateRange($year . '-01-01', $year . '-12-31');
            $pageTitle = $year . ' - E-Paper Archive';
        }

        // Get site settings for dynamic title
        $siteSettings = getSiteSettings();

        $data = [
            'title' => $pageTitle . ' - ' . $siteSettings['site_name'],
            'epapers' => $epapers,
            'currentYear' => $year,
            'currentMonth' => $month,
            'archiveYears' => $archiveYears,
            'archiveMonths' => $archiveMonths,
            'categories' => $this->categoryModel->where('status', 'active')->findAll(),
            'breakingNews' => $this->breakingNewsModel->getActiveBreakingNews(5),
            'headerAds' => $this->adModel->getActiveAdsByPosition('header'),
            'sidebarAds' => $this->adModel->getActiveAdsByPosition('sidebar'),
            'footerAds' => $this->adModel->getActiveAdsByPosition('footer'),
        ];

        return view('frontend/epaper/archive', $data);
    }

    public function search()
    {
        $keyword = $this->request->getGet('q');
        $epapers = [];

        if ($keyword) {
            $epapers = $this->epaperModel->searchEpapers($keyword, 20);
        }

        // Get site settings for dynamic title
        $siteSettings = getSiteSettings();

        $data = [
            'title' => 'Search E-Papers - ' . $siteSettings['site_name'],
            'keyword' => $keyword,
            'epapers' => $epapers,
            'categories' => $this->categoryModel->where('status', 'active')->findAll(),
            'breakingNews' => $this->breakingNewsModel->getActiveBreakingNews(5),
            'headerAds' => $this->adModel->getActiveAdsByPosition('header'),
            'sidebarAds' => $this->adModel->getActiveAdsByPosition('sidebar'),
            'footerAds' => $this->adModel->getActiveAdsByPosition('footer'),
        ];

        return view('frontend/epaper/search', $data);
    }
}
