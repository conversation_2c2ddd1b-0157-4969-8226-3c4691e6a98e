{"url": "http://localhost/bbc_news/public/index.php/admin/polls/analytics", "method": "GET", "isAJAX": false, "startTime": **********.869144, "totalTime": 112.9, "totalMemory": "7.235", "segmentDuration": 20, "segmentCount": 6, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.873596, "duration": 0.*****************}, {"name": "Required Before Filters", "component": "Timer", "start": **********.911048, "duration": 0.002644062042236328}, {"name": "Routing", "component": "Timer", "start": **********.9137, "duration": 0.002022981643676758}, {"name": "Before Filters", "component": "Timer", "start": **********.91604, "duration": 0.*****************}, {"name": "Controller", "component": "Timer", "start": **********.932291, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.932294, "duration": 0.*****************}, {"name": "After Filters", "component": "Timer", "start": **********.981699, "duration": 9.059906005859375e-06}, {"name": "Required After Filters", "component": "Timer", "start": **********.981744, "duration": 0.00036787986755371094}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(5 total Queries, 5 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "14.43 ms", "sql": "<strong>SELECT</strong> `polls`.*, `users`.`full_name` as `created_by_name`\n<strong>FROM</strong> `polls`\n<strong>JOIN</strong> `users` <strong>ON</strong> `users`.`id` = `polls`.`created_by`\n<strong>ORDER</strong> <strong>BY</strong> `polls`.`created_at` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\Poll.php:76", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Admin\\Polls.php:179", "function": "        App\\Models\\Poll->getPollsWithDetails()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Admin\\Polls->analytics()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["D:\\xampp\\htdocs\\bbc_news\\public\\index.php"], "function": "        require_once()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\Poll.php:76", "qid": "08c925c4fae36fd61ab387aee3175dfa"}, {"hover": "", "class": "", "duration": "0.31 ms", "sql": "<strong>SELECT</strong> DATE(voted_at) as vote_date, <strong>COUNT</strong>(*) as vote_count\n<strong>FROM</strong> `poll_votes`\n<strong>WHERE</strong> `voted_at` &gt;= &#039;2025-05-25&#039;\n<strong>GROUP</strong> <strong>BY</strong> DATE(voted_at)\n<strong>ORDER</strong> <strong>BY</strong> `vote_date` <strong>ASC</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Admin\\Polls.php:218", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Admin\\Polls->analytics()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["D:\\xampp\\htdocs\\bbc_news\\public\\index.php"], "function": "        require_once()", "index": " 11    "}], "trace-file": "APPPATH\\Controllers\\Admin\\Polls.php:218", "qid": "2da906f6f582232d452caea153af054b"}, {"hover": "", "class": "", "duration": "0.18 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `poll_options`\n<strong>WHERE</strong> `poll_id` = &#039;2&#039;\n<strong>ORDER</strong> <strong>BY</strong> `sort_order` <strong>ASC</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\PollOption.php:74", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Admin\\Polls.php:234", "function": "        App\\Models\\PollOption->getOptionsWithPercentages()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Admin\\Polls->analytics()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["D:\\xampp\\htdocs\\bbc_news\\public\\index.php"], "function": "        require_once()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\PollOption.php:74", "qid": "0f146a487ed3f728671529a673c16ef7"}, {"hover": "", "class": "", "duration": "0.16 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `poll_options`\n<strong>WHERE</strong> `poll_id` = &#039;1&#039;\n<strong>ORDER</strong> <strong>BY</strong> `sort_order` <strong>ASC</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\PollOption.php:74", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Admin\\Polls.php:234", "function": "        App\\Models\\PollOption->getOptionsWithPercentages()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Admin\\Polls->analytics()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["D:\\xampp\\htdocs\\bbc_news\\public\\index.php"], "function": "        require_once()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\PollOption.php:74", "qid": "00ec96b0a2a4bd14378ccbe0179cf328"}, {"hover": "", "class": "", "duration": "0.14 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `poll_options`\n<strong>WHERE</strong> `poll_id` = &#039;3&#039;\n<strong>ORDER</strong> <strong>BY</strong> `sort_order` <strong>ASC</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\PollOption.php:74", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Admin\\Polls.php:234", "function": "        App\\Models\\PollOption->getOptionsWithPercentages()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Admin\\Polls->analytics()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["D:\\xampp\\htdocs\\bbc_news\\public\\index.php"], "function": "        require_once()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\PollOption.php:74", "qid": "58824387e4e1c4e4eeb988d57cd67e6d"}]}, "badgeValue": 5, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.956055, "duration": "0.003386"}, {"name": "Query", "component": "Database", "start": **********.960141, "duration": "0.014432", "query": "<strong>SELECT</strong> `polls`.*, `users`.`full_name` as `created_by_name`\n<strong>FROM</strong> `polls`\n<strong>JOIN</strong> `users` <strong>ON</strong> `users`.`id` = `polls`.`created_by`\n<strong>ORDER</strong> <strong>BY</strong> `polls`.`created_at` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.976421, "duration": "0.000308", "query": "<strong>SELECT</strong> DATE(voted_at) as vote_date, <strong>COUNT</strong>(*) as vote_count\n<strong>FROM</strong> `poll_votes`\n<strong>WHERE</strong> `voted_at` &gt;= &#039;2025-05-25&#039;\n<strong>GROUP</strong> <strong>BY</strong> DATE(voted_at)\n<strong>ORDER</strong> <strong>BY</strong> `vote_date` <strong>ASC</strong>"}, {"name": "Query", "component": "Database", "start": **********.976871, "duration": "0.000184", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `poll_options`\n<strong>WHERE</strong> `poll_id` = &#039;2&#039;\n<strong>ORDER</strong> <strong>BY</strong> `sort_order` <strong>ASC</strong>"}, {"name": "Query", "component": "Database", "start": **********.977141, "duration": "0.000158", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `poll_options`\n<strong>WHERE</strong> `poll_id` = &#039;1&#039;\n<strong>ORDER</strong> <strong>BY</strong> `sort_order` <strong>ASC</strong>"}, {"name": "Query", "component": "Database", "start": **********.977391, "duration": "0.000136", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `poll_options`\n<strong>WHERE</strong> `poll_id` = &#039;3&#039;\n<strong>ORDER</strong> <strong>BY</strong> `sort_order` <strong>ASC</strong>"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "debug", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 2, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: admin/layout.php", "component": "Views", "start": **********.980704, "duration": 0.00078582763671875}, {"name": "View: admin/polls/analytics.php", "component": "Views", "start": **********.979517, "duration": 0.0020971298217773438}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 166 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH\\Database\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH\\Database\\Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\I18n\\Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Session\\Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH\\Session\\SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH\\Validation\\FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}, {"path": "SYSTEMPATH\\rewrite.php", "name": "rewrite.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Session.php", "name": "Session.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\Admin\\Polls.php", "name": "Polls.php"}, {"path": "APPPATH\\Controllers\\BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH\\Filters\\AuthFilter.php", "name": "AuthFilter.php"}, {"path": "APPPATH\\Helpers\\hindi_helper.php", "name": "hindi_helper.php"}, {"path": "APPPATH\\Models\\Poll.php", "name": "Poll.php"}, {"path": "APPPATH\\Models\\PollOption.php", "name": "PollOption.php"}, {"path": "APPPATH\\Models\\PollVote.php", "name": "PollVote.php"}, {"path": "APPPATH\\Views\\admin\\layout.php", "name": "layout.php"}, {"path": "APPPATH\\Views\\admin\\polls\\analytics.php", "name": "analytics.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}, {"path": "VENDORPATH\\autoload.php", "name": "autoload.php"}, {"path": "VENDORPATH\\composer\\ClassLoader.php", "name": "ClassLoader.php"}, {"path": "VENDORPATH\\composer\\InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "VENDORPATH\\composer\\autoload_real.php", "name": "autoload_real.php"}, {"path": "VENDORPATH\\composer\\autoload_static.php", "name": "autoload_static.php"}, {"path": "VENDORPATH\\composer\\installed.php", "name": "installed.php"}, {"path": "VENDORPATH\\composer\\platform_check.php", "name": "platform_check.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\Escaper.php", "name": "Escaper.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "VENDORPATH\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php", "name": "deep_copy.php"}, {"path": "VENDORPATH\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php", "name": "Functions.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LogLevel.php", "name": "LogLevel.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "VENDORPATH\\symfony\\deprecation-contracts\\function.php", "name": "function.php"}]}, "badgeValue": 166, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Admin\\Polls", "method": "analytics", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "news/(.*)", "handler": "\\App\\Controllers\\Home::news/$1"}, {"method": "GET", "route": "category/(.*)", "handler": "\\App\\Controllers\\Home::category/$1"}, {"method": "GET", "route": "poll/results/([0-9]+)", "handler": "\\App\\Controllers\\PollController::getResults/$1"}, {"method": "GET", "route": "admin/login", "handler": "\\App\\Controllers\\Auth::login"}, {"method": "GET", "route": "admin/logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "admin/dashboard", "handler": "\\App\\Controllers\\Admin\\Dashboard::index"}, {"method": "GET", "route": "admin/users", "handler": "\\App\\Controllers\\Admin\\Users::index"}, {"method": "GET", "route": "admin/users/create", "handler": "\\App\\Controllers\\Admin\\Users::create"}, {"method": "GET", "route": "admin/users/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Users::edit/$1"}, {"method": "GET", "route": "admin/users/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Users::delete/$1"}, {"method": "GET", "route": "admin/news", "handler": "\\App\\Controllers\\Admin\\News::index"}, {"method": "GET", "route": "admin/news/create", "handler": "\\App\\Controllers\\Admin\\News::create"}, {"method": "GET", "route": "admin/news/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\News::edit/$1"}, {"method": "GET", "route": "admin/news/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\News::delete/$1"}, {"method": "GET", "route": "admin/categories", "handler": "\\App\\Controllers\\Admin\\Categories::index"}, {"method": "GET", "route": "admin/categories/create", "handler": "\\App\\Controllers\\Admin\\Categories::create"}, {"method": "GET", "route": "admin/categories/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Categories::edit/$1"}, {"method": "GET", "route": "admin/categories/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Categories::delete/$1"}, {"method": "GET", "route": "admin/tags", "handler": "\\App\\Controllers\\Admin\\Tags::index"}, {"method": "GET", "route": "admin/tags/create", "handler": "\\App\\Controllers\\Admin\\Tags::create"}, {"method": "GET", "route": "admin/tags/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Tags::edit/$1"}, {"method": "GET", "route": "admin/tags/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Tags::delete/$1"}, {"method": "GET", "route": "admin/ads", "handler": "\\App\\Controllers\\Admin\\Ads::index"}, {"method": "GET", "route": "admin/ads/analytics", "handler": "\\App\\Controllers\\Admin\\Ads::analytics"}, {"method": "GET", "route": "admin/ads/create", "handler": "\\App\\Controllers\\Admin\\Ads::create"}, {"method": "GET", "route": "admin/ads/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Ads::edit/$1"}, {"method": "GET", "route": "admin/ads/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Ads::delete/$1"}, {"method": "GET", "route": "admin/breaking-news", "handler": "\\App\\Controllers\\Admin\\BreakingNews::index"}, {"method": "GET", "route": "admin/breaking-news/create", "handler": "\\App\\Controllers\\Admin\\BreakingNews::create"}, {"method": "GET", "route": "admin/breaking-news/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BreakingNews::edit/$1"}, {"method": "GET", "route": "admin/breaking-news/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BreakingNews::delete/$1"}, {"method": "GET", "route": "admin/polls", "handler": "\\App\\Controllers\\Admin\\Polls::index"}, {"method": "GET", "route": "admin/polls/analytics", "handler": "\\App\\Controllers\\Admin\\Polls::analytics"}, {"method": "GET", "route": "admin/polls/results/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Polls::results/$1"}, {"method": "GET", "route": "admin/polls/create", "handler": "\\App\\Controllers\\Admin\\Polls::create"}, {"method": "GET", "route": "admin/polls/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Polls::edit/$1"}, {"method": "GET", "route": "admin/polls/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Polls::delete/$1"}, {"method": "POST", "route": "poll/vote", "handler": "\\App\\Controllers\\PollController::vote"}, {"method": "POST", "route": "api/track-ad-click/([0-9]+)", "handler": "\\App\\Controllers\\Api\\AdTracking::trackClick/$1"}, {"method": "POST", "route": "api/track-ad-impression/([0-9]+)", "handler": "\\App\\Controllers\\Api\\AdTracking::trackImpression/$1"}, {"method": "POST", "route": "admin/authenticate", "handler": "\\App\\Controllers\\Auth::authenticate"}, {"method": "POST", "route": "admin/users/store", "handler": "\\App\\Controllers\\Admin\\Users::store"}, {"method": "POST", "route": "admin/users/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Users::update/$1"}, {"method": "POST", "route": "admin/news/store", "handler": "\\App\\Controllers\\Admin\\News::store"}, {"method": "POST", "route": "admin/news/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\News::update/$1"}, {"method": "POST", "route": "admin/categories/store", "handler": "\\App\\Controllers\\Admin\\Categories::store"}, {"method": "POST", "route": "admin/categories/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Categories::update/$1"}, {"method": "POST", "route": "admin/tags/store", "handler": "\\App\\Controllers\\Admin\\Tags::store"}, {"method": "POST", "route": "admin/tags/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Tags::update/$1"}, {"method": "POST", "route": "admin/ads/store", "handler": "\\App\\Controllers\\Admin\\Ads::store"}, {"method": "POST", "route": "admin/ads/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Ads::update/$1"}, {"method": "POST", "route": "admin/breaking-news/store", "handler": "\\App\\Controllers\\Admin\\BreakingNews::store"}, {"method": "POST", "route": "admin/breaking-news/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BreakingNews::update/$1"}, {"method": "POST", "route": "admin/breaking-news/toggle-status/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BreakingNews::toggleStatus/$1"}, {"method": "POST", "route": "admin/polls/store", "handler": "\\App\\Controllers\\Admin\\Polls::store"}, {"method": "POST", "route": "admin/polls/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\Polls::update/$1"}]}, "badgeValue": 39, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "15.12", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "0.09", "count": 5}}}, "badgeValue": 6, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.895923, "duration": 0.015118122100830078}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.974584, "duration": 4.291534423828125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.976733, "duration": 1.9073486328125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.977056, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.977302, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.977529, "duration": 7.867813110351562e-06}]}], "vars": {"varData": {"View Data": {"title": "Poll Analytics", "totalPolls": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>integer</var> 3</dt></dl></div>", "activePolls": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>integer</var> 3</dt></dl></div>", "totalVotes": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>integer</var> 400</dt></dl></div>", "averageVotesPerPoll": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>double</var> 133.3</dt></dl></div>", "statusStats": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (1)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>active</dfn> =&gt; <var>integer</var> 3<div class=\"access-path\">$value['active']</div></dt></dl></dd></dl></div>", "typeStats": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (2)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>Single Choice</dfn> =&gt; <var>integer</var> 2<div class=\"access-path\">$value['Single Choice']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>Multiple Choice</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value['Multiple Choice']</div></dt></dl></dd></dl></div>", "topPolls": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (3)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (3)</li><li>Contents (3)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>title</th><th>description</th><th>status</th><th>multiple_choice</th><th>show_results</th><th>start_date</th><th>end_date</th><th>total_votes</th><th>created_by</th><th>created_at</th><th>updated_at</th><th>created_by_name</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">2</td><td title=\"UTF-8 string (117)\">&#2310;&#2346;&#2325;&#2379; &#2325;&#2380;&#2344; &#2360;&#2375; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352; &#2357;&#2367;&#2359;&#2351; &#2360;&#2348;&#2360;&#2375; &#2332;&#2381;&#2351;&#2366;&#2342;&#2366; &#2346;&#2360;&#2306;&#2342; &#2361;&#2376;&#2306;?</td><td title=\"UTF-8 string (191)\">&#2361;&#2350;&#2366;&#2352;&#2368; &#2357;&#2375;&#2348;&#2360;&#2366;&#2311;&#2335; &#2346;&#2352; &#2310;&#2346; &#2325;&#2367;&#2360; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2375; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352; &#2360;&#2348;&#2360;&#2375; &#2332;&#2381;&#2351;&#2366;&#2342;&#2366; &#2346;&#2338;&#2364;&#2344;&#2366; &#2346;&#2360;&#2306;&#2342; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;?</td><td title=\"string (6)\">active</td><td title=\"string (1)\">1</td><td title=\"string (10)\">after_vote</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (3)\">150</td><td title=\"string (1)\">1</td><td title=\"string (19)\">2025-06-24 09:45:20</td><td title=\"string (19)\">2025-06-24 09:45:20</td><td title=\"string (13)\">Administrator</td></tr><tr><th>1</th><td title=\"string (1)\">1</td><td title=\"UTF-8 string (85)\">&#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2368; &#2360;&#2350;&#2360;&#2381;&#2351;&#2366; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</td><td title=\"UTF-8 string (153)\">&#2310;&#2346;&#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352; &#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2368; &#2360;&#2350;&#2360;&#2381;&#2351;&#2366; &#2325;&#2380;&#2344; &#2360;&#2368; &#2361;&#2376;?</td><td title=\"string (6)\">active</td><td title=\"string (1)\">0</td><td title=\"string (10)\">after_vote</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (3)\">125</td><td title=\"string (1)\">1</td><td title=\"string (19)\">2025-06-24 09:45:20</td><td title=\"string (19)\">2025-06-24 09:45:20</td><td title=\"string (13)\">Administrator</td></tr><tr><th>2</th><td title=\"string (1)\">3</td><td title=\"UTF-8 string (84)\">&#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2367;&#2340;&#2344;&#2366; &#2360;&#2347;&#2354; &#2361;&#2376;?</td><td title=\"UTF-8 string (146)\">&#2310;&#2346;&#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2368; &#2360;&#2347;&#2354;&#2340;&#2366; &#2325;&#2366; &#2360;&#2381;&#2340;&#2352; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</td><td title=\"string (6)\">active</td><td title=\"string (1)\">0</td><td title=\"string (6)\">always</td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-24 09:45:20</td><td title=\"string (3)\">125</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-06-24 09:45:20</td><td title=\"string (19)\">2025-06-24 09:45:20</td><td title=\"string (12)\">News Manager</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (117) \"&#2310;&#2346;&#2325;&#2379; &#2325;&#2380;&#2344; &#2360;&#2375; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352; &#2357;&#2367;&#2359;&#2351; &#2360;&#2348;&#2360;&#2375; &#2332;&#2381;&#2351;&#2366;&#2342;&#2366; &#2346;&#2360;&#2306;&#2342; &#2361;&#2376;&#2306;?\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (191) \"&#2361;&#2350;&#2366;&#2352;&#2368; &#2357;&#2375;&#2348;&#2360;&#2366;&#2311;&#2335; &#2346;&#2352; &#2310;&#2346; &#2325;&#2367;&#2360; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2375; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352; &#2360;&#2348;&#2360;&#2375; &#2332;&#2381;&#2351;&#2366;&#2342;&#2366; &#2346;&#2338;&#2364;&#2344;&#2366; &#2346;&#2360;&#2306;&#2342; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;?\"<div class=\"access-path\">$value[0]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>multiple_choice</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['multiple_choice']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>show_results</dfn> =&gt; <var>string</var> (10) \"after_vote\"<div class=\"access-path\">$value[0]['show_results']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_votes</dfn> =&gt; <var>string</var> (3) \"150\"<div class=\"access-path\">$value[0]['total_votes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_by</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['created_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:20\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:20\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_by_name</dfn> =&gt; <var>string</var> (13) \"Administrator\"<div class=\"access-path\">$value[0]['created_by_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (85) \"&#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2368; &#2360;&#2350;&#2360;&#2381;&#2351;&#2366; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?\"<div class=\"access-path\">$value[1]['title']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (153) \"&#2310;&#2346;&#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352; &#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2368; &#2360;&#2350;&#2360;&#2381;&#2351;&#2366; &#2325;&#2380;&#2344; &#2360;&#2368; &#2361;&#2376;?\"<div class=\"access-path\">$value[1]['description']</div></dt><dd><pre>&#2310;&#2346;&#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352; &#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2368; &#2360;&#2350;&#2360;&#2381;&#2351;&#2366; &#2325;&#2380;&#2344; &#2360;&#2368; &#2361;&#2376;?\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>multiple_choice</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[1]['multiple_choice']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>show_results</dfn> =&gt; <var>string</var> (10) \"after_vote\"<div class=\"access-path\">$value[1]['show_results']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_votes</dfn> =&gt; <var>string</var> (3) \"125\"<div class=\"access-path\">$value[1]['total_votes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_by</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['created_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:20\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:20\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_by_name</dfn> =&gt; <var>string</var> (13) \"Administrator\"<div class=\"access-path\">$value[1]['created_by_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (84) \"&#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2367;&#2340;&#2344;&#2366; &#2360;&#2347;&#2354; &#2361;&#2376;?\"<div class=\"access-path\">$value[2]['title']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (146) \"&#2310;&#2346;&#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2368; &#2360;&#2347;&#2354;&#2340;&#2366; &#2325;&#2366; &#2360;&#2381;&#2340;&#2352; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?\"<div class=\"access-path\">$value[2]['description']</div></dt><dd><pre>&#2310;&#2346;&#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2368; &#2360;&#2347;&#2354;&#2340;&#2366; &#2325;&#2366; &#2360;&#2381;&#2340;&#2352; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>multiple_choice</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[2]['multiple_choice']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>show_results</dfn> =&gt; <var>string</var> (6) \"always\"<div class=\"access-path\">$value[2]['show_results']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>string</var> (19) \"2025-07-24 09:45:20\"<div class=\"access-path\">$value[2]['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_votes</dfn> =&gt; <var>string</var> (3) \"125\"<div class=\"access-path\">$value[2]['total_votes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_by</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[2]['created_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:20\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:20\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_by_name</dfn> =&gt; <var>string</var> (12) \"News Manager\"<div class=\"access-path\">$value[2]['created_by_name']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "recentVotes": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>array</var> (0)</dt></dl></div>", "engagementData": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2025-06</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value['2025-06']</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>polls</dfn> =&gt; <var>integer</var> 3<div class=\"access-path\">$value['2025-06']['polls']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>votes</dfn> =&gt; <var>integer</var> 400<div class=\"access-path\">$value['2025-06']['votes']</div></dt></dl></dd></dl></dd></dl></div>", "pollAnalytics": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (3)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (3)</li><li>Contents (3)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>poll</th><th>options</th></tr></thead><tbody><tr><th>0</th><td title=\"array (13)\"><var>array</var> (13)</td><td title=\"array (6)\"><var>array</var> (6)</td></tr><tr><th>1</th><td title=\"array (13)\"><var>array</var> (13)</td><td title=\"array (5)\"><var>array</var> (5)</td></tr><tr><th>2</th><td title=\"array (13)\"><var>array</var> (13)</td><td title=\"array (5)\"><var>array</var> (5)</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>poll</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[0]['poll']</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['poll']['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (117) \"&#2310;&#2346;&#2325;&#2379; &#2325;&#2380;&#2344; &#2360;&#2375; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352; &#2357;&#2367;&#2359;&#2351; &#2360;&#2348;&#2360;&#2375; &#2332;&#2381;&#2351;&#2366;&#2342;&#2366; &#2346;&#2360;&#2306;&#2342; &#2361;&#2376;&#2306;?\"<div class=\"access-path\">$value[0]['poll']['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (191) \"&#2361;&#2350;&#2366;&#2352;&#2368; &#2357;&#2375;&#2348;&#2360;&#2366;&#2311;&#2335; &#2346;&#2352; &#2310;&#2346; &#2325;&#2367;&#2360; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2375; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352; &#2360;&#2348;&#2360;&#2375; &#2332;&#2381;&#2351;&#2366;&#2342;&#2366; &#2346;&#2338;&#2364;&#2344;&#2366; &#2346;&#2360;&#2306;&#2342; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;?\"<div class=\"access-path\">$value[0]['poll']['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['poll']['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>multiple_choice</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['poll']['multiple_choice']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>show_results</dfn> =&gt; <var>string</var> (10) \"after_vote\"<div class=\"access-path\">$value[0]['poll']['show_results']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['poll']['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['poll']['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_votes</dfn> =&gt; <var>string</var> (3) \"150\"<div class=\"access-path\">$value[0]['poll']['total_votes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_by</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['poll']['created_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:20\"<div class=\"access-path\">$value[0]['poll']['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:20\"<div class=\"access-path\">$value[0]['poll']['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_by_name</dfn> =&gt; <var>string</var> (13) \"Administrator\"<div class=\"access-path\">$value[0]['poll']['created_by_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>options</dfn> =&gt; <var>array</var> (6)<div class=\"access-path\">$value[0]['options']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (6)</li><li>Contents (6)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>poll_id</th><th>option_text</th><th>vote_count</th><th>sort_order</th><th>created_at</th><th>updated_at</th><th>percentage</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">6</td><td title=\"string (1)\">2</td><td title=\"UTF-8 string (21)\">&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;</td><td title=\"string (2)\">16</td><td title=\"string (1)\">1</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"double\">14.8</td></tr><tr><th>1</th><td title=\"string (1)\">7</td><td title=\"string (1)\">2</td><td title=\"UTF-8 string (9)\">&#2326;&#2375;&#2354;</td><td title=\"string (2)\">40</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"double\">37</td></tr><tr><th>2</th><td title=\"string (1)\">8</td><td title=\"string (1)\">2</td><td title=\"UTF-8 string (21)\">&#2350;&#2344;&#2379;&#2352;&#2306;&#2332;&#2344;</td><td title=\"string (1)\">4</td><td title=\"string (1)\">3</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"double\">3.7</td></tr><tr><th>3</th><td title=\"string (1)\">9</td><td title=\"string (1)\">2</td><td title=\"UTF-8 string (15)\">&#2340;&#2325;&#2344;&#2368;&#2325;</td><td title=\"string (2)\">12</td><td title=\"string (1)\">4</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"double\">11.1</td></tr><tr><th>4</th><td title=\"string (2)\">10</td><td title=\"string (1)\">2</td><td title=\"UTF-8 string (21)\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;</td><td title=\"string (2)\">22</td><td title=\"string (1)\">5</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"double\">20.4</td></tr><tr><th>5</th><td title=\"string (2)\">11</td><td title=\"string (1)\">2</td><td title=\"UTF-8 string (61)\">&#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352;</td><td title=\"string (2)\">14</td><td title=\"string (1)\">6</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"double\">13</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (8)<div class=\"access-path\">$value[0]['options'][0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[0]['options'][0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>poll_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['options'][0]['poll_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>option_text</dfn> =&gt; <var>UTF-8 string</var> (21) \"&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;\"<div class=\"access-path\">$value[0]['options'][0]['option_text']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>vote_count</dfn> =&gt; <var>string</var> (2) \"16\"<div class=\"access-path\">$value[0]['options'][0]['vote_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['options'][0]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[0]['options'][0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[0]['options'][0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>percentage</dfn> =&gt; <var>double</var> 14.8<div class=\"access-path\">$value[0]['options'][0]['percentage']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (8)<div class=\"access-path\">$value[0]['options'][1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[0]['options'][1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>poll_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['options'][1]['poll_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>option_text</dfn> =&gt; <var>UTF-8 string</var> (9) \"&#2326;&#2375;&#2354;\"<div class=\"access-path\">$value[0]['options'][1]['option_text']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>vote_count</dfn> =&gt; <var>string</var> (2) \"40\"<div class=\"access-path\">$value[0]['options'][1]['vote_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['options'][1]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[0]['options'][1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[0]['options'][1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>percentage</dfn> =&gt; <var>double</var> 37<div class=\"access-path\">$value[0]['options'][1]['percentage']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (8)<div class=\"access-path\">$value[0]['options'][2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"8\"<div class=\"access-path\">$value[0]['options'][2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>poll_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['options'][2]['poll_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>option_text</dfn> =&gt; <var>UTF-8 string</var> (21) \"&#2350;&#2344;&#2379;&#2352;&#2306;&#2332;&#2344;\"<div class=\"access-path\">$value[0]['options'][2]['option_text']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>vote_count</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[0]['options'][2]['vote_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[0]['options'][2]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[0]['options'][2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[0]['options'][2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>percentage</dfn> =&gt; <var>double</var> 3.7<div class=\"access-path\">$value[0]['options'][2]['percentage']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (8)<div class=\"access-path\">$value[0]['options'][3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"9\"<div class=\"access-path\">$value[0]['options'][3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>poll_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['options'][3]['poll_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>option_text</dfn> =&gt; <var>UTF-8 string</var> (15) \"&#2340;&#2325;&#2344;&#2368;&#2325;\"<div class=\"access-path\">$value[0]['options'][3]['option_text']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>vote_count</dfn> =&gt; <var>string</var> (2) \"12\"<div class=\"access-path\">$value[0]['options'][3]['vote_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[0]['options'][3]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[0]['options'][3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[0]['options'][3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>percentage</dfn> =&gt; <var>double</var> 11.1<div class=\"access-path\">$value[0]['options'][3]['percentage']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (8)<div class=\"access-path\">$value[0]['options'][4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"10\"<div class=\"access-path\">$value[0]['options'][4]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>poll_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['options'][4]['poll_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>option_text</dfn> =&gt; <var>UTF-8 string</var> (21) \"&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;\"<div class=\"access-path\">$value[0]['options'][4]['option_text']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>vote_count</dfn> =&gt; <var>string</var> (2) \"22\"<div class=\"access-path\">$value[0]['options'][4]['vote_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[0]['options'][4]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[0]['options'][4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[0]['options'][4]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>percentage</dfn> =&gt; <var>double</var> 20.4<div class=\"access-path\">$value[0]['options'][4]['percentage']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>5</dfn> =&gt; <var>array</var> (8)<div class=\"access-path\">$value[0]['options'][5]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"11\"<div class=\"access-path\">$value[0]['options'][5]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>poll_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['options'][5]['poll_id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>option_text</dfn> =&gt; <var>UTF-8 string</var> (61) \"&#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352;\"<div class=\"access-path\">$value[0]['options'][5]['option_text']</div></dt><dd><pre>&#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2360;&#2350;&#2366;&#2330;&#2366;&#2352;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>vote_count</dfn> =&gt; <var>string</var> (2) \"14\"<div class=\"access-path\">$value[0]['options'][5]['vote_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[0]['options'][5]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[0]['options'][5]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[0]['options'][5]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>percentage</dfn> =&gt; <var>double</var> 13<div class=\"access-path\">$value[0]['options'][5]['percentage']</div></dt></dl></dd></dl></li></ul></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>poll</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[1]['poll']</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['poll']['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (85) \"&#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2368; &#2360;&#2350;&#2360;&#2381;&#2351;&#2366; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?\"<div class=\"access-path\">$value[1]['poll']['title']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (153) \"&#2310;&#2346;&#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352; &#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2368; &#2360;&#2350;&#2360;&#2381;&#2351;&#2366; &#2325;&#2380;&#2344; &#2360;&#2368; &#2361;&#2376;?\"<div class=\"access-path\">$value[1]['poll']['description']</div></dt><dd><pre>&#2310;&#2346;&#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352; &#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2368; &#2360;&#2350;&#2360;&#2381;&#2351;&#2366; &#2325;&#2380;&#2344; &#2360;&#2368; &#2361;&#2376;?\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['poll']['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>multiple_choice</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[1]['poll']['multiple_choice']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>show_results</dfn> =&gt; <var>string</var> (10) \"after_vote\"<div class=\"access-path\">$value[1]['poll']['show_results']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['poll']['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['poll']['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_votes</dfn> =&gt; <var>string</var> (3) \"125\"<div class=\"access-path\">$value[1]['poll']['total_votes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_by</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['poll']['created_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:20\"<div class=\"access-path\">$value[1]['poll']['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:20\"<div class=\"access-path\">$value[1]['poll']['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_by_name</dfn> =&gt; <var>string</var> (13) \"Administrator\"<div class=\"access-path\">$value[1]['poll']['created_by_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>options</dfn> =&gt; <var>array</var> (5)<div class=\"access-path\">$value[1]['options']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (5)</li><li>Contents (5)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>poll_id</th><th>option_text</th><th>vote_count</th><th>sort_order</th><th>created_at</th><th>updated_at</th><th>percentage</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"UTF-8 string (27)\">&#2348;&#2375;&#2352;&#2379;&#2332;&#2327;&#2366;&#2352;&#2368;</td><td title=\"string (2)\">15</td><td title=\"string (1)\">1</td><td title=\"string (19)\">2025-06-24 09:45:20</td><td title=\"string (19)\">2025-06-24 09:45:20</td><td title=\"double\">11.8</td></tr><tr><th>1</th><td title=\"string (1)\">2</td><td title=\"string (1)\">1</td><td title=\"UTF-8 string (30)\">&#2349;&#2381;&#2352;&#2359;&#2381;&#2335;&#2366;&#2330;&#2366;&#2352;</td><td title=\"string (2)\">44</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-06-24 09:45:20</td><td title=\"string (19)\">2025-06-24 09:45:20</td><td title=\"double\">34.6</td></tr><tr><th>2</th><td title=\"string (1)\">3</td><td title=\"string (1)\">1</td><td title=\"UTF-8 string (15)\">&#2327;&#2352;&#2368;&#2348;&#2368;</td><td title=\"string (1)\">4</td><td title=\"string (1)\">3</td><td title=\"string (19)\">2025-06-24 09:45:20</td><td title=\"string (19)\">2025-06-24 09:45:20</td><td title=\"double\">3.1</td></tr><tr><th>3</th><td title=\"string (1)\">4</td><td title=\"string (1)\">1</td><td title=\"UTF-8 string (35)\">&#2358;&#2367;&#2325;&#2381;&#2359;&#2366; &#2325;&#2368; &#2325;&#2350;&#2368;</td><td title=\"string (2)\">48</td><td title=\"string (1)\">4</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"double\">37.8</td></tr><tr><th>4</th><td title=\"string (1)\">5</td><td title=\"string (1)\">1</td><td title=\"UTF-8 string (63)\">&#2360;&#2381;&#2357;&#2366;&#2360;&#2381;&#2341;&#2381;&#2351; &#2360;&#2375;&#2357;&#2366;&#2323;&#2306; &#2325;&#2368; &#2325;&#2350;&#2368;</td><td title=\"string (2)\">16</td><td title=\"string (1)\">5</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"double\">12.6</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (8)<div class=\"access-path\">$value[1]['options'][0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['options'][0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>poll_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['options'][0]['poll_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>option_text</dfn> =&gt; <var>UTF-8 string</var> (27) \"&#2348;&#2375;&#2352;&#2379;&#2332;&#2327;&#2366;&#2352;&#2368;\"<div class=\"access-path\">$value[1]['options'][0]['option_text']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>vote_count</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[1]['options'][0]['vote_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['options'][0]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:20\"<div class=\"access-path\">$value[1]['options'][0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:20\"<div class=\"access-path\">$value[1]['options'][0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>percentage</dfn> =&gt; <var>double</var> 11.8<div class=\"access-path\">$value[1]['options'][0]['percentage']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (8)<div class=\"access-path\">$value[1]['options'][1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['options'][1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>poll_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['options'][1]['poll_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>option_text</dfn> =&gt; <var>UTF-8 string</var> (30) \"&#2349;&#2381;&#2352;&#2359;&#2381;&#2335;&#2366;&#2330;&#2366;&#2352;\"<div class=\"access-path\">$value[1]['options'][1]['option_text']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>vote_count</dfn> =&gt; <var>string</var> (2) \"44\"<div class=\"access-path\">$value[1]['options'][1]['vote_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['options'][1]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:20\"<div class=\"access-path\">$value[1]['options'][1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:20\"<div class=\"access-path\">$value[1]['options'][1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>percentage</dfn> =&gt; <var>double</var> 34.6<div class=\"access-path\">$value[1]['options'][1]['percentage']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (8)<div class=\"access-path\">$value[1]['options'][2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[1]['options'][2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>poll_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['options'][2]['poll_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>option_text</dfn> =&gt; <var>UTF-8 string</var> (15) \"&#2327;&#2352;&#2368;&#2348;&#2368;\"<div class=\"access-path\">$value[1]['options'][2]['option_text']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>vote_count</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[1]['options'][2]['vote_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[1]['options'][2]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:20\"<div class=\"access-path\">$value[1]['options'][2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:20\"<div class=\"access-path\">$value[1]['options'][2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>percentage</dfn> =&gt; <var>double</var> 3.1<div class=\"access-path\">$value[1]['options'][2]['percentage']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (8)<div class=\"access-path\">$value[1]['options'][3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[1]['options'][3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>poll_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['options'][3]['poll_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>option_text</dfn> =&gt; <var>UTF-8 string</var> (35) \"&#2358;&#2367;&#2325;&#2381;&#2359;&#2366; &#2325;&#2368; &#2325;&#2350;&#2368;\"<div class=\"access-path\">$value[1]['options'][3]['option_text']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>vote_count</dfn> =&gt; <var>string</var> (2) \"48\"<div class=\"access-path\">$value[1]['options'][3]['vote_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[1]['options'][3]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[1]['options'][3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[1]['options'][3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>percentage</dfn> =&gt; <var>double</var> 37.8<div class=\"access-path\">$value[1]['options'][3]['percentage']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (8)<div class=\"access-path\">$value[1]['options'][4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[1]['options'][4]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>poll_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['options'][4]['poll_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>option_text</dfn> =&gt; <var>UTF-8 string</var> (63) \"&#2360;&#2381;&#2357;&#2366;&#2360;&#2381;&#2341;&#2381;&#2351; &#2360;&#2375;&#2357;&#2366;&#2323;&#2306; &#2325;&#2368; &#2325;&#2350;&#2368;\"<div class=\"access-path\">$value[1]['options'][4]['option_text']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>vote_count</dfn> =&gt; <var>string</var> (2) \"16\"<div class=\"access-path\">$value[1]['options'][4]['vote_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[1]['options'][4]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[1]['options'][4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[1]['options'][4]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>percentage</dfn> =&gt; <var>double</var> 12.6<div class=\"access-path\">$value[1]['options'][4]['percentage']</div></dt></dl></dd></dl></li></ul></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (2)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>poll</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[2]['poll']</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['poll']['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>UTF-8 string</var> (84) \"&#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2367;&#2340;&#2344;&#2366; &#2360;&#2347;&#2354; &#2361;&#2376;?\"<div class=\"access-path\">$value[2]['poll']['title']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>description</dfn> =&gt; <var>UTF-8 string</var> (146) \"&#2310;&#2346;&#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2368; &#2360;&#2347;&#2354;&#2340;&#2366; &#2325;&#2366; &#2360;&#2381;&#2340;&#2352; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?\"<div class=\"access-path\">$value[2]['poll']['description']</div></dt><dd><pre>&#2310;&#2346;&#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2350;&#2367;&#2358;&#2344; &#2325;&#2368; &#2360;&#2347;&#2354;&#2340;&#2366; &#2325;&#2366; &#2360;&#2381;&#2340;&#2352; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['poll']['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>multiple_choice</dfn> =&gt; <var>string</var> (1) \"0\"<div class=\"access-path\">$value[2]['poll']['multiple_choice']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>show_results</dfn> =&gt; <var>string</var> (6) \"always\"<div class=\"access-path\">$value[2]['poll']['show_results']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>start_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['poll']['start_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>end_date</dfn> =&gt; <var>string</var> (19) \"2025-07-24 09:45:20\"<div class=\"access-path\">$value[2]['poll']['end_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_votes</dfn> =&gt; <var>string</var> (3) \"125\"<div class=\"access-path\">$value[2]['poll']['total_votes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_by</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[2]['poll']['created_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:20\"<div class=\"access-path\">$value[2]['poll']['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:20\"<div class=\"access-path\">$value[2]['poll']['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_by_name</dfn> =&gt; <var>string</var> (12) \"News Manager\"<div class=\"access-path\">$value[2]['poll']['created_by_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>options</dfn> =&gt; <var>array</var> (5)<div class=\"access-path\">$value[2]['options']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (5)</li><li>Contents (5)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>poll_id</th><th>option_text</th><th>vote_count</th><th>sort_order</th><th>created_at</th><th>updated_at</th><th>percentage</th></tr></thead><tbody><tr><th>0</th><td title=\"string (2)\">12</td><td title=\"string (1)\">3</td><td title=\"UTF-8 string (22)\">&#2348;&#2361;&#2369;&#2340; &#2360;&#2347;&#2354;</td><td title=\"string (2)\">13</td><td title=\"string (1)\">1</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"double\">7.6</td></tr><tr><th>1</th><td title=\"string (2)\">13</td><td title=\"string (1)\">3</td><td title=\"UTF-8 string (9)\">&#2360;&#2347;&#2354;</td><td title=\"string (2)\">44</td><td title=\"string (1)\">2</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"double\">25.7</td></tr><tr><th>2</th><td title=\"string (2)\">14</td><td title=\"string (1)\">3</td><td title=\"UTF-8 string (9)\">&#2324;&#2360;&#2340;</td><td title=\"string (2)\">24</td><td title=\"string (1)\">3</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"double\">14</td></tr><tr><th>3</th><td title=\"string (2)\">15</td><td title=\"string (1)\">3</td><td title=\"UTF-8 string (12)\">&#2309;&#2360;&#2347;&#2354;</td><td title=\"string (2)\">50</td><td title=\"string (1)\">4</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"double\">29.2</td></tr><tr><th>4</th><td title=\"string (2)\">16</td><td title=\"string (1)\">3</td><td title=\"UTF-8 string (25)\">&#2348;&#2361;&#2369;&#2340; &#2309;&#2360;&#2347;&#2354;</td><td title=\"string (2)\">40</td><td title=\"string (1)\">5</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"string (19)\">2025-06-24 09:45:21</td><td title=\"double\">23.4</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (8)<div class=\"access-path\">$value[2]['options'][0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"12\"<div class=\"access-path\">$value[2]['options'][0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>poll_id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['options'][0]['poll_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>option_text</dfn> =&gt; <var>UTF-8 string</var> (22) \"&#2348;&#2361;&#2369;&#2340; &#2360;&#2347;&#2354;\"<div class=\"access-path\">$value[2]['options'][0]['option_text']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>vote_count</dfn> =&gt; <var>string</var> (2) \"13\"<div class=\"access-path\">$value[2]['options'][0]['vote_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['options'][0]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[2]['options'][0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[2]['options'][0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>percentage</dfn> =&gt; <var>double</var> 7.6<div class=\"access-path\">$value[2]['options'][0]['percentage']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (8)<div class=\"access-path\">$value[2]['options'][1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"13\"<div class=\"access-path\">$value[2]['options'][1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>poll_id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['options'][1]['poll_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>option_text</dfn> =&gt; <var>UTF-8 string</var> (9) \"&#2360;&#2347;&#2354;\"<div class=\"access-path\">$value[2]['options'][1]['option_text']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>vote_count</dfn> =&gt; <var>string</var> (2) \"44\"<div class=\"access-path\">$value[2]['options'][1]['vote_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[2]['options'][1]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[2]['options'][1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[2]['options'][1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>percentage</dfn> =&gt; <var>double</var> 25.7<div class=\"access-path\">$value[2]['options'][1]['percentage']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (8)<div class=\"access-path\">$value[2]['options'][2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"14\"<div class=\"access-path\">$value[2]['options'][2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>poll_id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['options'][2]['poll_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>option_text</dfn> =&gt; <var>UTF-8 string</var> (9) \"&#2324;&#2360;&#2340;\"<div class=\"access-path\">$value[2]['options'][2]['option_text']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>vote_count</dfn> =&gt; <var>string</var> (2) \"24\"<div class=\"access-path\">$value[2]['options'][2]['vote_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['options'][2]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[2]['options'][2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[2]['options'][2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>percentage</dfn> =&gt; <var>double</var> 14<div class=\"access-path\">$value[2]['options'][2]['percentage']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (8)<div class=\"access-path\">$value[2]['options'][3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[2]['options'][3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>poll_id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['options'][3]['poll_id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>option_text</dfn> =&gt; <var>UTF-8 string</var> (12) \"&#2309;&#2360;&#2347;&#2354;\"<div class=\"access-path\">$value[2]['options'][3]['option_text']</div></dt><dd><pre>&#2309;&#2360;&#2347;&#2354;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>vote_count</dfn> =&gt; <var>string</var> (2) \"50\"<div class=\"access-path\">$value[2]['options'][3]['vote_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[2]['options'][3]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[2]['options'][3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[2]['options'][3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>percentage</dfn> =&gt; <var>double</var> 29.2<div class=\"access-path\">$value[2]['options'][3]['percentage']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (8)<div class=\"access-path\">$value[2]['options'][4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"16\"<div class=\"access-path\">$value[2]['options'][4]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>poll_id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['options'][4]['poll_id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>option_text</dfn> =&gt; <var>UTF-8 string</var> (25) \"&#2348;&#2361;&#2369;&#2340; &#2309;&#2360;&#2347;&#2354;\"<div class=\"access-path\">$value[2]['options'][4]['option_text']</div></dt><dd><pre>&#2348;&#2361;&#2369;&#2340; &#2309;&#2360;&#2347;&#2354;\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>vote_count</dfn> =&gt; <var>string</var> (2) \"40\"<div class=\"access-path\">$value[2]['options'][4]['vote_count']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[2]['options'][4]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[2]['options'][4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-24 09:45:21\"<div class=\"access-path\">$value[2]['options'][4]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>percentage</dfn> =&gt; <var>double</var> 23.4<div class=\"access-path\">$value[2]['options'][4]['percentage']</div></dt></dl></dd></dl></li></ul></dd></dl></dd></dl></li></ul></dd></dl></div>"}}, "session": {"__ci_last_regenerate": "<pre>**********</pre>", "_ci_previous_url": "http://localhost/bbc_news/public/index.php/admin/polls/analytics", "user_id": "1", "username": "admin", "email": "<EMAIL>", "full_name": "Administrator", "role": "admin", "isLoggedIn": "<pre>1</pre>", "__ci_vars": "<pre>Array\n(\n)\n</pre>"}, "headers": {"Host": "localhost:8080", "Connection": "keep-alive", "Sec-Ch-Ua": "&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Sec-Fetch-Site": "none", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "en-US,en;q=0.9,eu;q=0.8,hi;q=0.7,bho;q=0.6,ne;q=0.5,nl;q=0.4,eo;q=0.3,haw;q=0.2", "Cookie": "csrf_cookie_name=d68cc39e23f3ee587320962fafa53a84; ci_session=be9cfbc2fc9ec9a35787cd2cab7a11e1"}, "cookies": {"csrf_cookie_name": "d68cc39e23f3ee587320962fafa53a84", "ci_session": "be9cfbc2fc9ec9a35787cd2cab7a11e1"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.12", "phpSAPI": "cli-server", "environment": "development", "baseURL": "http://localhost/bbc_news/public/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}